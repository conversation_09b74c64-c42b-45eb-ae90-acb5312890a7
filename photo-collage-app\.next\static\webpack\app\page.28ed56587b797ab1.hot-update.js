"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/CollageRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageRenderer = (param)=>{\n    let { template, uploadedPhotos, placedPhotos, onDownload } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isRendering, setIsRendering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewDataUrl, setPreviewDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const getPlacedPhoto = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const loadImage = (src)=>{\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            img.onload = ()=>resolve(img);\n            img.onerror = reject;\n            img.src = src;\n        });\n    };\n    const renderSlotToCanvas = async (ctx, slot, placedPhoto, uploadedPhoto)=>{\n        try {\n            const img = await loadImage(uploadedPhoto.url);\n            // Calculate slot dimensions in pixels\n            const slotX = slot.x / 100 * template.canvasWidth;\n            const slotY = slot.y / 100 * template.canvasHeight;\n            const slotWidth = slot.width / 100 * template.canvasWidth;\n            const slotHeight = slot.height / 100 * template.canvasHeight;\n            // Save canvas state\n            ctx.save();\n            // Create clipping path for the slot shape\n            ctx.beginPath();\n            if (slot.shape === \"circle\") {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                const radius = Math.min(slotWidth, slotHeight) / 2;\n                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\n            } else {\n                // Rectangle or custom shape\n                ctx.rect(slotX, slotY, slotWidth, slotHeight);\n            }\n            ctx.clip();\n            // Apply slot rotation if any\n            if (slot.rotation) {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(slot.rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Calculate image positioning and scaling\n            const scale = placedPhoto.scale || 1;\n            const rotation = placedPhoto.rotation || 0;\n            const posX = (placedPhoto.x || 50) / 100;\n            const posY = (placedPhoto.y || 50) / 100;\n            // Calculate scaled image dimensions\n            const scaledWidth = slotWidth * scale;\n            const scaledHeight = slotHeight * scale;\n            // Calculate position offset based on positioning\n            const offsetX = slotX + (slotWidth - scaledWidth) * posX;\n            const offsetY = slotY + (slotHeight - scaledHeight) * posY;\n            // Apply image rotation\n            if (rotation !== 0) {\n                const centerX = offsetX + scaledWidth / 2;\n                const centerY = offsetY + scaledHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Draw the image\n            ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);\n            // Restore canvas state\n            ctx.restore();\n        } catch (error) {\n            console.error(\"Error rendering slot:\", error);\n        }\n    };\n    const renderCollage = async ()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return null;\n        setIsRendering(true);\n        try {\n            const ctx = canvas.getContext(\"2d\");\n            if (!ctx) return null;\n            // Set canvas dimensions\n            canvas.width = template.canvasWidth;\n            canvas.height = template.canvasHeight;\n            // Clear canvas and set background\n            ctx.fillStyle = template.backgroundColor || \"#ffffff\";\n            ctx.fillRect(0, 0, template.canvasWidth, template.canvasHeight);\n            // Render each slot with its photo\n            for (const slot of template.slots){\n                const placedPhoto = getPlacedPhoto(slot.id);\n                if (placedPhoto) {\n                    const uploadedPhoto = getUploadedPhoto(placedPhoto.photoId);\n                    if (uploadedPhoto) {\n                        await renderSlotToCanvas(ctx, slot, placedPhoto, uploadedPhoto);\n                    }\n                }\n            }\n            // Get the data URL\n            const dataUrl = canvas.toDataURL(\"image/png\", 1.0);\n            setPreviewDataUrl(dataUrl);\n            return dataUrl;\n        } catch (error) {\n            console.error(\"Error rendering collage:\", error);\n            return null;\n        } finally{\n            setIsRendering(false);\n        }\n    };\n    const handleDownload = async ()=>{\n        const dataUrl = await renderCollage();\n        if (dataUrl) {\n            // Create download link\n            const link = document.createElement(\"a\");\n            link.download = \"collage-\".concat(template.name.toLowerCase().replace(/\\s+/g, \"-\"), \"-\").concat(Date.now(), \".png\");\n            link.href = dataUrl;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            if (onDownload) {\n                onDownload(dataUrl);\n            }\n        }\n    };\n    const handlePreview = async ()=>{\n        await renderCollage();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Auto-render preview when component mounts or data changes\n        if (placedPhotos.length > 0) {\n            handlePreview();\n        }\n    }, [\n        template,\n        placedPhotos,\n        uploadedPhotos\n    ]);\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    const isComplete = filledSlots === totalSlots;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        children: \"Collage Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: isComplete ? \"Your collage is ready!\" : \"\".concat(filledSlots, \" of \").concat(totalSlots, \" slots filled\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                        ref: canvasRef,\n                                        className: \"border rounded-lg shadow-sm max-w-full h-auto\",\n                                        style: {\n                                            maxHeight: \"400px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isRendering && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm\",\n                                            children: \"Rendering...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handlePreview,\n                                    disabled: isRendering || placedPhotos.length === 0,\n                                    children: isRendering ? \"Rendering...\" : \"Refresh Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: isRendering || !isComplete,\n                                    children: \"Download Collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-sm text-gray-600\",\n                            children: [\n                                !isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add photos to all slots to enable download\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, undefined),\n                                isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600\",\n                                    children: \"✓ Ready to download!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Canvas Size: \",\n                                        template.canvasWidth,\n                                        \" \\xd7 \",\n                                        template.canvasHeight,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Format: PNG • Quality: High\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageRenderer, \"YielKjIkWuN+XikvhPlPJlx0t9U=\");\n_c = CollageRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageRenderer);\nvar _c;\n$RefreshReg$(_c, \"CollageRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageRenderer.tsx\n"));

/***/ })

});