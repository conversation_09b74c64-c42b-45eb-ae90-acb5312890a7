# Photo Collage Maker

A modern, responsive web application for creating beautiful photo collages using customizable templates. Built with Next.js, TypeScript, and TailwindCSS.

## Features

- **Template Selection**: Choose from 5 different collage templates including grids, letters, numbers, hearts, and geometric patterns
- **Drag & Drop Upload**: Easy photo upload with drag-and-drop support and file validation
- **Interactive Editor**: Drag photos into template slots with visual feedback
- **Photo Controls**: Adjust position, scale, and rotation of photos within slots
- **Canvas Rendering**: High-quality collage rendering using HTML5 Canvas
- **Download**: Export completed collages as PNG files
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Touch Support**: Mobile-friendly touch interactions

## Templates Available

1. **4x4 Grid** - Classic 16-photo grid layout
2. **Heart Shape** - Romantic heart-shaped arrangement
3. **Letter A** - Letter A shaped photo collage
4. **Number 1** - Number 1 shaped layout
5. **Circle Pattern** - Circular arrangement with center focus

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd photo-collage-app
```

2. Install dependencies:
```bash
yarn install
# or
npm install
```

3. Start the development server:
```bash
yarn dev
# or
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## Usage

1. **Choose a Template**: Browse the template gallery and select your preferred layout
2. **Upload Photos**: Drag and drop images or click to select files (supports JPG, PNG, WebP)
3. **Arrange Photos**: Drag photos from the library into template slots
4. **Edit Photos**: Click on placed photos to adjust position, scale, and rotation
5. **Preview & Download**: Preview your collage and download as a high-quality PNG

## Technical Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: TailwindCSS
- **UI Components**: Shadcn/ui
- **Canvas**: HTML5 Canvas API for rendering
- **File Handling**: Browser File API

## Project Structure

```
src/
├── app/                 # Next.js app router pages
├── components/          # React components
│   ├── ui/             # Reusable UI components
│   ├── CollageApp.tsx  # Main application component
│   ├── TemplateGallery.tsx
│   ├── ImageUpload.tsx
│   ├── CollageEditor.tsx
│   ├── PhotoControls.tsx
│   └── CollageRenderer.tsx
├── data/               # Template data
├── types/              # TypeScript type definitions
└── lib/                # Utility functions
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Future Enhancements

- Additional template shapes and layouts
- Text overlay support
- Background customization
- Social media sharing
- Batch processing
- Template creation tools
- Cloud storage integration

## Support

For issues and questions, please open an issue on the GitHub repository.
