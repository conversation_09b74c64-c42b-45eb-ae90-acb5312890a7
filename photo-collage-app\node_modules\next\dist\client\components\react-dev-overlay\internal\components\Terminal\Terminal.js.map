{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Terminal/Terminal.tsx"], "names": ["Terminal", "getFile", "lines", "contentFileName", "shift", "fileName", "line", "column", "split", "parsedLine", "Number", "parsedColumn", "hasLocation", "isNaN", "location", "undefined", "getImportTraceFiles", "some", "test", "files", "length", "includes", "file", "pop", "trim", "unshift", "getEditorLinks", "content", "importTraceFiles", "source", "join", "React", "useMemo", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "div", "data-nextjs-terminal", "EditorLink", "isSourceFile", "key", "pre", "map", "entry", "index", "span", "style", "color", "fg", "decoration", "fontWeight", "fontStyle", "HotlinkedText", "text", "importTraceFile"], "mappings": ";;;;+BAwDaA;;;eAAAA;;;;;gEAxDK;iEACK;+BACO;4BACH;AAI3B,SAASC,QAAQC,KAAe;IAC9B,MAAMC,kBAAkBD,MAAME,KAAK;IACnC,IAAI,CAACD,iBAAiB,OAAO;IAC7B,MAAM,CAACE,UAAUC,MAAMC,OAAO,GAAGJ,gBAAgBK,KAAK,CAAC,KAAK;IAE5D,MAAMC,aAAaC,OAAOJ;IAC1B,MAAMK,eAAeD,OAAOH;IAC5B,MAAMK,cAAc,CAACF,OAAOG,KAAK,CAACJ,eAAe,CAACC,OAAOG,KAAK,CAACF;IAE/D,OAAO;QACLN,UAAUO,cAAcP,WAAWF;QACnCW,UAAUF,cACN;YACEN,MAAMG;YACNF,QAAQI;QACV,IACAI;IACN;AACF;AAEA,SAASC,oBAAoBd,KAAe;IAC1C,IACEA,MAAMe,IAAI,CAAC,CAACX,OAAS,8BAA8BY,IAAI,CAACZ,UACxDJ,MAAMe,IAAI,CAAC,CAACX,OAAS,qCAAqCY,IAAI,CAACZ,QAC/D;QACA,iDAAiD;QACjD,MAAMa,QAAQ,EAAE;QAChB,MACE,SAASD,IAAI,CAAChB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,KACrC,CAAClB,KAAK,CAACA,MAAMkB,MAAM,GAAG,EAAE,CAACC,QAAQ,CAAC,KAClC;YACA,MAAMC,OAAOpB,MAAMqB,GAAG,GAAIC,IAAI;YAC9BL,MAAMM,OAAO,CAACH;QAChB;QAEA,OAAOH;IACT;IAEA,OAAO,EAAE;AACX;AAEA,SAASO,eAAeC,OAAe;IACrC,MAAMzB,QAAQyB,QAAQnB,KAAK,CAAC;IAC5B,MAAMc,OAAOrB,QAAQC;IACrB,MAAM0B,mBAAmBZ,oBAAoBd;IAE7C,OAAO;QAAEoB;QAAMO,QAAQ3B,MAAM4B,IAAI,CAAC;QAAOF;IAAiB;AAC5D;AAEO,MAAM5B,WAAoC,SAASA,SAAS,KAElE;IAFkE,IAAA,EACjE2B,OAAO,EACR,GAFkE;IAGjE,MAAM,EAAEL,IAAI,EAAEO,MAAM,EAAED,gBAAgB,EAAE,GAAGG,OAAMC,OAAO,CACtD,IAAMN,eAAeC,UACrB;QAACA;KAAQ;IAGX,MAAMM,UAAUF,OAAMC,OAAO,CAAC;QAC5B,OAAOE,cAAK,CAACC,UAAU,CAACN,QAAQ;YAC9BO,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAACT;KAAO;IAEX,qBACE,qBAACU;QAAIC,wBAAAA;OACFlB,sBACC,qBAACmB,sBAAU;QACTC,cAAAA;QACAC,KAAKrB,KAAKjB,QAAQ;QAClBiB,MAAMA,KAAKjB,QAAQ;QACnBS,UAAUQ,KAAKR,QAAQ;sBAG3B,qBAAC8B,aACEX,QAAQY,GAAG,CAAC,CAACC,OAAOC,sBACnB,qBAACC;YACCL,KAAK,AAAC,oBAAiBI;YACvBE,OAAO;gBACLC,OAAOJ,MAAMK,EAAE,GAAG,AAAC,iBAAcL,MAAMK,EAAE,GAAC,MAAKpC;gBAC/C,GAAI+B,MAAMM,UAAU,KAAK,SACrB;oBAAEC,YAAY;gBAAI,IAClBP,MAAMM,UAAU,KAAK,WACrB;oBAAEE,WAAW;gBAAS,IACtBvC,SAAS;YACf;yBAEA,qBAACwC,4BAAa;YAACC,MAAMV,MAAMnB,OAAO;cAGrCC,iBAAiBiB,GAAG,CAAC,CAACY,gCACrB,qBAAChB,sBAAU;YACTC,cAAc;YACdC,KAAKc;YACLnC,MAAMmC;;AAMlB"}