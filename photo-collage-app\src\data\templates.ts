import { CollageTemplate } from '@/types/template';

export const templates: CollageTemplate[] = [
  // Grid Layout Template
  {
    id: 'grid-4x4',
    name: '4x4 Grid',
    description: 'Classic 16-photo grid layout',
    category: 'grid',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 800,
    backgroundColor: '#ffffff',
    slots: [
      // Row 1
      { id: 'slot-1', x: 2, y: 2, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-2', x: 27, y: 2, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-3', x: 52, y: 2, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-4', x: 77, y: 2, width: 21, height: 21, shape: 'rectangle' },
      // Row 2
      { id: 'slot-5', x: 2, y: 27, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-6', x: 27, y: 27, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-7', x: 52, y: 27, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-8', x: 77, y: 27, width: 21, height: 21, shape: 'rectangle' },
      // Row 3
      { id: 'slot-9', x: 2, y: 52, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-10', x: 27, y: 52, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-11', x: 52, y: 52, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-12', x: 77, y: 52, width: 21, height: 21, shape: 'rectangle' },
      // Row 4
      { id: 'slot-13', x: 2, y: 77, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-14', x: 27, y: 77, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-15', x: 52, y: 77, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-16', x: 77, y: 77, width: 21, height: 21, shape: 'rectangle' },
    ]
  },

  // Heart Shape Template
  {
    id: 'heart-shape',
    name: 'Heart Collage',
    description: 'Romantic heart-shaped photo arrangement',
    category: 'heart',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 700,
    backgroundColor: '#ffe6f2',
    slots: [
      // Top left curve
      { id: 'heart-1', x: 15, y: 15, width: 15, height: 15, shape: 'circle' },
      { id: 'heart-2', x: 32, y: 10, width: 12, height: 12, shape: 'circle' },
      { id: 'heart-3', x: 10, y: 32, width: 12, height: 12, shape: 'circle' },
      
      // Top right curve
      { id: 'heart-4', x: 55, y: 15, width: 15, height: 15, shape: 'circle' },
      { id: 'heart-5', x: 72, y: 10, width: 12, height: 12, shape: 'circle' },
      { id: 'heart-6', x: 78, y: 32, width: 12, height: 12, shape: 'circle' },
      
      // Center area
      { id: 'heart-7', x: 35, y: 35, width: 30, height: 20, shape: 'rectangle' },
      
      // Lower sections
      { id: 'heart-8', x: 25, y: 58, width: 18, height: 15, shape: 'rectangle' },
      { id: 'heart-9', x: 57, y: 58, width: 18, height: 15, shape: 'rectangle' },
      { id: 'heart-10', x: 42, y: 75, width: 16, height: 12, shape: 'rectangle' },
    ]
  },

  // Letter 'A' Template
  {
    id: 'letter-a',
    name: 'Letter A',
    description: 'Letter A shaped photo collage',
    category: 'letter',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 800,
    backgroundColor: '#f0f8ff',
    slots: [
      // Top point
      { id: 'a-top', x: 45, y: 5, width: 10, height: 15, shape: 'rectangle' },
      
      // Upper left diagonal
      { id: 'a-ul1', x: 35, y: 22, width: 12, height: 15, shape: 'rectangle', rotation: -15 },
      { id: 'a-ul2', x: 25, y: 38, width: 12, height: 15, shape: 'rectangle', rotation: -15 },
      
      // Upper right diagonal
      { id: 'a-ur1', x: 53, y: 22, width: 12, height: 15, shape: 'rectangle', rotation: 15 },
      { id: 'a-ur2', x: 63, y: 38, width: 12, height: 15, shape: 'rectangle', rotation: 15 },
      
      // Cross bar
      { id: 'a-cross1', x: 35, y: 50, width: 12, height: 8, shape: 'rectangle' },
      { id: 'a-cross2', x: 53, y: 50, width: 12, height: 8, shape: 'rectangle' },
      
      // Lower left leg
      { id: 'a-ll1', x: 15, y: 65, width: 12, height: 15, shape: 'rectangle' },
      { id: 'a-ll2', x: 15, y: 82, width: 12, height: 15, shape: 'rectangle' },
      
      // Lower right leg
      { id: 'a-lr1', x: 73, y: 65, width: 12, height: 15, shape: 'rectangle' },
      { id: 'a-lr2', x: 73, y: 82, width: 12, height: 15, shape: 'rectangle' },
    ]
  },

  // Number '1' Template
  {
    id: 'number-1',
    name: 'Number 1',
    description: 'Number 1 shaped photo collage',
    category: 'number',
    thumbnail: '',
    canvasWidth: 400,
    canvasHeight: 800,
    backgroundColor: '#fff5ee',
    slots: [
      // Top diagonal
      { id: 'num1-top', x: 25, y: 5, width: 15, height: 12, shape: 'rectangle', rotation: 45 },
      
      // Main vertical line
      { id: 'num1-1', x: 40, y: 15, width: 20, height: 15, shape: 'rectangle' },
      { id: 'num1-2', x: 40, y: 32, width: 20, height: 15, shape: 'rectangle' },
      { id: 'num1-3', x: 40, y: 49, width: 20, height: 15, shape: 'rectangle' },
      { id: 'num1-4', x: 40, y: 66, width: 20, height: 15, shape: 'rectangle' },
      
      // Bottom base
      { id: 'num1-base1', x: 20, y: 83, width: 20, height: 12, shape: 'rectangle' },
      { id: 'num1-base2', x: 40, y: 83, width: 20, height: 12, shape: 'rectangle' },
      { id: 'num1-base3', x: 60, y: 83, width: 20, height: 12, shape: 'rectangle' },
    ]
  },

  // Circular Pattern Template
  {
    id: 'circle-pattern',
    name: 'Circle Pattern',
    description: 'Circular arrangement of photos',
    category: 'shape',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 800,
    backgroundColor: '#f5f5f5',
    slots: [
      // Center circle
      { id: 'center', x: 37.5, y: 37.5, width: 25, height: 25, shape: 'circle' },
      
      // Inner ring (8 photos)
      { id: 'inner-1', x: 50, y: 15, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-2', x: 70, y: 25, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-3', x: 80, y: 50, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-4', x: 70, y: 75, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-5', x: 50, y: 85, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-6', x: 25, y: 75, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-7', x: 15, y: 50, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-8', x: 25, y: 25, width: 15, height: 15, shape: 'circle' },
      
      // Outer ring (4 photos)
      { id: 'outer-1', x: 50, y: 2, width: 12, height: 12, shape: 'circle' },
      { id: 'outer-2', x: 88, y: 50, width: 12, height: 12, shape: 'circle' },
      { id: 'outer-3', x: 50, y: 88, width: 12, height: 12, shape: 'circle' },
      { id: 'outer-4', x: 2, y: 50, width: 12, height: 12, shape: 'circle' },
    ]
  }
];

export const getTemplateById = (id: string): CollageTemplate | undefined => {
  return templates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category: CollageTemplate['category']): CollageTemplate[] => {
  return templates.filter(template => template.category === category);
};
