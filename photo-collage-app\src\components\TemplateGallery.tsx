'use client';

import React, { useState } from 'react';
import { CollageTemplate } from '@/types/template';
import { templates } from '@/data/templates';
import TemplatePreview from './TemplatePreview';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

interface TemplateGalleryProps {
  onTemplateSelect: (template: CollageTemplate) => void;
}

const TemplateGallery: React.FC<TemplateGalleryProps> = ({ onTemplateSelect }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<CollageTemplate | null>(null);

  const categories = [
    { id: 'all', name: 'All Templates', icon: '🎨' },
    { id: 'grid', name: 'Grid Layouts', icon: '⚏' },
    { id: 'heart', name: '<PERSON> Shapes', icon: '❤️' },
    { id: 'letter', name: 'Letters', icon: '🔤' },
    { id: 'number', name: 'Numbers', icon: '🔢' },
    { id: 'shape', name: 'Shapes', icon: '🔵' },
  ];

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(template => template.category === selectedCategory);

  const handleTemplateClick = (template: CollageTemplate) => {
    setSelectedTemplate(template);
  };

  const handleUseTemplate = () => {
    if (selectedTemplate) {
      onTemplateSelect(selectedTemplate);
    }
  };

  return (
    <div className="w-full">
      <Card>
        <CardHeader>
          <CardTitle>Choose Your Template</CardTitle>
          <CardDescription>
            Select a template to start creating your collage. Browse by category or view all available options.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Category Filter */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">Categories</h3>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center gap-2"
                >
                  <span>{category.icon}</span>
                  {category.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Template Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <div 
                key={template.id} 
                className={`group cursor-pointer transition-all duration-200 ${
                  selectedTemplate?.id === template.id 
                    ? 'ring-2 ring-blue-500 ring-offset-2' 
                    : 'hover:shadow-lg'
                }`}
                onClick={() => handleTemplateClick(template)}
              >
                <Card className="h-full">
                  <CardContent className="p-4">
                    <div className="mb-4">
                      <TemplatePreview 
                        template={template} 
                        width={250} 
                        showSlotNumbers={false}
                      />
                    </div>
                    <div className="text-center">
                      <h3 className="font-semibold text-lg mb-2">{template.name}</h3>
                      <p className="text-gray-600 text-sm mb-2">{template.description}</p>
                      <div className="flex justify-between items-center text-xs text-gray-500 mb-3">
                        <span className="bg-gray-100 px-2 py-1 rounded">
                          {template.category}
                        </span>
                        <span>{template.slots.length} photos</span>
                      </div>
                      {selectedTemplate?.id === template.id && (
                        <div className="text-blue-600 text-sm font-medium">
                          ✓ Selected
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>

          {/* No templates found */}
          {filteredTemplates.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🎨</div>
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No templates found
              </h3>
              <p className="text-gray-500">
                Try selecting a different category to see more templates.
              </p>
            </div>
          )}

          {/* Selected Template Actions */}
          {selectedTemplate && (
            <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-lg text-blue-900">
                    {selectedTemplate.name}
                  </h3>
                  <p className="text-blue-700 text-sm">
                    Ready to create your collage with {selectedTemplate.slots.length} photos
                  </p>
                </div>
                <div className="flex gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => setSelectedTemplate(null)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleUseTemplate}>
                    Use This Template
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TemplateGallery;
