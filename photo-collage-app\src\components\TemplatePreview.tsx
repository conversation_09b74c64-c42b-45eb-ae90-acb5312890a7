import React from 'react';
import { CollageTemplate, PhotoSlot } from '@/types/template';

interface TemplatePreviewProps {
  template: CollageTemplate;
  width?: number;
  height?: number;
  showSlotNumbers?: boolean;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  template,
  width = 200,
  height = 200,
  showSlotNumbers = false
}) => {
  const aspectRatio = template.canvasWidth / template.canvasHeight;
  const previewHeight = width / aspectRatio;

  const renderSlot = (slot: PhotoSlot, index: number) => {
    const slotStyle: React.CSSProperties = {
      position: 'absolute',
      left: `${slot.x}%`,
      top: `${slot.y}%`,
      width: `${slot.width}%`,
      height: `${slot.height}%`,
      backgroundColor: '#e5e7eb',
      border: '2px solid #9ca3af',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '10px',
      color: '#6b7280',
      fontWeight: 'bold',
    };

    if (slot.shape === 'circle') {
      slotStyle.borderRadius = '50%';
    } else if (slot.shape === 'rectangle') {
      slotStyle.borderRadius = '4px';
    }

    if (slot.rotation) {
      slotStyle.transform = `rotate(${slot.rotation}deg)`;
    }

    return (
      <div key={slot.id} style={slotStyle}>
        {showSlotNumbers && (index + 1)}
      </div>
    );
  };

  return (
    <div className="relative border rounded-lg overflow-hidden shadow-sm">
      <div
        className="relative"
        style={{
          width: `${width}px`,
          height: `${previewHeight}px`,
          backgroundColor: template.backgroundColor || '#ffffff',
        }}
      >
        {template.slots.map((slot, index) => renderSlot(slot, index))}
      </div>
      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2">
        <div className="font-semibold">{template.name}</div>
        <div className="opacity-75">{template.slots.length} photos</div>
      </div>
    </div>
  );
};

export default TemplatePreview;
