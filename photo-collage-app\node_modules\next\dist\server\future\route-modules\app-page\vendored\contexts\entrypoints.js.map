{"version": 3, "sources": ["../../../../../../../src/server/future/route-modules/app-page/vendored/contexts/entrypoints.ts"], "names": ["HeadManagerContext", "ServerInsertedHtml", "AppRouterContext", "HooksClientContext", "RouterContext", "HtmlContext", "AmpContext", "LoadableContext", "ImageConfigContext", "Loadable"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IAAYA,kBAAkB;;;IAClBC,kBAAkB;;;IAClBC,gBAAgB;;;IAChBC,kBAAkB;;;IAClBC,aAAa;;;IACbC,WAAW;;;IACXC,UAAU;;;IACVC,eAAe;;;IACfC,kBAAkB;;;IAClBC,QAAQ;;;;yFATgB;yFACA;uFACF;yFACE;oFACL;kFACF;iFACD;sFACK;yFACG;+EACV"}