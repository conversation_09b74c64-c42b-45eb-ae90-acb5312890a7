/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CCollageApp.tsx&server=false!":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CCollageApp.tsx&server=false! ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CollageApp.tsx */ \"(app-pages-browser)/./src/components/CollageApp.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDV29ya1NwYWNlJTVDcGhvdG9Db2xsYWdlJTVDcGhvdG8tY29sbGFnZS1hcHAlNUNzcmMlNUNjb21wb25lbnRzJTVDQ29sbGFnZUFwcC50c3gmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzQ3NTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxXb3JrU3BhY2VcXFxccGhvdG9Db2xsYWdlXFxcXHBob3RvLWNvbGxhZ2UtYXBwXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXENvbGxhZ2VBcHAudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CCollageApp.tsx&server=false!\n"));

/***/ })

});