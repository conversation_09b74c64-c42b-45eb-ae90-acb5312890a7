"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(app-pages-browser)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(app-pages-browser)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImageUpload */ \"(app-pages-browser)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CollageApp = ()=>{\n    _s();\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 41,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 122,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: [\n                                                    \"Add photos to use in your collage. You need \",\n                                                    selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length,\n                                                    \" photos for this template.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            onPhotosUploaded: setUploadedPhotos,\n                                            maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                children: \"Template Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    template: selectedTemplate,\n                                                    width: 280,\n                                                    showSlotNumbers: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Photo Slots:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Uploaded Photos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            uploadedPhotos.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            uploadedPhotos.length >= ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Ready to create collage!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            uploadedPhotos.length > 0 && uploadedPhotos.length < ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"⚠\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                \"Need \",\n                                                                ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) - uploadedPhotos.length,\n                                                                \" more photos\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 137,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageApp, \"VRQBFVBNyGs2ie7xBu6SudtNgyg=\");\n_c = CollageApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageApp);\nvar _c;\n$RefreshReg$(_c, \"CollageApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageApp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst ImageUpload = (param)=>{\n    let { onPhotosUploaded, maxFiles = 20, acceptedTypes = [\n        \"image/jpeg\",\n        \"image/jpg\",\n        \"image/png\",\n        \"image/webp\"\n    ] } = param;\n    _s();\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            const img = new Image();\n            reader.onload = (e)=>{\n                var _e_target;\n                const url = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                img.onload = ()=>{\n                    const photo = {\n                        id: \"photo-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                        file,\n                        url,\n                        width: img.width,\n                        height: img.height\n                    };\n                    resolve(photo);\n                };\n                img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n                img.src = url;\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsDataURL(file);\n        });\n    }, []);\n    const validateFile = (file)=>{\n        if (!acceptedTypes.includes(file.type)) {\n            return \"File type \".concat(file.type, \" is not supported. Please use JPEG, PNG, or WebP images.\");\n        }\n        if (file.size > 10 * 1024 * 1024) {\n            return \"File size must be less than 10MB.\";\n        }\n        return null;\n    };\n    const processFiles = async (files)=>{\n        setIsUploading(true);\n        const newPhotos = [];\n        const errors = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            if (uploadedPhotos.length + newPhotos.length >= maxFiles) {\n                errors.push(\"Maximum \".concat(maxFiles, \" files allowed. Some files were skipped.\"));\n                break;\n            }\n            const validationError = validateFile(file);\n            if (validationError) {\n                errors.push(\"\".concat(file.name, \": \").concat(validationError));\n                continue;\n            }\n            try {\n                const photo = await createPhotoFromFile(file);\n                newPhotos.push(photo);\n            } catch (error) {\n                errors.push(\"\".concat(file.name, \": Failed to process image\"));\n            }\n        }\n        if (errors.length > 0) {\n            alert(errors.join(\"\\n\"));\n        }\n        const updatedPhotos = [\n            ...uploadedPhotos,\n            ...newPhotos\n        ];\n        setUploadedPhotos(updatedPhotos);\n        onPhotosUploaded(updatedPhotos);\n        setIsUploading(false);\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const removePhoto = (photoId)=>{\n        const updatedPhotos = uploadedPhotos.filter((photo)=>photo.id !== photoId);\n        setUploadedPhotos(updatedPhotos);\n        onPhotosUploaded(updatedPhotos);\n    };\n    const openFileDialog = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6 transition-all duration-200 \".concat(isDragOver ? \"border-blue-500 border-2 bg-blue-50\" : \"border-dashed border-2 border-gray-300\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center cursor-pointer\",\n                        onDragOver: handleDragOver,\n                        onDragLeave: handleDragLeave,\n                        onDrop: handleDrop,\n                        onClick: openFileDialog,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: isUploading ? \"⏳\" : isDragOver ? \"\\uD83D\\uDCE4\" : \"\\uD83D\\uDCF7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: isUploading ? \"Processing images...\" : \"Upload Your Photos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: isDragOver ? \"Drop your images here\" : \"Drag and drop images here, or click to select files\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Supported formats: JPEG, PNG, WebP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Maximum file size: 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Maximum \",\n                                            maxFiles,\n                                            \" files\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined),\n                            !isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"mt-4\",\n                                disabled: isUploading,\n                                children: \"Choose Files\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                multiple: true,\n                accept: acceptedTypes.join(\",\"),\n                onChange: handleFileSelect,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            uploadedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: [\n                                        \"Uploaded Photos (\",\n                                        uploadedPhotos.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        setUploadedPhotos([]);\n                                        onPhotosUploaded([]);\n                                    },\n                                    children: \"Clear All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                            children: uploadedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: photo.url,\n                                                alt: \"Uploaded photo\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removePhoto(photo.id),\n                                            className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-xs text-gray-500 truncate\",\n                                            children: photo.file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                photo.width,\n                                                \" \\xd7 \",\n                                                photo.height\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, photo.id, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageUpload, \"unFRhwm2pWkPzdMzpoNxf1o68AI=\");\n_c = ImageUpload;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ImageUpload);\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ImageUpload.tsx\n"));

/***/ })

});