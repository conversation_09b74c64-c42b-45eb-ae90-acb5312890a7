{"version": 3, "sources": ["../../../../../src/server/web/spec-extension/adapters/request-cookies.ts"], "names": ["ResponseCookies", "ReflectAdapter", "ReadonlyRequestCookiesError", "Error", "constructor", "callable", "RequestCookiesAdapter", "seal", "cookies", "Proxy", "get", "target", "prop", "receiver", "SYMBOL_MODIFY_COOKIE_VALUES", "Symbol", "for", "getModifiedCookieValues", "modified", "Array", "isArray", "length", "appendMutableCookies", "headers", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resCookies", "returnedCookies", "getAll", "cookie", "set", "MutableRequestCookiesAdapter", "wrap", "onUpdateCookies", "response<PERSON><PERSON><PERSON>", "Headers", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "staticGenerationAsyncStore", "fetch", "__nextGetStaticStore", "getStore", "pathWasRevalidated", "allCookies", "filter", "c", "has", "name", "serializedCookies", "tempCookies", "push", "toString", "args", "add", "delete"], "mappings": "AAGA,SAASA,eAAe,QAAQ,aAAY;AAC5C,SAASC,cAAc,QAAQ,YAAW;AAE1C;;CAEC,GACD,OAAO,MAAMC,oCAAoCC;IAC/CC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIH;IACZ;AACF;AAWA,OAAO,MAAMI;IACX,OAAcC,KAAKC,OAAuB,EAA0B;QAClE,OAAO,IAAIC,MAAMD,SAAgB;YAC/BE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOV,4BAA4BG,QAAQ;oBAC7C;wBACE,OAAOJ,eAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF;AAEA,MAAMC,8BAA8BC,OAAOC,GAAG,CAAC;AAE/C,OAAO,SAASC,wBACdT,OAAwB;IAExB,MAAMU,WAAyC,AAACV,OAA0B,CACxEM,4BACD;IACD,IAAI,CAACI,YAAY,CAACC,MAAMC,OAAO,CAACF,aAAaA,SAASG,MAAM,KAAK,GAAG;QAClE,OAAO,EAAE;IACX;IAEA,OAAOH;AACT;AAEA,OAAO,SAASI,qBACdC,OAAgB,EAChBC,cAA+B;IAE/B,MAAMC,uBAAuBR,wBAAwBO;IACrD,IAAIC,qBAAqBJ,MAAM,KAAK,GAAG;QACrC,OAAO;IACT;IAEA,uDAAuD;IACvD,mDAAmD;IACnD,8BAA8B;IAC9B,MAAMK,aAAa,IAAI1B,gBAAgBuB;IACvC,MAAMI,kBAAkBD,WAAWE,MAAM;IAEzC,yCAAyC;IACzC,KAAK,MAAMC,UAAUJ,qBAAsB;QACzCC,WAAWI,GAAG,CAACD;IACjB;IAEA,gDAAgD;IAChD,KAAK,MAAMA,UAAUF,gBAAiB;QACpCD,WAAWI,GAAG,CAACD;IACjB;IAEA,OAAO;AACT;AAMA,OAAO,MAAME;IACX,OAAcC,KACZxB,OAAuB,EACvByB,eAA6C,EAC5B;QACjB,MAAMC,iBAAiB,IAAIlC,gBAAgB,IAAImC;QAC/C,KAAK,MAAMN,UAAUrB,QAAQoB,MAAM,GAAI;YACrCM,eAAeJ,GAAG,CAACD;QACrB;QAEA,IAAIO,iBAAmC,EAAE;QACzC,MAAMC,kBAAkB,IAAIC;QAC5B,MAAMC,wBAAwB;gBAEO;YADnC,gEAAgE;YAChE,MAAMC,6BAA6B,AAACC,MACjCC,oBAAoB,qBADY,8BAAA,AAACD,MACjCC,oBAAoB,MADaD,2BAAD,4BAE/BE,QAAQ;YACZ,IAAIH,4BAA4B;gBAC9BA,2BAA2BI,kBAAkB,GAAG;YAClD;YAEA,MAAMC,aAAaX,eAAeN,MAAM;YACxCQ,iBAAiBS,WAAWC,MAAM,CAAC,CAACC,IAAMV,gBAAgBW,GAAG,CAACD,EAAEE,IAAI;YACpE,IAAIhB,iBAAiB;gBACnB,MAAMiB,oBAA8B,EAAE;gBACtC,KAAK,MAAMrB,UAAUO,eAAgB;oBACnC,MAAMe,cAAc,IAAInD,gBAAgB,IAAImC;oBAC5CgB,YAAYrB,GAAG,CAACD;oBAChBqB,kBAAkBE,IAAI,CAACD,YAAYE,QAAQ;gBAC7C;gBAEApB,gBAAgBiB;YAClB;QACF;QAEA,OAAO,IAAIzC,MAAMyB,gBAAgB;YAC/BxB,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,qDAAqD;oBACrD,KAAKE;wBACH,OAAOsB;oBAET,iEAAiE;oBACjE,yBAAyB;oBACzB,KAAK;wBACH,OAAO,SAAU,GAAGkB,IAAiC;4BACnDjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACL,IAAI;4BAEtD,IAAI;gCACFtC,OAAO6C,MAAM,IAAIF;4BACnB,SAAU;gCACRf;4BACF;wBACF;oBACF,KAAK;wBACH,OAAO,SACL,GAAGe,IAE0B;4BAE7BjB,gBAAgBkB,GAAG,CACjB,OAAOD,IAAI,CAAC,EAAE,KAAK,WAAWA,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE,CAACL,IAAI;4BAEtD,IAAI;gCACF,OAAOtC,OAAOmB,GAAG,IAAIwB;4BACvB,SAAU;gCACRf;4BACF;wBACF;oBACF;wBACE,OAAOtC,eAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;AACF"}