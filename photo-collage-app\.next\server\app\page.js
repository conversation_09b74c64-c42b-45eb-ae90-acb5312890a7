/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDYXBwLXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDY29tcG9uZW50cyU1Q3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMmbW9kdWxlcz1DJTNBJTVDV29ya1NwYWNlJTVDcGhvdG9Db2xsYWdlJTVDcGhvdG8tY29sbGFnZS1hcHAlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWdKO0FBQ2hKLDBPQUFvSjtBQUNwSix3T0FBbUo7QUFDbkosa1BBQXdKO0FBQ3hKLHNRQUFrSztBQUNsSyIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLz81MGZjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxXb3JrU3BhY2VcXFxccGhvdG9Db2xsYWdlXFxcXHBob3RvLWNvbGxhZ2UtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-no-ssr.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CUnifiedEditor.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-no-ssr.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CUnifiedEditor.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js */ \"(ssr)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-no-ssr.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/UnifiedEditor.tsx */ \"(ssr)/./src/components/UnifiedEditor.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNzaGFyZWQlNUNsaWIlNUNsYXp5LWR5bmFtaWMlNUNkeW5hbWljLW5vLXNzci5qcyZtb2R1bGVzPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNVbmlmaWVkRWRpdG9yLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ1BBQTJKO0FBQzNKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvPzMxNzEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxXb3JrU3BhY2VcXFxccGhvdG9Db2xsYWdlXFxcXHBob3RvLWNvbGxhZ2UtYXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcc2hhcmVkXFxcXGxpYlxcXFxsYXp5LWR5bmFtaWNcXFxcZHluYW1pYy1uby1zc3IuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcVW5pZmllZEVkaXRvci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-no-ssr.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CUnifiedEditor.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/LiveCanvas.tsx":
/*!***************************************!*\
  !*** ./src/components/LiveCanvas.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _PhotoControls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PhotoControls */ \"(ssr)/./src/components/PhotoControls.tsx\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize2,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize2,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize2,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize2,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Maximize2,RotateCcw,ZoomIn,ZoomOut!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst LiveCanvas = ({ template, uploadedPhotos, placedPhotos, selectedPhotoId, onPlacedPhotosChange, onPhotoSelect, onDownload })=>{\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [canvasZoom, setCanvasZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Calculate canvas dimensions for display\n    const maxCanvasWidth = isFullscreen ? 800 : 600;\n    const maxCanvasHeight = isFullscreen ? 600 : 450;\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    let displayWidth = maxCanvasWidth;\n    let displayHeight = displayWidth / aspectRatio;\n    if (displayHeight > maxCanvasHeight) {\n        displayHeight = maxCanvasHeight;\n        displayWidth = displayHeight * aspectRatio;\n    }\n    // Apply zoom\n    const finalWidth = displayWidth * canvasZoom;\n    const finalHeight = displayHeight * canvasZoom;\n    const getUploadedPhoto = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    }, [\n        uploadedPhotos\n    ]);\n    const getPlacedPhoto = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    }, [\n        placedPhotos\n    ]);\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const handlePhotoClick = (placedPhoto)=>{\n        onPhotoSelect(selectedPhotoId === placedPhoto.photoId ? null : placedPhoto.photoId);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n        if (selectedPhotoId === photoId) {\n            onPhotoSelect(null);\n        }\n    };\n    const updatePlacedPhoto = (photoId, updates)=>{\n        const updatedPlacedPhotos = placedPhotos.map((p)=>p.photoId === photoId ? {\n                ...p,\n                ...updates\n            } : p);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const clearAllPhotos = ()=>{\n        onPlacedPhotosChange([]);\n        onPhotoSelect(null);\n    };\n    const autoFillSlots = ()=>{\n        const availablePhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n        const availableSlots = template.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        availablePhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: 1.0,\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            onPlacedPhotosChange([\n                ...placedPhotos,\n                ...newPlacedPhotos\n            ]);\n        }\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPlacedPhoto(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const isSelected = selectedPhotoId === placedPhoto?.photoId;\n        const slotStyle = {\n            position: \"absolute\",\n            left: `${slot.x}%`,\n            top: `${slot.y}%`,\n            width: `${slot.width}%`,\n            height: `${slot.height}%`,\n            transform: slot.rotation ? `rotate(${slot.rotation}deg)` : undefined,\n            borderRadius: slot.shape === \"circle\" ? \"50%\" : \"8px\",\n            border: isHovered ? \"2px dashed #3b82f6\" : isSelected ? \"2px solid #3b82f6\" : uploadedPhoto ? \"1px solid #e5e7eb\" : \"2px dashed #d1d5db\",\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f9fafb\",\n            cursor: uploadedPhoto ? \"pointer\" : \"default\",\n            overflow: \"hidden\",\n            transition: \"all 0.2s ease\"\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            onClick: ()=>placedPhoto && handlePhotoClick(placedPhoto),\n            className: \"group\",\n            children: uploadedPhoto && placedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: `scale(${placedPhoto.scale}) rotate(${placedPhoto.rotation}deg)`,\n                            objectPosition: `${placedPhoto.x}% ${placedPhoto.y}%`\n                        },\n                        loading: \"lazy\",\n                        decoding: \"async\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 13\n                    }, undefined),\n                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-blue-500 bg-opacity-20 border-2 border-blue-500 rounded\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 15\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            removePhotoFromSlot(uploadedPhoto.id);\n                        },\n                        className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                lineNumber: 181,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `w-full h-full flex items-center justify-center transition-all duration-200 ${isHovered ? \"text-blue-500 bg-blue-50\" : \"text-gray-400 hover:text-gray-500 hover:bg-gray-50\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-2xl mb-1 transition-transform duration-200 ${isHovered ? \"scale-110\" : \"\"}`,\n                            children: isHovered ? \"\\uD83D\\uDCE4\" : \"\\uD83D\\uDCF7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs font-medium\",\n                            children: isHovered ? \"Drop here!\" : \"Empty slot\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs opacity-75 mt-1\",\n                            children: [\n                                \"Slot \",\n                                template.slots.indexOf(slot) + 1\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                lineNumber: 207,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, undefined);\n    };\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    const completionPercentage = totalSlots > 0 ? Math.round(filledSlots / totalSlots * 100) : 0;\n    const isComplete = filledSlots === totalSlots;\n    const selectedPlacedPhoto = selectedPhotoId ? placedPhotos.find((p)=>p.photoId === selectedPhotoId) : null;\n    const selectedUploadedPhoto = selectedPhotoId ? getUploadedPhoto(selectedPhotoId) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: template.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: isComplete ? \"default\" : \"secondary\",\n                                        children: [\n                                            completionPercentage,\n                                            \"% Complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setCanvasZoom(Math.min(2, canvasZoom + 0.25)),\n                                        disabled: canvasZoom >= 2,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setCanvasZoom(Math.max(0.5, canvasZoom - 0.25)),\n                                        disabled: canvasZoom <= 0.5,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setIsFullscreen(!isFullscreen),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    filledSlots,\n                                    \" of \",\n                                    totalSlots,\n                                    \" slots filled\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: autoFillSlots,\n                                        disabled: uploadedPhotos.length === 0 || filledSlots === totalSlots,\n                                        children: \"Auto Fill\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: clearAllPhotos,\n                                        disabled: placedPhotos.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Clear All\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: onDownload,\n                                        disabled: !isComplete,\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Maximize2_RotateCcw_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Download\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"h-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6 h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-auto max-w-full max-h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm mx-auto\",\n                                        style: {\n                                            width: `${finalWidth}px`,\n                                            height: `${finalHeight}px`,\n                                            backgroundColor: template.backgroundColor || \"#ffffff\",\n                                            minWidth: \"300px\",\n                                            minHeight: \"200px\"\n                                        },\n                                        onClick: ()=>onPhotoSelect(null),\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedPlacedPhoto && selectedUploadedPhoto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoControls__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            placedPhoto: selectedPlacedPhoto,\n                            uploadedPhoto: selectedUploadedPhoto,\n                            onUpdate: (updates)=>updatePlacedPhoto(selectedPhotoId, updates),\n                            onRemove: ()=>{\n                                removePhotoFromSlot(selectedPhotoId);\n                                onPhotoSelect(null);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\LiveCanvas.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LiveCanvas);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LiveCanvas.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PhotoControls.tsx":
/*!******************************************!*\
  !*** ./src/components/PhotoControls.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PhotoControls = ({ placedPhoto, uploadedPhoto, onUpdate, onRemove })=>{\n    const handleScaleChange = (scale)=>{\n        onUpdate({\n            scale: Math.max(0.1, Math.min(3.0, scale))\n        });\n    };\n    const handleRotationChange = (rotation)=>{\n        onUpdate({\n            rotation: rotation % 360\n        });\n    };\n    const handlePositionChange = (x, y)=>{\n        onUpdate({\n            x: Math.max(0, Math.min(100, x)),\n            y: Math.max(0, Math.min(100, y))\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-sm\",\n                    children: \"Photo Controls\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-square w-20 mx-auto bg-gray-100 rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: uploadedPhoto.url,\n                            alt: \"Photo preview\",\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Scale: \",\n                                    (placedPhoto.scale * 100).toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale - 0.1),\n                                        disabled: placedPhoto.scale <= 0.1,\n                                        className: \"touch-manipulation\",\n                                        children: \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0.1\",\n                                        max: \"3.0\",\n                                        step: \"0.1\",\n                                        value: placedPhoto.scale,\n                                        onChange: (e)=>handleScaleChange(parseFloat(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale + 0.1),\n                                        disabled: placedPhoto.scale >= 3.0,\n                                        className: \"touch-manipulation\",\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Rotation: \",\n                                    placedPhoto.rotation,\n                                    \"\\xb0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation - 15),\n                                        className: \"touch-manipulation\",\n                                        children: \"↺\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"360\",\n                                        step: \"15\",\n                                        value: placedPhoto.rotation,\n                                        onChange: (e)=>handleRotationChange(parseInt(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation + 15),\n                                        className: \"touch-manipulation\",\n                                        children: \"↻\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: \"Position\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"X: \",\n                                                    placedPhoto.x.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.x,\n                                                onChange: (e)=>handlePositionChange(parseInt(e.target.value), placedPhoto.y),\n                                                className: \"w-full touch-manipulation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Y: \",\n                                                    placedPhoto.y.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.y,\n                                                onChange: (e)=>handlePositionChange(placedPhoto.x, parseInt(e.target.value)),\n                                                className: \"w-full touch-manipulation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onUpdate({\n                                        scale: 1.0,\n                                        rotation: 0,\n                                        x: 50,\n                                        y: 50\n                                    }),\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"destructive\",\n                                onClick: onRemove,\n                                children: \"Remove\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit width\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio > 1 ? 1.0 : aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Width\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit height\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio < 1 ? 1.0 : 1 / aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Height\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PhotoControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PhotoControls.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PhotoUploadSidebar.tsx":
/*!***********************************************!*\
  !*** ./src/components/PhotoUploadSidebar.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_imageOptimization__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/imageOptimization */ \"(ssr)/./src/lib/imageOptimization.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst PhotoUploadSidebar = ({ uploadedPhotos, placedPhotos, selectedPhotoId, selectedTemplate, onPhotosUploaded, onPhotoRemove, onPhotoSelect })=>{\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadErrors, setUploadErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const maxFiles = selectedTemplate?.slots.length || 20;\n    const acceptedTypes = [\n        \"image/jpeg\",\n        \"image/jpg\",\n        \"image/png\",\n        \"image/webp\"\n    ];\n    const maxFileSize = 10 * 1024 * 1024; // 10MB\n    // Get photos that are not placed in the template\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    // Get photos that are placed in the template\n    const placedPhotosList = uploadedPhotos.filter((photo)=>placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const validateFile = (file)=>{\n        if (!acceptedTypes.includes(file.type)) {\n            return \"File type not supported. Please use JPEG, PNG, or WebP.\";\n        }\n        if (file.size > maxFileSize) {\n            return \"File too large. Maximum size is 10MB.\";\n        }\n        return null;\n    };\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return (0,_lib_imageOptimization__WEBPACK_IMPORTED_MODULE_5__.measureImageProcessingTime)(async ()=>{\n            // Optimize image for better performance\n            const optimized = await (0,_lib_imageOptimization__WEBPACK_IMPORTED_MODULE_5__.optimizeImage)(file, {\n                maxWidth: 2048,\n                maxHeight: 2048,\n                quality: 0.85,\n                format: file.type.includes(\"png\") ? \"png\" : \"jpeg\"\n            });\n            const photo = {\n                id: `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                file: optimized.file,\n                url: optimized.url,\n                width: optimized.width,\n                height: optimized.height\n            };\n            return photo;\n        }, `Image optimization for ${file.name}`);\n    }, []);\n    const processFiles = async (files)=>{\n        setIsUploading(true);\n        setUploadErrors([]);\n        const newPhotos = [];\n        const errors = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            if (uploadedPhotos.length + newPhotos.length >= maxFiles) {\n                errors.push(`Maximum ${maxFiles} files allowed. Some files were skipped.`);\n                break;\n            }\n            const validationError = validateFile(file);\n            if (validationError) {\n                errors.push(`${file.name}: ${validationError}`);\n                continue;\n            }\n            try {\n                const photo = await createPhotoFromFile(file);\n                newPhotos.push(photo);\n            } catch (error) {\n                errors.push(`${file.name}: Failed to process image`);\n            }\n        }\n        if (errors.length > 0) {\n            setUploadErrors(errors);\n        }\n        if (newPhotos.length > 0) {\n            onPhotosUploaded(newPhotos);\n        }\n        setIsUploading(false);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n        // Reset input value to allow selecting the same file again\n        e.target.value = \"\";\n    };\n    const openFileDialog = ()=>{\n        fileInputRef.current?.click();\n    };\n    const handlePhotoClick = (photoId)=>{\n        onPhotoSelect(selectedPhotoId === photoId ? null : photoId);\n    };\n    const handleRemovePhoto = (photoId, e)=>{\n        e.stopPropagation();\n        onPhotoRemove(photoId);\n    };\n    const remainingSlots = selectedTemplate ? selectedTemplate.slots.length - placedPhotos.length : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: `transition-all duration-200 ${isDragOver ? \"border-blue-500 border-2 bg-blue-50\" : \"border-dashed border-2 border-gray-300\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center cursor-pointer\",\n                                onDragOver: handleDragOver,\n                                onDragLeave: handleDragLeave,\n                                onDrop: handleDrop,\n                                onClick: openFileDialog,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl mb-2\",\n                                        children: isUploading ? \"⏳\" : isDragOver ? \"\\uD83D\\uDCE4\" : \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-medium mb-1\",\n                                        children: isUploading ? \"Processing...\" : \"Upload Photos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600 mb-2\",\n                                        children: isDragOver ? \"Drop your images here\" : \"Drag & drop or click to select\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"JPEG, PNG, WebP • Max 10MB\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"Need \",\n                                                    remainingSlots,\n                                                    \" more photos\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        className: \"mt-2\",\n                                        disabled: isUploading,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Choose Files\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined),\n                    uploadErrors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 p-2 bg-red-50 border border-red-200 rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4 text-red-500 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-red-700\",\n                                    children: uploadErrors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: error\n                                        }, index, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: acceptedTypes.join(\",\"),\n                        onChange: handleFileSelect,\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200 bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-xs text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    uploadedPhotos.length,\n                                    \" photos uploaded\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    placedPhotos.length,\n                                    \" placed\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${placedPhotos.length / selectedTemplate.slots.length * 100}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto\",\n                children: [\n                    placedPhotosList.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3 flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"In Collage (\",\n                                    placedPhotosList.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: placedPhotosList.map((photo)=>{\n                                    const isSelected = selectedPhotoId === photo.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `relative group cursor-pointer transition-all ${isSelected ? \"ring-2 ring-blue-500 ring-offset-1 rounded\" : \"\"}`,\n                                        onClick: ()=>handlePhotoClick(photo.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-100 rounded overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: photo.url,\n                                                    alt: \"Placed photo\",\n                                                    className: \"w-full h-full object-cover\",\n                                                    loading: \"lazy\",\n                                                    decoding: \"async\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: \"absolute top-1 left-1 text-xs bg-green-500\",\n                                                children: \"Placed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>handleRemovePhoto(photo.id, e),\n                                                className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, photo.id, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 11\n                    }, undefined),\n                    unplacedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                children: [\n                                    \"Available (\",\n                                    unplacedPhotos.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: unplacedPhotos.map((photo)=>{\n                                    const isSelected = selectedPhotoId === photo.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `relative group cursor-pointer transition-all ${isSelected ? \"ring-2 ring-blue-500 ring-offset-1 rounded\" : \"\"}`,\n                                        onClick: ()=>handlePhotoClick(photo.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-100 rounded overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: photo.url,\n                                                    alt: \"Available photo\",\n                                                    className: \"w-full h-full object-cover\",\n                                                    loading: \"lazy\",\n                                                    decoding: \"async\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>handleRemovePhoto(photo.id, e),\n                                                className: \"absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, photo.id, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, undefined),\n                    uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 text-4xl mb-3\",\n                                children: \"\\uD83D\\uDCF7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-1\",\n                                children: \"No photos yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mb-3\",\n                                children: \"Upload photos to start creating your collage\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                onClick: openFileDialog,\n                                children: \"Upload Photos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoUploadSidebar.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PhotoUploadSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PhotoUploadSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TemplatePreview.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplatePreview.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst TemplatePreview = ({ template, width = 200, height = 200, showSlotNumbers = false })=>{\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const previewHeight = width / aspectRatio;\n    const renderSlot = (slot, index)=>{\n        const slotStyle = {\n            position: \"absolute\",\n            left: `${slot.x}%`,\n            top: `${slot.y}%`,\n            width: `${slot.width}%`,\n            height: `${slot.height}%`,\n            backgroundColor: \"#e5e7eb\",\n            border: \"2px solid #9ca3af\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            fontSize: \"10px\",\n            color: \"#6b7280\",\n            fontWeight: \"bold\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = `rotate(${slot.rotation}deg)`;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            children: showSlotNumbers && index + 1\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border rounded-lg overflow-hidden shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    width: `${width}px`,\n                    height: `${previewHeight}px`,\n                    backgroundColor: template.backgroundColor || \"#ffffff\"\n                },\n                children: template.slots.map((slot, index)=>renderSlot(slot, index))\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold\",\n                        children: template.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-75\",\n                        children: [\n                            template.slots.length,\n                            \" photos\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplatePreview);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TemplatePreview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TemplateSidebar.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateSidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(ssr)/./src/data/templates.ts\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(ssr)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Search,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Search,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Search,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Search,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Search,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Search,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst TemplateSidebar = ({ selectedTemplate, onTemplateSelect })=>{\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const categories = [\n        {\n            id: \"all\",\n            name: \"All\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            count: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.length\n        },\n        {\n            id: \"grid\",\n            name: \"Grid\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            count: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((t)=>t.category === \"grid\").length\n        },\n        {\n            id: \"heart\",\n            name: \"Heart\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            count: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((t)=>t.category === \"heart\").length\n        },\n        {\n            id: \"letter\",\n            name: \"Letter\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            count: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((t)=>t.category === \"letter\").length\n        },\n        {\n            id: \"number\",\n            name: \"Number\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            count: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((t)=>t.category === \"number\").length\n        },\n        {\n            id: \"shape\",\n            name: \"Shape\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            count: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((t)=>t.category === \"shape\").length\n        }\n    ];\n    const filteredTemplates = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let filtered = _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates;\n        // Filter by category\n        if (selectedCategory !== \"all\") {\n            filtered = filtered.filter((template)=>template.category === selectedCategory);\n        }\n        // Filter by search query\n        if (searchQuery.trim()) {\n            const query = searchQuery.toLowerCase().trim();\n            filtered = filtered.filter((template)=>template.name.toLowerCase().includes(query) || template.description.toLowerCase().includes(query) || template.category.toLowerCase().includes(query));\n        }\n        return filtered;\n    }, [\n        selectedCategory,\n        searchQuery\n    ]);\n    const handleTemplateClick = (template)=>{\n        onTemplateSelect(template);\n    };\n    // Keyboard navigation for templates\n    const handleKeyDown = (e, template)=>{\n        if (e.key === \"Enter\" || e.key === \" \") {\n            e.preventDefault();\n            handleTemplateClick(template);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Grid_Hash_Heart_Search_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                            type: \"text\",\n                            placeholder: \"Search templates...\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            className: \"pl-10\",\n                            \"aria-label\": \"Search templates by name, description, or category\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-700 mb-3\",\n                        children: \"Categories\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: categories.map((category)=>{\n                            const Icon = category.icon;\n                            const isSelected = selectedCategory === category.id;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: isSelected ? \"default\" : \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"w-full justify-start gap-2 h-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex-1 text-left\",\n                                        children: category.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs\",\n                                        children: category.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, category.id, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: filteredTemplates.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: filteredTemplates.map((template)=>{\n                        const isSelected = selectedTemplate?.id === template.id;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `group cursor-pointer transition-all duration-200 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 rounded-lg ${isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"hover:shadow-md\"}`,\n                            onClick: ()=>handleTemplateClick(template),\n                            onKeyDown: (e)=>handleKeyDown(e, template),\n                            tabIndex: 0,\n                            role: \"button\",\n                            \"aria-label\": `Select ${template.name} template with ${template.slots.length} photo slots`,\n                            \"aria-pressed\": isSelected,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                template: template,\n                                                width: 200,\n                                                showSlotNumbers: false\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 pt-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-900 line-clamp-1\",\n                                                        children: template.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-600 text-xs font-medium\",\n                                                        children: \"✓ Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 mb-2 line-clamp-2\",\n                                                children: template.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-xs\",\n                                                        children: template.category\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            template.slots.length,\n                                                            \" slots\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 19\n                            }, undefined)\n                        }, template.id, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 17\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-400 text-4xl mb-3\",\n                            children: \"\\uD83D\\uDD0D\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                            children: \"No templates found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Try adjusting your search or category filter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, undefined),\n                        (searchQuery || selectedCategory !== \"all\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: ()=>{\n                                setSearchQuery(\"\");\n                                setSelectedCategory(\"all\");\n                            },\n                            className: \"mt-3\",\n                            children: \"Clear filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-xs text-gray-600 space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Canvas Size:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        selectedTemplate.canvasWidth,\n                                        \" \\xd7 \",\n                                        selectedTemplate.canvasHeight,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Photo Slots:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: selectedTemplate.slots.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Category:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"capitalize\",\n                                    children: selectedTemplate.category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateSidebar.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplateSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TemplateSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UnifiedEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedEditor.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(ssr)/./src/data/templates.ts\");\n/* harmony import */ var _TemplateSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplateSidebar */ \"(ssr)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PhotoUploadSidebar */ \"(ssr)/./src/components/PhotoUploadSidebar.tsx\");\n/* harmony import */ var _LiveCanvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LiveCanvas */ \"(ssr)/./src/components/LiveCanvas.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _ui_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/toast */ \"(ssr)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst UnifiedEditor = ({ initialTemplateId })=>{\n    // Core state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0]);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // UI state\n    const [isTemplateSidebarOpen, setIsTemplateSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPhotoSidebarOpen, setIsPhotoSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedPhotoId, setSelectedPhotoId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Toast notifications\n    const { toasts, removeToast, success, info, error } = (0,_ui_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Auto-place photos when they are uploaded with smart positioning\n    const autoPlacePhotos = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                // Calculate optimal scale based on photo and slot aspect ratios\n                const photoAspectRatio = photo.width / photo.height;\n                const slotAspectRatio = slot.width / slot.height;\n                // Start with a scale that fits the photo nicely in the slot\n                let optimalScale = 1.0;\n                if (photoAspectRatio > slotAspectRatio) {\n                    // Photo is wider than slot, scale to fit height\n                    optimalScale = Math.min(1.2, 1.0 / (photoAspectRatio / slotAspectRatio));\n                } else {\n                    // Photo is taller than slot, scale to fit width\n                    optimalScale = Math.min(1.2, slotAspectRatio / photoAspectRatio);\n                }\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: Math.max(0.8, Math.min(1.5, optimalScale)),\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n            // Show a brief notification about auto-placement\n            success(`Auto-placed ${newPlacedPhotos.length} photo${newPlacedPhotos.length > 1 ? \"s\" : \"\"}`, `Photos were automatically placed in available template slots`);\n        }\n    }, [\n        selectedTemplate,\n        placedPhotos\n    ]);\n    // Handle photo uploads with auto-placement\n    const handlePhotosUploaded = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        setUploadedPhotos((prev)=>{\n            const updated = [\n                ...prev,\n                ...newPhotos\n            ];\n            // Auto-place new photos\n            autoPlacePhotos(newPhotos);\n            return updated;\n        });\n    }, [\n        autoPlacePhotos\n    ]);\n    // Handle template change\n    const handleTemplateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((template)=>{\n        const previousTemplate = selectedTemplate;\n        setSelectedTemplate(template);\n        // Clear placed photos when template changes\n        setPlacedPhotos([]);\n        setSelectedPhotoId(null);\n        // Show template change notification\n        info(`Switched to ${template.name}`, `Template changed from ${previousTemplate?.name || \"none\"} to ${template.name}`);\n        // Auto-place existing photos in new template\n        if (uploadedPhotos.length > 0) {\n            setTimeout(()=>autoPlacePhotos(uploadedPhotos), 100); // Small delay for better UX\n        }\n    }, [\n        uploadedPhotos,\n        autoPlacePhotos,\n        selectedTemplate,\n        info\n    ]);\n    // Handle photo placement changes\n    const handlePlacedPhotosChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    }, []);\n    // Handle photo removal\n    const handlePhotoRemove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((photoId)=>{\n        setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photoId));\n        setPlacedPhotos((prev)=>prev.filter((p)=>p.photoId !== photoId));\n        if (selectedPhotoId === photoId) {\n            setSelectedPhotoId(null);\n        }\n    }, [\n        selectedPhotoId\n    ]);\n    // Calculate completion status\n    const filledSlots = placedPhotos.length;\n    const totalSlots = selectedTemplate?.slots.length || 0;\n    const completionPercentage = totalSlots > 0 ? Math.round(filledSlots / totalSlots * 100) : 0;\n    const isComplete = filledSlots === totalSlots && totalSlots > 0;\n    // Handle download\n    const handleDownload = async ()=>{\n        if (!selectedTemplate || placedPhotos.length === 0) return;\n        setIsDownloading(true);\n        try {\n            // This will be implemented with the LiveCanvas component\n            console.log(\"Download initiated\");\n        } catch (error) {\n            console.error(\"Download failed:\", error);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Handle share\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: \"Check out my photo collage!\",\n                    text: `I created this amazing collage using ${selectedTemplate?.name} template`,\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Share cancelled or failed\");\n            }\n        } else {\n            // Fallback: copy URL to clipboard\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_toast__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center gap-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: selectedTemplate.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                completionPercentage,\n                                                \"% complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleShare,\n                                    className: \"hidden md:flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Share\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: !isComplete || isDownloading,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isDownloading ? \"Downloading...\" : \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: `bg-white border-r border-gray-200 transition-all duration-300 ${isTemplateSidebarOpen ? \"w-80\" : \"w-12\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsTemplateSidebarOpen(!isTemplateSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isTemplateSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 42\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 80\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    selectedTemplate: selectedTemplate,\n                                    onTemplateSelect: handleTemplateChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-6\",\n                            children: selectedTemplate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LiveCanvas__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                selectedPhotoId: selectedPhotoId,\n                                onPlacedPhotosChange: handlePlacedPhotosChange,\n                                onPhotoSelect: setSelectedPhotoId,\n                                onDownload: handleDownload\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"p-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Select a Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Choose a template from the sidebar to get started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: `bg-white border-l border-gray-200 transition-all duration-300 ${isPhotoSidebarOpen ? \"w-80\" : \"w-12\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsPhotoSidebarOpen(!isPhotoSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isPhotoSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 39\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 78\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Photos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined),\n                                isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    uploadedPhotos: uploadedPhotos,\n                                    placedPhotos: placedPhotos,\n                                    selectedPhotoId: selectedPhotoId,\n                                    selectedTemplate: selectedTemplate,\n                                    onPhotosUploaded: handlePhotosUploaded,\n                                    onPhotoRemove: handlePhotoRemove,\n                                    onPhotoSelect: setSelectedPhotoId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UnifiedEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UnifiedEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIElucHV0UHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5JbnB1dEhUTUxBdHRyaWJ1dGVzPEhUTUxJbnB1dEVsZW1lbnQ+IHt9XG5cbmNvbnN0IElucHV0ID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MSW5wdXRFbGVtZW50LCBJbnB1dFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGlucHV0XG4gICAgICAgIHR5cGU9e3R5cGV9XG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IGgtMTAgdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LXNtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastContainer: () => (/* binding */ ToastContainer),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ ToastContainer,useToast auto */ \n\n\nconst ToastComponent = ({ toast, onRemove })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            onRemove(toast.id);\n        }, toast.duration || 3000);\n        return ()=>clearTimeout(timer);\n    }, [\n        toast.id,\n        toast.duration,\n        onRemove\n    ]);\n    const getIcon = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getBgColor = ()=>{\n        switch(toast.type){\n            case \"success\":\n                return \"bg-green-50 border-green-200\";\n            case \"error\":\n                return \"bg-red-50 border-red-200\";\n            case \"info\":\n                return \"bg-blue-50 border-blue-200\";\n            default:\n                return \"bg-blue-50 border-blue-200\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${getBgColor()} border rounded-lg p-4 shadow-lg max-w-sm w-full animate-in slide-in-from-right-full duration-300`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                getIcon(),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: toast.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined),\n                        toast.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mt-1\",\n                            children: toast.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onRemove(toast.id),\n                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\nconst ToastContainer = ({ toasts, onRemove })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastComponent, {\n                toast: toast,\n                onRemove: onRemove\n            }, toast.id, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook for managing toasts\nconst useToast = ()=>{\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (toast)=>{\n        const id = Math.random().toString(36).substr(2, 9);\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    ...toast,\n                    id\n                }\n            ]);\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const success = (title, description)=>{\n        addToast({\n            type: \"success\",\n            title,\n            description\n        });\n    };\n    const error = (title, description)=>{\n        addToast({\n            type: \"error\",\n            title,\n            description\n        });\n    };\n    const info = (title, description)=>{\n        addToast({\n            type: \"info\",\n            title,\n            description\n        });\n    };\n    return {\n        toasts,\n        addToast,\n        removeToast,\n        success,\n        error,\n        info\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/templates.ts":
/*!*******************************!*\
  !*** ./src/data/templates.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTemplateById: () => (/* binding */ getTemplateById),\n/* harmony export */   getTemplatesByCategory: () => (/* binding */ getTemplatesByCategory),\n/* harmony export */   templates: () => (/* binding */ templates)\n/* harmony export */ });\nconst templates = [\n    // Grid Layout Template\n    {\n        id: \"grid-4x4\",\n        name: \"4x4 Grid\",\n        description: \"Classic 16-photo grid layout\",\n        category: \"grid\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#ffffff\",\n        slots: [\n            // Row 1\n            {\n                id: \"slot-1\",\n                x: 2,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-2\",\n                x: 27,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-3\",\n                x: 52,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-4\",\n                x: 77,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 2\n            {\n                id: \"slot-5\",\n                x: 2,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-6\",\n                x: 27,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-7\",\n                x: 52,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-8\",\n                x: 77,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 3\n            {\n                id: \"slot-9\",\n                x: 2,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-10\",\n                x: 27,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-11\",\n                x: 52,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-12\",\n                x: 77,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 4\n            {\n                id: \"slot-13\",\n                x: 2,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-14\",\n                x: 27,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-15\",\n                x: 52,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-16\",\n                x: 77,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Heart Shape Template\n    {\n        id: \"heart-shape\",\n        name: \"Heart Collage\",\n        description: \"Romantic heart-shaped photo arrangement\",\n        category: \"heart\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 700,\n        backgroundColor: \"#ffe6f2\",\n        slots: [\n            // Top left curve\n            {\n                id: \"heart-1\",\n                x: 15,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-2\",\n                x: 32,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-3\",\n                x: 10,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Top right curve\n            {\n                id: \"heart-4\",\n                x: 55,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-5\",\n                x: 72,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-6\",\n                x: 78,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Center area\n            {\n                id: \"heart-7\",\n                x: 35,\n                y: 35,\n                width: 30,\n                height: 20,\n                shape: \"rectangle\"\n            },\n            // Lower sections\n            {\n                id: \"heart-8\",\n                x: 25,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-9\",\n                x: 57,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-10\",\n                x: 42,\n                y: 75,\n                width: 16,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Letter 'A' Template\n    {\n        id: \"letter-a\",\n        name: \"Letter A\",\n        description: \"Letter A shaped photo collage\",\n        category: \"letter\",\n        thumbnail: \"\",\n        canvasWidth: 600,\n        canvasHeight: 800,\n        backgroundColor: \"#f0f8ff\",\n        slots: [\n            // Top point\n            {\n                id: \"a-top\",\n                x: 45,\n                y: 5,\n                width: 10,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Upper left diagonal\n            {\n                id: \"a-ul1\",\n                x: 35,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            {\n                id: \"a-ul2\",\n                x: 25,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            // Upper right diagonal\n            {\n                id: \"a-ur1\",\n                x: 53,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            {\n                id: \"a-ur2\",\n                x: 63,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            // Cross bar\n            {\n                id: \"a-cross1\",\n                x: 35,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-cross2\",\n                x: 53,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            // Lower left leg\n            {\n                id: \"a-ll1\",\n                x: 15,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-ll2\",\n                x: 15,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Lower right leg\n            {\n                id: \"a-lr1\",\n                x: 73,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-lr2\",\n                x: 73,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Number '1' Template\n    {\n        id: \"number-1\",\n        name: \"Number 1\",\n        description: \"Number 1 shaped photo collage\",\n        category: \"number\",\n        thumbnail: \"\",\n        canvasWidth: 400,\n        canvasHeight: 800,\n        backgroundColor: \"#fff5ee\",\n        slots: [\n            // Top diagonal\n            {\n                id: \"num1-top\",\n                x: 25,\n                y: 5,\n                width: 15,\n                height: 12,\n                shape: \"rectangle\",\n                rotation: 45\n            },\n            // Main vertical line\n            {\n                id: \"num1-1\",\n                x: 40,\n                y: 15,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-2\",\n                x: 40,\n                y: 32,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-3\",\n                x: 40,\n                y: 49,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-4\",\n                x: 40,\n                y: 66,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Bottom base\n            {\n                id: \"num1-base1\",\n                x: 20,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base2\",\n                x: 40,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base3\",\n                x: 60,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Circular Pattern Template\n    {\n        id: \"circle-pattern\",\n        name: \"Circle Pattern\",\n        description: \"Circular arrangement of photos\",\n        category: \"shape\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#f5f5f5\",\n        slots: [\n            // Center circle\n            {\n                id: \"center\",\n                x: 37.5,\n                y: 37.5,\n                width: 25,\n                height: 25,\n                shape: \"circle\"\n            },\n            // Inner ring (8 photos)\n            {\n                id: \"inner-1\",\n                x: 50,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-2\",\n                x: 70,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-3\",\n                x: 80,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-4\",\n                x: 70,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-5\",\n                x: 50,\n                y: 85,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-6\",\n                x: 25,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-7\",\n                x: 15,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-8\",\n                x: 25,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            // Outer ring (4 photos)\n            {\n                id: \"outer-1\",\n                x: 50,\n                y: 2,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-2\",\n                x: 88,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-3\",\n                x: 50,\n                y: 88,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-4\",\n                x: 2,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            }\n        ]\n    }\n];\nconst getTemplateById = (id)=>{\n    return templates.find((template)=>template.id === id);\n};\nconst getTemplatesByCategory = (category)=>{\n    return templates.filter((template)=>template.category === category);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/templates.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/imageOptimization.ts":
/*!**************************************!*\
  !*** ./src/lib/imageOptimization.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertImageFormat: () => (/* binding */ convertImageFormat),\n/* harmony export */   createThumbnail: () => (/* binding */ createThumbnail),\n/* harmony export */   getImageDimensions: () => (/* binding */ getImageDimensions),\n/* harmony export */   measureImageProcessingTime: () => (/* binding */ measureImageProcessingTime),\n/* harmony export */   optimizeImage: () => (/* binding */ optimizeImage),\n/* harmony export */   preloadImage: () => (/* binding */ preloadImage),\n/* harmony export */   preloadImages: () => (/* binding */ preloadImages),\n/* harmony export */   processImageWithCleanup: () => (/* binding */ processImageWithCleanup)\n/* harmony export */ });\n/**\n * Image optimization utilities for better performance\n */ /**\n * Compress and optimize an image file\n */ const optimizeImage = async (file, options = {})=>{\n    const { maxWidth = 1920, maxHeight = 1080, quality = 0.8, format = \"jpeg\" } = options;\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        const canvas = document.createElement(\"canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) {\n            reject(new Error(\"Could not get canvas context\"));\n            return;\n        }\n        img.onload = ()=>{\n            // Calculate new dimensions while maintaining aspect ratio\n            let { width, height } = img;\n            if (width > maxWidth || height > maxHeight) {\n                const aspectRatio = width / height;\n                if (width > height) {\n                    width = Math.min(width, maxWidth);\n                    height = width / aspectRatio;\n                } else {\n                    height = Math.min(height, maxHeight);\n                    width = height * aspectRatio;\n                }\n            }\n            // Set canvas dimensions\n            canvas.width = width;\n            canvas.height = height;\n            // Draw and compress image\n            ctx.drawImage(img, 0, 0, width, height);\n            canvas.toBlob((blob)=>{\n                if (!blob) {\n                    reject(new Error(\"Failed to compress image\"));\n                    return;\n                }\n                const optimizedFile = new File([\n                    blob\n                ], file.name, {\n                    type: `image/${format}`,\n                    lastModified: Date.now()\n                });\n                const url = URL.createObjectURL(optimizedFile);\n                resolve({\n                    file: optimizedFile,\n                    url,\n                    width: Math.round(width),\n                    height: Math.round(height)\n                });\n            }, `image/${format}`, quality);\n        };\n        img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n        img.src = URL.createObjectURL(file);\n    });\n};\n/**\n * Create a thumbnail version of an image\n */ const createThumbnail = async (file, size = 200)=>{\n    const optimized = await optimizeImage(file, {\n        maxWidth: size,\n        maxHeight: size,\n        quality: 0.7,\n        format: \"jpeg\"\n    });\n    return {\n        url: optimized.url,\n        width: optimized.width,\n        height: optimized.height\n    };\n};\n/**\n * Preload images for better performance\n */ const preloadImage = (src)=>{\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.onload = ()=>resolve(img);\n        img.onerror = reject;\n        img.src = src;\n    });\n};\n/**\n * Batch preload multiple images\n */ const preloadImages = async (urls)=>{\n    const promises = urls.map((url)=>preloadImage(url));\n    return Promise.all(promises);\n};\n/**\n * Get image dimensions without loading the full image\n */ const getImageDimensions = (file)=>{\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.onload = ()=>{\n            resolve({\n                width: img.width,\n                height: img.height\n            });\n            URL.revokeObjectURL(img.src);\n        };\n        img.onerror = reject;\n        img.src = URL.createObjectURL(file);\n    });\n};\n/**\n * Convert image to different format\n */ const convertImageFormat = async (file, targetFormat, quality = 0.8)=>{\n    const optimized = await optimizeImage(file, {\n        format: targetFormat,\n        quality,\n        maxWidth: 4096,\n        maxHeight: 4096\n    });\n    return optimized.file;\n};\n/**\n * Performance monitoring utilities\n */ const measureImageProcessingTime = async (operation, operationName)=>{\n    const startTime = performance.now();\n    try {\n        const result = await operation();\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        console.log(`Image operation \"${operationName}\" took ${duration.toFixed(2)}ms`);\n        // Report to Core Web Vitals if available\n        if (\"PerformanceObserver\" in window) {\n            // This would integrate with real performance monitoring\n            console.log(`Performance: ${operationName} - ${duration}ms`);\n        }\n        return result;\n    } catch (error) {\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        console.error(`Image operation \"${operationName}\" failed after ${duration.toFixed(2)}ms:`, error);\n        throw error;\n    }\n};\n/**\n * Memory-efficient image processing with cleanup\n */ const processImageWithCleanup = async (file, processor)=>{\n    const url = URL.createObjectURL(file);\n    try {\n        return await processor(url);\n    } finally{\n        // Clean up object URL to prevent memory leaks\n        URL.revokeObjectURL(url);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2ltYWdlT3B0aW1pemF0aW9uLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUE7O0NBRUMsR0FTRDs7Q0FFQyxHQUNNLE1BQU1BLGdCQUFnQixPQUMzQkMsTUFDQUMsVUFBb0MsQ0FBQyxDQUFDO0lBRXRDLE1BQU0sRUFDSkMsV0FBVyxJQUFJLEVBQ2ZDLFlBQVksSUFBSSxFQUNoQkMsVUFBVSxHQUFHLEVBQ2JDLFNBQVMsTUFBTSxFQUNoQixHQUFHSjtJQUVKLE9BQU8sSUFBSUssUUFBUSxDQUFDQyxTQUFTQztRQUMzQixNQUFNQyxNQUFNLElBQUlDO1FBQ2hCLE1BQU1DLFNBQVNDLFNBQVNDLGFBQWEsQ0FBQztRQUN0QyxNQUFNQyxNQUFNSCxPQUFPSSxVQUFVLENBQUM7UUFFOUIsSUFBSSxDQUFDRCxLQUFLO1lBQ1JOLE9BQU8sSUFBSVEsTUFBTTtZQUNqQjtRQUNGO1FBRUFQLElBQUlRLE1BQU0sR0FBRztZQUNYLDBEQUEwRDtZQUMxRCxJQUFJLEVBQUVDLEtBQUssRUFBRUMsTUFBTSxFQUFFLEdBQUdWO1lBRXhCLElBQUlTLFFBQVFoQixZQUFZaUIsU0FBU2hCLFdBQVc7Z0JBQzFDLE1BQU1pQixjQUFjRixRQUFRQztnQkFFNUIsSUFBSUQsUUFBUUMsUUFBUTtvQkFDbEJELFFBQVFHLEtBQUtDLEdBQUcsQ0FBQ0osT0FBT2hCO29CQUN4QmlCLFNBQVNELFFBQVFFO2dCQUNuQixPQUFPO29CQUNMRCxTQUFTRSxLQUFLQyxHQUFHLENBQUNILFFBQVFoQjtvQkFDMUJlLFFBQVFDLFNBQVNDO2dCQUNuQjtZQUNGO1lBRUEsd0JBQXdCO1lBQ3hCVCxPQUFPTyxLQUFLLEdBQUdBO1lBQ2ZQLE9BQU9RLE1BQU0sR0FBR0E7WUFFaEIsMEJBQTBCO1lBQzFCTCxJQUFJUyxTQUFTLENBQUNkLEtBQUssR0FBRyxHQUFHUyxPQUFPQztZQUVoQ1IsT0FBT2EsTUFBTSxDQUNYLENBQUNDO2dCQUNDLElBQUksQ0FBQ0EsTUFBTTtvQkFDVGpCLE9BQU8sSUFBSVEsTUFBTTtvQkFDakI7Z0JBQ0Y7Z0JBRUEsTUFBTVUsZ0JBQWdCLElBQUlDLEtBQUs7b0JBQUNGO2lCQUFLLEVBQUV6QixLQUFLNEIsSUFBSSxFQUFFO29CQUNoREMsTUFBTSxDQUFDLE1BQU0sRUFBRXhCLE9BQU8sQ0FBQztvQkFDdkJ5QixjQUFjQyxLQUFLQyxHQUFHO2dCQUN4QjtnQkFFQSxNQUFNQyxNQUFNQyxJQUFJQyxlQUFlLENBQUNUO2dCQUVoQ25CLFFBQVE7b0JBQ05QLE1BQU0wQjtvQkFDTk87b0JBQ0FmLE9BQU9HLEtBQUtlLEtBQUssQ0FBQ2xCO29CQUNsQkMsUUFBUUUsS0FBS2UsS0FBSyxDQUFDakI7Z0JBQ3JCO1lBQ0YsR0FDQSxDQUFDLE1BQU0sRUFBRWQsT0FBTyxDQUFDLEVBQ2pCRDtRQUVKO1FBRUFLLElBQUk0QixPQUFPLEdBQUcsSUFBTTdCLE9BQU8sSUFBSVEsTUFBTTtRQUNyQ1AsSUFBSTZCLEdBQUcsR0FBR0osSUFBSUMsZUFBZSxDQUFDbkM7SUFDaEM7QUFDRixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNdUMsa0JBQWtCLE9BQzdCdkMsTUFDQXdDLE9BQWUsR0FBRztJQUVsQixNQUFNQyxZQUFZLE1BQU0xQyxjQUFjQyxNQUFNO1FBQzFDRSxVQUFVc0M7UUFDVnJDLFdBQVdxQztRQUNYcEMsU0FBUztRQUNUQyxRQUFRO0lBQ1Y7SUFFQSxPQUFPO1FBQ0w0QixLQUFLUSxVQUFVUixHQUFHO1FBQ2xCZixPQUFPdUIsVUFBVXZCLEtBQUs7UUFDdEJDLFFBQVFzQixVQUFVdEIsTUFBTTtJQUMxQjtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU11QixlQUFlLENBQUNKO0lBQzNCLE9BQU8sSUFBSWhDLFFBQVEsQ0FBQ0MsU0FBU0M7UUFDM0IsTUFBTUMsTUFBTSxJQUFJQztRQUNoQkQsSUFBSVEsTUFBTSxHQUFHLElBQU1WLFFBQVFFO1FBQzNCQSxJQUFJNEIsT0FBTyxHQUFHN0I7UUFDZEMsSUFBSTZCLEdBQUcsR0FBR0E7SUFDWjtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU1LLGdCQUFnQixPQUFPQztJQUNsQyxNQUFNQyxXQUFXRCxLQUFLRSxHQUFHLENBQUNiLENBQUFBLE1BQU9TLGFBQWFUO0lBQzlDLE9BQU8zQixRQUFReUMsR0FBRyxDQUFDRjtBQUNyQixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNRyxxQkFBcUIsQ0FBQ2hEO0lBQ2pDLE9BQU8sSUFBSU0sUUFBUSxDQUFDQyxTQUFTQztRQUMzQixNQUFNQyxNQUFNLElBQUlDO1FBQ2hCRCxJQUFJUSxNQUFNLEdBQUc7WUFDWFYsUUFBUTtnQkFBRVcsT0FBT1QsSUFBSVMsS0FBSztnQkFBRUMsUUFBUVYsSUFBSVUsTUFBTTtZQUFDO1lBQy9DZSxJQUFJZSxlQUFlLENBQUN4QyxJQUFJNkIsR0FBRztRQUM3QjtRQUNBN0IsSUFBSTRCLE9BQU8sR0FBRzdCO1FBQ2RDLElBQUk2QixHQUFHLEdBQUdKLElBQUlDLGVBQWUsQ0FBQ25DO0lBQ2hDO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTWtELHFCQUFxQixPQUNoQ2xELE1BQ0FtRCxjQUNBL0MsVUFBa0IsR0FBRztJQUVyQixNQUFNcUMsWUFBWSxNQUFNMUMsY0FBY0MsTUFBTTtRQUMxQ0ssUUFBUThDO1FBQ1IvQztRQUNBRixVQUFVO1FBQ1ZDLFdBQVc7SUFDYjtJQUVBLE9BQU9zQyxVQUFVekMsSUFBSTtBQUN2QixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNb0QsNkJBQTZCLE9BQ3hDQyxXQUNBQztJQUVBLE1BQU1DLFlBQVlDLFlBQVl4QixHQUFHO0lBRWpDLElBQUk7UUFDRixNQUFNeUIsU0FBUyxNQUFNSjtRQUNyQixNQUFNSyxVQUFVRixZQUFZeEIsR0FBRztRQUMvQixNQUFNMkIsV0FBV0QsVUFBVUg7UUFFM0JLLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGlCQUFpQixFQUFFUCxjQUFjLE9BQU8sRUFBRUssU0FBU0csT0FBTyxDQUFDLEdBQUcsRUFBRSxDQUFDO1FBRTlFLHlDQUF5QztRQUN6QyxJQUFJLHlCQUF5QkMsUUFBUTtZQUNuQyx3REFBd0Q7WUFDeERILFFBQVFDLEdBQUcsQ0FBQyxDQUFDLGFBQWEsRUFBRVAsY0FBYyxHQUFHLEVBQUVLLFNBQVMsRUFBRSxDQUFDO1FBQzdEO1FBRUEsT0FBT0Y7SUFDVCxFQUFFLE9BQU9PLE9BQU87UUFDZCxNQUFNTixVQUFVRixZQUFZeEIsR0FBRztRQUMvQixNQUFNMkIsV0FBV0QsVUFBVUg7UUFDM0JLLFFBQVFJLEtBQUssQ0FBQyxDQUFDLGlCQUFpQixFQUFFVixjQUFjLGVBQWUsRUFBRUssU0FBU0csT0FBTyxDQUFDLEdBQUcsR0FBRyxDQUFDLEVBQUVFO1FBQzNGLE1BQU1BO0lBQ1I7QUFDRixFQUFFO0FBRUY7O0NBRUMsR0FDTSxNQUFNQywwQkFBMEIsT0FDckNqRSxNQUNBa0U7SUFFQSxNQUFNakMsTUFBTUMsSUFBSUMsZUFBZSxDQUFDbkM7SUFFaEMsSUFBSTtRQUNGLE9BQU8sTUFBTWtFLFVBQVVqQztJQUN6QixTQUFVO1FBQ1IsOENBQThDO1FBQzlDQyxJQUFJZSxlQUFlLENBQUNoQjtJQUN0QjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9waG90by1jb2xsYWdlLWFwcC8uL3NyYy9saWIvaW1hZ2VPcHRpbWl6YXRpb24udHM/N2RmNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEltYWdlIG9wdGltaXphdGlvbiB1dGlsaXRpZXMgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICovXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW1hZ2VPcHRpbWl6YXRpb25PcHRpb25zIHtcbiAgbWF4V2lkdGg/OiBudW1iZXI7XG4gIG1heEhlaWdodD86IG51bWJlcjtcbiAgcXVhbGl0eT86IG51bWJlcjtcbiAgZm9ybWF0PzogJ2pwZWcnIHwgJ3BuZycgfCAnd2VicCc7XG59XG5cbi8qKlxuICogQ29tcHJlc3MgYW5kIG9wdGltaXplIGFuIGltYWdlIGZpbGVcbiAqL1xuZXhwb3J0IGNvbnN0IG9wdGltaXplSW1hZ2UgPSBhc3luYyAoXG4gIGZpbGU6IEZpbGUsXG4gIG9wdGlvbnM6IEltYWdlT3B0aW1pemF0aW9uT3B0aW9ucyA9IHt9XG4pOiBQcm9taXNlPHsgZmlsZTogRmlsZTsgdXJsOiBzdHJpbmc7IHdpZHRoOiBudW1iZXI7IGhlaWdodDogbnVtYmVyIH0+ID0+IHtcbiAgY29uc3Qge1xuICAgIG1heFdpZHRoID0gMTkyMCxcbiAgICBtYXhIZWlnaHQgPSAxMDgwLFxuICAgIHF1YWxpdHkgPSAwLjgsXG4gICAgZm9ybWF0ID0gJ2pwZWcnXG4gIH0gPSBvcHRpb25zO1xuXG4gIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgY29uc3QgaW1nID0gbmV3IEltYWdlKCk7XG4gICAgY29uc3QgY2FudmFzID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnY2FudmFzJyk7XG4gICAgY29uc3QgY3R4ID0gY2FudmFzLmdldENvbnRleHQoJzJkJyk7XG5cbiAgICBpZiAoIWN0eCkge1xuICAgICAgcmVqZWN0KG5ldyBFcnJvcignQ291bGQgbm90IGdldCBjYW52YXMgY29udGV4dCcpKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpbWcub25sb2FkID0gKCkgPT4ge1xuICAgICAgLy8gQ2FsY3VsYXRlIG5ldyBkaW1lbnNpb25zIHdoaWxlIG1haW50YWluaW5nIGFzcGVjdCByYXRpb1xuICAgICAgbGV0IHsgd2lkdGgsIGhlaWdodCB9ID0gaW1nO1xuICAgICAgXG4gICAgICBpZiAod2lkdGggPiBtYXhXaWR0aCB8fCBoZWlnaHQgPiBtYXhIZWlnaHQpIHtcbiAgICAgICAgY29uc3QgYXNwZWN0UmF0aW8gPSB3aWR0aCAvIGhlaWdodDtcbiAgICAgICAgXG4gICAgICAgIGlmICh3aWR0aCA+IGhlaWdodCkge1xuICAgICAgICAgIHdpZHRoID0gTWF0aC5taW4od2lkdGgsIG1heFdpZHRoKTtcbiAgICAgICAgICBoZWlnaHQgPSB3aWR0aCAvIGFzcGVjdFJhdGlvO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGhlaWdodCA9IE1hdGgubWluKGhlaWdodCwgbWF4SGVpZ2h0KTtcbiAgICAgICAgICB3aWR0aCA9IGhlaWdodCAqIGFzcGVjdFJhdGlvO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC8vIFNldCBjYW52YXMgZGltZW5zaW9uc1xuICAgICAgY2FudmFzLndpZHRoID0gd2lkdGg7XG4gICAgICBjYW52YXMuaGVpZ2h0ID0gaGVpZ2h0O1xuXG4gICAgICAvLyBEcmF3IGFuZCBjb21wcmVzcyBpbWFnZVxuICAgICAgY3R4LmRyYXdJbWFnZShpbWcsIDAsIDAsIHdpZHRoLCBoZWlnaHQpO1xuXG4gICAgICBjYW52YXMudG9CbG9iKFxuICAgICAgICAoYmxvYikgPT4ge1xuICAgICAgICAgIGlmICghYmxvYikge1xuICAgICAgICAgICAgcmVqZWN0KG5ldyBFcnJvcignRmFpbGVkIHRvIGNvbXByZXNzIGltYWdlJykpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IG9wdGltaXplZEZpbGUgPSBuZXcgRmlsZShbYmxvYl0sIGZpbGUubmFtZSwge1xuICAgICAgICAgICAgdHlwZTogYGltYWdlLyR7Zm9ybWF0fWAsXG4gICAgICAgICAgICBsYXN0TW9kaWZpZWQ6IERhdGUubm93KCksXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKG9wdGltaXplZEZpbGUpO1xuXG4gICAgICAgICAgcmVzb2x2ZSh7XG4gICAgICAgICAgICBmaWxlOiBvcHRpbWl6ZWRGaWxlLFxuICAgICAgICAgICAgdXJsLFxuICAgICAgICAgICAgd2lkdGg6IE1hdGgucm91bmQod2lkdGgpLFxuICAgICAgICAgICAgaGVpZ2h0OiBNYXRoLnJvdW5kKGhlaWdodClcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgYGltYWdlLyR7Zm9ybWF0fWAsXG4gICAgICAgIHF1YWxpdHlcbiAgICAgICk7XG4gICAgfTtcblxuICAgIGltZy5vbmVycm9yID0gKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignRmFpbGVkIHRvIGxvYWQgaW1hZ2UnKSk7XG4gICAgaW1nLnNyYyA9IFVSTC5jcmVhdGVPYmplY3RVUkwoZmlsZSk7XG4gIH0pO1xufTtcblxuLyoqXG4gKiBDcmVhdGUgYSB0aHVtYm5haWwgdmVyc2lvbiBvZiBhbiBpbWFnZVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlVGh1bWJuYWlsID0gYXN5bmMgKFxuICBmaWxlOiBGaWxlLFxuICBzaXplOiBudW1iZXIgPSAyMDBcbik6IFByb21pc2U8eyB1cmw6IHN0cmluZzsgd2lkdGg6IG51bWJlcjsgaGVpZ2h0OiBudW1iZXIgfT4gPT4ge1xuICBjb25zdCBvcHRpbWl6ZWQgPSBhd2FpdCBvcHRpbWl6ZUltYWdlKGZpbGUsIHtcbiAgICBtYXhXaWR0aDogc2l6ZSxcbiAgICBtYXhIZWlnaHQ6IHNpemUsXG4gICAgcXVhbGl0eTogMC43LFxuICAgIGZvcm1hdDogJ2pwZWcnXG4gIH0pO1xuXG4gIHJldHVybiB7XG4gICAgdXJsOiBvcHRpbWl6ZWQudXJsLFxuICAgIHdpZHRoOiBvcHRpbWl6ZWQud2lkdGgsXG4gICAgaGVpZ2h0OiBvcHRpbWl6ZWQuaGVpZ2h0XG4gIH07XG59O1xuXG4vKipcbiAqIFByZWxvYWQgaW1hZ2VzIGZvciBiZXR0ZXIgcGVyZm9ybWFuY2VcbiAqL1xuZXhwb3J0IGNvbnN0IHByZWxvYWRJbWFnZSA9IChzcmM6IHN0cmluZyk6IFByb21pc2U8SFRNTEltYWdlRWxlbWVudD4gPT4ge1xuICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgIGNvbnN0IGltZyA9IG5ldyBJbWFnZSgpO1xuICAgIGltZy5vbmxvYWQgPSAoKSA9PiByZXNvbHZlKGltZyk7XG4gICAgaW1nLm9uZXJyb3IgPSByZWplY3Q7XG4gICAgaW1nLnNyYyA9IHNyYztcbiAgfSk7XG59O1xuXG4vKipcbiAqIEJhdGNoIHByZWxvYWQgbXVsdGlwbGUgaW1hZ2VzXG4gKi9cbmV4cG9ydCBjb25zdCBwcmVsb2FkSW1hZ2VzID0gYXN5bmMgKHVybHM6IHN0cmluZ1tdKTogUHJvbWlzZTxIVE1MSW1hZ2VFbGVtZW50W10+ID0+IHtcbiAgY29uc3QgcHJvbWlzZXMgPSB1cmxzLm1hcCh1cmwgPT4gcHJlbG9hZEltYWdlKHVybCkpO1xuICByZXR1cm4gUHJvbWlzZS5hbGwocHJvbWlzZXMpO1xufTtcblxuLyoqXG4gKiBHZXQgaW1hZ2UgZGltZW5zaW9ucyB3aXRob3V0IGxvYWRpbmcgdGhlIGZ1bGwgaW1hZ2VcbiAqL1xuZXhwb3J0IGNvbnN0IGdldEltYWdlRGltZW5zaW9ucyA9IChmaWxlOiBGaWxlKTogUHJvbWlzZTx7IHdpZHRoOiBudW1iZXI7IGhlaWdodDogbnVtYmVyIH0+ID0+IHtcbiAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICBjb25zdCBpbWcgPSBuZXcgSW1hZ2UoKTtcbiAgICBpbWcub25sb2FkID0gKCkgPT4ge1xuICAgICAgcmVzb2x2ZSh7IHdpZHRoOiBpbWcud2lkdGgsIGhlaWdodDogaW1nLmhlaWdodCB9KTtcbiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoaW1nLnNyYyk7XG4gICAgfTtcbiAgICBpbWcub25lcnJvciA9IHJlamVjdDtcbiAgICBpbWcuc3JjID0gVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKTtcbiAgfSk7XG59O1xuXG4vKipcbiAqIENvbnZlcnQgaW1hZ2UgdG8gZGlmZmVyZW50IGZvcm1hdFxuICovXG5leHBvcnQgY29uc3QgY29udmVydEltYWdlRm9ybWF0ID0gYXN5bmMgKFxuICBmaWxlOiBGaWxlLFxuICB0YXJnZXRGb3JtYXQ6ICdqcGVnJyB8ICdwbmcnIHwgJ3dlYnAnLFxuICBxdWFsaXR5OiBudW1iZXIgPSAwLjhcbik6IFByb21pc2U8RmlsZT4gPT4ge1xuICBjb25zdCBvcHRpbWl6ZWQgPSBhd2FpdCBvcHRpbWl6ZUltYWdlKGZpbGUsIHtcbiAgICBmb3JtYXQ6IHRhcmdldEZvcm1hdCxcbiAgICBxdWFsaXR5LFxuICAgIG1heFdpZHRoOiA0MDk2LFxuICAgIG1heEhlaWdodDogNDA5NlxuICB9KTtcblxuICByZXR1cm4gb3B0aW1pemVkLmZpbGU7XG59O1xuXG4vKipcbiAqIFBlcmZvcm1hbmNlIG1vbml0b3JpbmcgdXRpbGl0aWVzXG4gKi9cbmV4cG9ydCBjb25zdCBtZWFzdXJlSW1hZ2VQcm9jZXNzaW5nVGltZSA9IGFzeW5jIDxUPihcbiAgb3BlcmF0aW9uOiAoKSA9PiBQcm9taXNlPFQ+LFxuICBvcGVyYXRpb25OYW1lOiBzdHJpbmdcbik6IFByb21pc2U8VD4gPT4ge1xuICBjb25zdCBzdGFydFRpbWUgPSBwZXJmb3JtYW5jZS5ub3coKTtcbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgb3BlcmF0aW9uKCk7XG4gICAgY29uc3QgZW5kVGltZSA9IHBlcmZvcm1hbmNlLm5vdygpO1xuICAgIGNvbnN0IGR1cmF0aW9uID0gZW5kVGltZSAtIHN0YXJ0VGltZTtcbiAgICBcbiAgICBjb25zb2xlLmxvZyhgSW1hZ2Ugb3BlcmF0aW9uIFwiJHtvcGVyYXRpb25OYW1lfVwiIHRvb2sgJHtkdXJhdGlvbi50b0ZpeGVkKDIpfW1zYCk7XG4gICAgXG4gICAgLy8gUmVwb3J0IHRvIENvcmUgV2ViIFZpdGFscyBpZiBhdmFpbGFibGVcbiAgICBpZiAoJ1BlcmZvcm1hbmNlT2JzZXJ2ZXInIGluIHdpbmRvdykge1xuICAgICAgLy8gVGhpcyB3b3VsZCBpbnRlZ3JhdGUgd2l0aCByZWFsIHBlcmZvcm1hbmNlIG1vbml0b3JpbmdcbiAgICAgIGNvbnNvbGUubG9nKGBQZXJmb3JtYW5jZTogJHtvcGVyYXRpb25OYW1lfSAtICR7ZHVyYXRpb259bXNgKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zdCBlbmRUaW1lID0gcGVyZm9ybWFuY2Uubm93KCk7XG4gICAgY29uc3QgZHVyYXRpb24gPSBlbmRUaW1lIC0gc3RhcnRUaW1lO1xuICAgIGNvbnNvbGUuZXJyb3IoYEltYWdlIG9wZXJhdGlvbiBcIiR7b3BlcmF0aW9uTmFtZX1cIiBmYWlsZWQgYWZ0ZXIgJHtkdXJhdGlvbi50b0ZpeGVkKDIpfW1zOmAsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufTtcblxuLyoqXG4gKiBNZW1vcnktZWZmaWNpZW50IGltYWdlIHByb2Nlc3Npbmcgd2l0aCBjbGVhbnVwXG4gKi9cbmV4cG9ydCBjb25zdCBwcm9jZXNzSW1hZ2VXaXRoQ2xlYW51cCA9IGFzeW5jIDxUPihcbiAgZmlsZTogRmlsZSxcbiAgcHJvY2Vzc29yOiAodXJsOiBzdHJpbmcpID0+IFByb21pc2U8VD5cbik6IFByb21pc2U8VD4gPT4ge1xuICBjb25zdCB1cmwgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpO1xuICBcbiAgdHJ5IHtcbiAgICByZXR1cm4gYXdhaXQgcHJvY2Vzc29yKHVybCk7XG4gIH0gZmluYWxseSB7XG4gICAgLy8gQ2xlYW4gdXAgb2JqZWN0IFVSTCB0byBwcmV2ZW50IG1lbW9yeSBsZWFrc1xuICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgfVxufTtcbiJdLCJuYW1lcyI6WyJvcHRpbWl6ZUltYWdlIiwiZmlsZSIsIm9wdGlvbnMiLCJtYXhXaWR0aCIsIm1heEhlaWdodCIsInF1YWxpdHkiLCJmb3JtYXQiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImltZyIsIkltYWdlIiwiY2FudmFzIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiY3R4IiwiZ2V0Q29udGV4dCIsIkVycm9yIiwib25sb2FkIiwid2lkdGgiLCJoZWlnaHQiLCJhc3BlY3RSYXRpbyIsIk1hdGgiLCJtaW4iLCJkcmF3SW1hZ2UiLCJ0b0Jsb2IiLCJibG9iIiwib3B0aW1pemVkRmlsZSIsIkZpbGUiLCJuYW1lIiwidHlwZSIsImxhc3RNb2RpZmllZCIsIkRhdGUiLCJub3ciLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJyb3VuZCIsIm9uZXJyb3IiLCJzcmMiLCJjcmVhdGVUaHVtYm5haWwiLCJzaXplIiwib3B0aW1pemVkIiwicHJlbG9hZEltYWdlIiwicHJlbG9hZEltYWdlcyIsInVybHMiLCJwcm9taXNlcyIsIm1hcCIsImFsbCIsImdldEltYWdlRGltZW5zaW9ucyIsInJldm9rZU9iamVjdFVSTCIsImNvbnZlcnRJbWFnZUZvcm1hdCIsInRhcmdldEZvcm1hdCIsIm1lYXN1cmVJbWFnZVByb2Nlc3NpbmdUaW1lIiwib3BlcmF0aW9uIiwib3BlcmF0aW9uTmFtZSIsInN0YXJ0VGltZSIsInBlcmZvcm1hbmNlIiwicmVzdWx0IiwiZW5kVGltZSIsImR1cmF0aW9uIiwiY29uc29sZSIsImxvZyIsInRvRml4ZWQiLCJ3aW5kb3ciLCJlcnJvciIsInByb2Nlc3NJbWFnZVdpdGhDbGVhbnVwIiwicHJvY2Vzc29yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/imageOptimization.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: {\n        default: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        template: \"%s | Photo Collage Maker\"\n    },\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly.\",\n    keywords: [\n        \"photo collage maker\",\n        \"collage creator\",\n        \"photo editor\",\n        \"free collage maker\",\n        \"online photo collage\",\n        \"picture collage\",\n        \"photo montage\",\n        \"template collage\",\n        \"heart collage\",\n        \"grid collage\",\n        \"letter collage\",\n        \"number collage\"\n    ],\n    authors: [\n        {\n            name: \"Photo Collage Maker Team\"\n        }\n    ],\n    creator: \"Photo Collage Maker\",\n    publisher: \"Photo Collage Maker\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://photocollagemakerpro.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n        siteName: \"Photo Collage Maker\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Photo Collage Maker - Create Beautiful Collages Online\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@photocollagemakerpro\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\",\n        yandex: \"your-yandex-verification-code\",\n        yahoo: \"your-yahoo-verification-code\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 5,\n    userScalable: true,\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#000000\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                name: \"Photo Collage Maker\",\n                                description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n                                url: \"https://photocollagemakerpro.com\",\n                                applicationCategory: \"DesignApplication\",\n                                operatingSystem: \"Web Browser\",\n                                offers: {\n                                    \"@type\": \"Offer\",\n                                    price: \"0\",\n                                    priceCurrency: \"USD\",\n                                    availability: \"https://schema.org/InStock\"\n                                },\n                                featureList: [\n                                    \"Free photo collage maker\",\n                                    \"Multiple template categories\",\n                                    \"Heart-shaped collages\",\n                                    \"Grid layouts\",\n                                    \"Letter and number templates\",\n                                    \"Drag and drop interface\",\n                                    \"High-quality downloads\",\n                                    \"No registration required\"\n                                ],\n                                screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n                                softwareVersion: \"1.0.0\",\n                                aggregateRating: {\n                                    \"@type\": \"AggregateRating\",\n                                    ratingValue: \"4.8\",\n                                    ratingCount: \"1250\",\n                                    bestRating: \"5\",\n                                    worstRating: \"1\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBSU1BO0FBRmdCO0FBSWYsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87UUFDTEMsU0FBUztRQUNUQyxVQUFVO0lBQ1o7SUFDQUMsYUFBYTtJQUNiQyxVQUFVO1FBQ1I7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFDREMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBMkI7S0FBRTtJQUMvQ0MsU0FBUztJQUNUQyxXQUFXO0lBQ1hDLGlCQUFpQjtRQUNmQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsV0FBVztJQUNiO0lBQ0FDLGNBQWMsSUFBSUMsSUFBSTtJQUN0QkMsWUFBWTtRQUNWQyxXQUFXO0lBQ2I7SUFDQUMsV0FBVztRQUNUQyxNQUFNO1FBQ05DLFFBQVE7UUFDUkMsS0FBSztRQUNMcEIsT0FBTztRQUNQRyxhQUFhO1FBQ2JrQixVQUFVO1FBQ1ZDLFFBQVE7WUFDTjtnQkFDRUYsS0FBSztnQkFDTEcsT0FBTztnQkFDUEMsUUFBUTtnQkFDUkMsS0FBSztZQUNQO1NBQ0Q7SUFDSDtJQUNBQyxTQUFTO1FBQ1BDLE1BQU07UUFDTjNCLE9BQU87UUFDUEcsYUFBYTtRQUNibUIsUUFBUTtZQUFDO1NBQXFCO1FBQzlCZixTQUFTO0lBQ1g7SUFDQXFCLFFBQVE7UUFDTkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLFdBQVc7WUFDVEYsT0FBTztZQUNQQyxRQUFRO1lBQ1IscUJBQXFCLENBQUM7WUFDdEIscUJBQXFCO1lBQ3JCLGVBQWUsQ0FBQztRQUNsQjtJQUNGO0lBQ0FFLGNBQWM7UUFDWkMsUUFBUTtRQUNSQyxRQUFRO1FBQ1JDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFTSxNQUFNQyxXQUFxQjtJQUNoQ2IsT0FBTztJQUNQYyxjQUFjO0lBQ2RDLGNBQWM7SUFDZEMsY0FBYztJQUNkQyxZQUFZO1FBQ1Y7WUFBRUMsT0FBTztZQUFpQ0MsT0FBTztRQUFVO1FBQzNEO1lBQUVELE9BQU87WUFBZ0NDLE9BQU87UUFBVTtLQUMzRDtBQUNILEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVOzswQkFDeEIsOERBQUNDOztrQ0FFQyw4REFBQ0M7d0JBQ0MvQixNQUFLO3dCQUNMZ0MseUJBQXlCOzRCQUN2QkMsUUFBUUMsS0FBS0MsU0FBUyxDQUFDO2dDQUNyQixZQUFZO2dDQUNaLFNBQVM7Z0NBQ1QvQyxNQUFNO2dDQUNOSCxhQUFhO2dDQUNiaUIsS0FBSztnQ0FDTGtDLHFCQUFxQjtnQ0FDckJDLGlCQUFpQjtnQ0FDakJDLFFBQVE7b0NBQ04sU0FBUztvQ0FDVEMsT0FBTztvQ0FDUEMsZUFBZTtvQ0FDZkMsY0FBYztnQ0FDaEI7Z0NBQ0FDLGFBQWE7b0NBQ1g7b0NBQ0E7b0NBQ0E7b0NBQ0E7b0NBQ0E7b0NBQ0E7b0NBQ0E7b0NBQ0E7aUNBQ0Q7Z0NBQ0RDLFlBQVk7Z0NBQ1pDLGlCQUFpQjtnQ0FDakJDLGlCQUFpQjtvQ0FDZixTQUFTO29DQUNUQyxhQUFhO29DQUNiQyxhQUFhO29DQUNiQyxZQUFZO29DQUNaQyxhQUFhO2dDQUNmOzRCQUNGO3dCQUNGOzs7Ozs7a0NBSUYsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFhQyxNQUFLOzs7Ozs7a0NBQzVCLDhEQUFDRjt3QkFBS0MsS0FBSTt3QkFBYUMsTUFBSzt3QkFBNEJDLGFBQVk7Ozs7OztrQ0FHcEUsOERBQUNIO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLO3dCQUFlRSxPQUFNOzs7Ozs7a0NBQzNDLDhEQUFDSjt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBWXBELE1BQUs7Ozs7OztrQ0FDdkMsOERBQUNrRDt3QkFBS0MsS0FBSTt3QkFBbUJDLE1BQUs7Ozs7OztrQ0FDbEMsOERBQUNGO3dCQUFLQyxLQUFJO3dCQUFXQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRTVCLDhEQUFDRztnQkFBSzFCLFdBQVcsQ0FBQyxFQUFFakQsNEtBQWUsQ0FBQyxZQUFZLENBQUM7MEJBQy9DLDRFQUFDNEU7b0JBQUlDLElBQUc7OEJBQ0wvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSwgVmlld3BvcnQgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddLCBkaXNwbGF5OiAnc3dhcCcgfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IHtcbiAgICBkZWZhdWx0OiAnUGhvdG8gQ29sbGFnZSBNYWtlciAtIENyZWF0ZSBCZWF1dGlmdWwgQ29sbGFnZXMgT25saW5lIEZyZWUnLFxuICAgIHRlbXBsYXRlOiAnJXMgfCBQaG90byBDb2xsYWdlIE1ha2VyJ1xuICB9LFxuICBkZXNjcmlwdGlvbjogJ0NyZWF0ZSBzdHVubmluZyBwaG90byBjb2xsYWdlcyBvbmxpbmUgZm9yIGZyZWUgd2l0aCBvdXIgZWFzeS10by11c2UgZWRpdG9yLiBDaG9vc2UgZnJvbSBoZWFydCBzaGFwZXMsIGdyaWRzLCBsZXR0ZXJzLCBudW1iZXJzIGFuZCBjdXN0b20gdGVtcGxhdGVzLiBVcGxvYWQgcGhvdG9zIGFuZCBkb3dubG9hZCBoaWdoLXF1YWxpdHkgY29sbGFnZXMgaW5zdGFudGx5LicsXG4gIGtleXdvcmRzOiBbXG4gICAgJ3Bob3RvIGNvbGxhZ2UgbWFrZXInLFxuICAgICdjb2xsYWdlIGNyZWF0b3InLFxuICAgICdwaG90byBlZGl0b3InLFxuICAgICdmcmVlIGNvbGxhZ2UgbWFrZXInLFxuICAgICdvbmxpbmUgcGhvdG8gY29sbGFnZScsXG4gICAgJ3BpY3R1cmUgY29sbGFnZScsXG4gICAgJ3Bob3RvIG1vbnRhZ2UnLFxuICAgICd0ZW1wbGF0ZSBjb2xsYWdlJyxcbiAgICAnaGVhcnQgY29sbGFnZScsXG4gICAgJ2dyaWQgY29sbGFnZScsXG4gICAgJ2xldHRlciBjb2xsYWdlJyxcbiAgICAnbnVtYmVyIGNvbGxhZ2UnXG4gIF0sXG4gIGF1dGhvcnM6IFt7IG5hbWU6ICdQaG90byBDb2xsYWdlIE1ha2VyIFRlYW0nIH1dLFxuICBjcmVhdG9yOiAnUGhvdG8gQ29sbGFnZSBNYWtlcicsXG4gIHB1Ymxpc2hlcjogJ1Bob3RvIENvbGxhZ2UgTWFrZXInLFxuICBmb3JtYXREZXRlY3Rpb246IHtcbiAgICBlbWFpbDogZmFsc2UsXG4gICAgYWRkcmVzczogZmFsc2UsXG4gICAgdGVsZXBob25lOiBmYWxzZSxcbiAgfSxcbiAgbWV0YWRhdGFCYXNlOiBuZXcgVVJMKCdodHRwczovL3Bob3RvY29sbGFnZW1ha2VycHJvLmNvbScpLFxuICBhbHRlcm5hdGVzOiB7XG4gICAgY2Fub25pY2FsOiAnLycsXG4gIH0sXG4gIG9wZW5HcmFwaDoge1xuICAgIHR5cGU6ICd3ZWJzaXRlJyxcbiAgICBsb2NhbGU6ICdlbl9VUycsXG4gICAgdXJsOiAnLycsXG4gICAgdGl0bGU6ICdQaG90byBDb2xsYWdlIE1ha2VyIC0gQ3JlYXRlIEJlYXV0aWZ1bCBDb2xsYWdlcyBPbmxpbmUgRnJlZScsXG4gICAgZGVzY3JpcHRpb246ICdDcmVhdGUgc3R1bm5pbmcgcGhvdG8gY29sbGFnZXMgb25saW5lIGZvciBmcmVlIHdpdGggb3VyIGVhc3ktdG8tdXNlIGVkaXRvci4gQ2hvb3NlIGZyb20gaGVhcnQgc2hhcGVzLCBncmlkcywgbGV0dGVycywgbnVtYmVycyBhbmQgY3VzdG9tIHRlbXBsYXRlcy4nLFxuICAgIHNpdGVOYW1lOiAnUGhvdG8gQ29sbGFnZSBNYWtlcicsXG4gICAgaW1hZ2VzOiBbXG4gICAgICB7XG4gICAgICAgIHVybDogJy9vZy1pbWFnZS5qcGcnLFxuICAgICAgICB3aWR0aDogMTIwMCxcbiAgICAgICAgaGVpZ2h0OiA2MzAsXG4gICAgICAgIGFsdDogJ1Bob3RvIENvbGxhZ2UgTWFrZXIgLSBDcmVhdGUgQmVhdXRpZnVsIENvbGxhZ2VzIE9ubGluZScsXG4gICAgICB9LFxuICAgIF0sXG4gIH0sXG4gIHR3aXR0ZXI6IHtcbiAgICBjYXJkOiAnc3VtbWFyeV9sYXJnZV9pbWFnZScsXG4gICAgdGl0bGU6ICdQaG90byBDb2xsYWdlIE1ha2VyIC0gQ3JlYXRlIEJlYXV0aWZ1bCBDb2xsYWdlcyBPbmxpbmUgRnJlZScsXG4gICAgZGVzY3JpcHRpb246ICdDcmVhdGUgc3R1bm5pbmcgcGhvdG8gY29sbGFnZXMgb25saW5lIGZvciBmcmVlIHdpdGggb3VyIGVhc3ktdG8tdXNlIGVkaXRvci4gQ2hvb3NlIGZyb20gaGVhcnQgc2hhcGVzLCBncmlkcywgbGV0dGVycywgbnVtYmVycyBhbmQgY3VzdG9tIHRlbXBsYXRlcy4nLFxuICAgIGltYWdlczogWycvdHdpdHRlci1pbWFnZS5qcGcnXSxcbiAgICBjcmVhdG9yOiAnQHBob3RvY29sbGFnZW1ha2VycHJvJyxcbiAgfSxcbiAgcm9ib3RzOiB7XG4gICAgaW5kZXg6IHRydWUsXG4gICAgZm9sbG93OiB0cnVlLFxuICAgIGdvb2dsZUJvdDoge1xuICAgICAgaW5kZXg6IHRydWUsXG4gICAgICBmb2xsb3c6IHRydWUsXG4gICAgICAnbWF4LXZpZGVvLXByZXZpZXcnOiAtMSxcbiAgICAgICdtYXgtaW1hZ2UtcHJldmlldyc6ICdsYXJnZScsXG4gICAgICAnbWF4LXNuaXBwZXQnOiAtMSxcbiAgICB9LFxuICB9LFxuICB2ZXJpZmljYXRpb246IHtcbiAgICBnb29nbGU6ICd5b3VyLWdvb2dsZS12ZXJpZmljYXRpb24tY29kZScsXG4gICAgeWFuZGV4OiAneW91ci15YW5kZXgtdmVyaWZpY2F0aW9uLWNvZGUnLFxuICAgIHlhaG9vOiAneW91ci15YWhvby12ZXJpZmljYXRpb24tY29kZScsXG4gIH0sXG59XG5cbmV4cG9ydCBjb25zdCB2aWV3cG9ydDogVmlld3BvcnQgPSB7XG4gIHdpZHRoOiAnZGV2aWNlLXdpZHRoJyxcbiAgaW5pdGlhbFNjYWxlOiAxLFxuICBtYXhpbXVtU2NhbGU6IDUsXG4gIHVzZXJTY2FsYWJsZTogdHJ1ZSxcbiAgdGhlbWVDb2xvcjogW1xuICAgIHsgbWVkaWE6ICcocHJlZmVycy1jb2xvci1zY2hlbWU6IGxpZ2h0KScsIGNvbG9yOiAnI2ZmZmZmZicgfSxcbiAgICB7IG1lZGlhOiAnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScsIGNvbG9yOiAnIzAwMDAwMCcgfSxcbiAgXSxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCIgY2xhc3NOYW1lPVwic2Nyb2xsLXNtb290aFwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIHsvKiBTdHJ1Y3R1cmVkIERhdGEgKi99XG4gICAgICAgIDxzY3JpcHRcbiAgICAgICAgICB0eXBlPVwiYXBwbGljYXRpb24vbGQranNvblwiXG4gICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICAgICAgICAgIF9faHRtbDogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgICAgICAnQGNvbnRleHQnOiAnaHR0cHM6Ly9zY2hlbWEub3JnJyxcbiAgICAgICAgICAgICAgJ0B0eXBlJzogJ1dlYkFwcGxpY2F0aW9uJyxcbiAgICAgICAgICAgICAgbmFtZTogJ1Bob3RvIENvbGxhZ2UgTWFrZXInLFxuICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogJ0NyZWF0ZSBzdHVubmluZyBwaG90byBjb2xsYWdlcyBvbmxpbmUgZm9yIGZyZWUgd2l0aCBvdXIgZWFzeS10by11c2UgZWRpdG9yLiBDaG9vc2UgZnJvbSBoZWFydCBzaGFwZXMsIGdyaWRzLCBsZXR0ZXJzLCBudW1iZXJzIGFuZCBjdXN0b20gdGVtcGxhdGVzLicsXG4gICAgICAgICAgICAgIHVybDogJ2h0dHBzOi8vcGhvdG9jb2xsYWdlbWFrZXJwcm8uY29tJyxcbiAgICAgICAgICAgICAgYXBwbGljYXRpb25DYXRlZ29yeTogJ0Rlc2lnbkFwcGxpY2F0aW9uJyxcbiAgICAgICAgICAgICAgb3BlcmF0aW5nU3lzdGVtOiAnV2ViIEJyb3dzZXInLFxuICAgICAgICAgICAgICBvZmZlcnM6IHtcbiAgICAgICAgICAgICAgICAnQHR5cGUnOiAnT2ZmZXInLFxuICAgICAgICAgICAgICAgIHByaWNlOiAnMCcsXG4gICAgICAgICAgICAgICAgcHJpY2VDdXJyZW5jeTogJ1VTRCcsXG4gICAgICAgICAgICAgICAgYXZhaWxhYmlsaXR5OiAnaHR0cHM6Ly9zY2hlbWEub3JnL0luU3RvY2snXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIGZlYXR1cmVMaXN0OiBbXG4gICAgICAgICAgICAgICAgJ0ZyZWUgcGhvdG8gY29sbGFnZSBtYWtlcicsXG4gICAgICAgICAgICAgICAgJ011bHRpcGxlIHRlbXBsYXRlIGNhdGVnb3JpZXMnLFxuICAgICAgICAgICAgICAgICdIZWFydC1zaGFwZWQgY29sbGFnZXMnLFxuICAgICAgICAgICAgICAgICdHcmlkIGxheW91dHMnLFxuICAgICAgICAgICAgICAgICdMZXR0ZXIgYW5kIG51bWJlciB0ZW1wbGF0ZXMnLFxuICAgICAgICAgICAgICAgICdEcmFnIGFuZCBkcm9wIGludGVyZmFjZScsXG4gICAgICAgICAgICAgICAgJ0hpZ2gtcXVhbGl0eSBkb3dubG9hZHMnLFxuICAgICAgICAgICAgICAgICdObyByZWdpc3RyYXRpb24gcmVxdWlyZWQnXG4gICAgICAgICAgICAgIF0sXG4gICAgICAgICAgICAgIHNjcmVlbnNob3Q6ICdodHRwczovL3Bob3RvY29sbGFnZW1ha2VycHJvLmNvbS9zY3JlZW5zaG90LmpwZycsXG4gICAgICAgICAgICAgIHNvZnR3YXJlVmVyc2lvbjogJzEuMC4wJyxcbiAgICAgICAgICAgICAgYWdncmVnYXRlUmF0aW5nOiB7XG4gICAgICAgICAgICAgICAgJ0B0eXBlJzogJ0FnZ3JlZ2F0ZVJhdGluZycsXG4gICAgICAgICAgICAgICAgcmF0aW5nVmFsdWU6ICc0LjgnLFxuICAgICAgICAgICAgICAgIHJhdGluZ0NvdW50OiAnMTI1MCcsXG4gICAgICAgICAgICAgICAgYmVzdFJhdGluZzogJzUnLFxuICAgICAgICAgICAgICAgIHdvcnN0UmF0aW5nOiAnMSdcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuXG4gICAgICAgIHsvKiBQcmVjb25uZWN0IHRvIGV4dGVybmFsIGRvbWFpbnMgKi99XG4gICAgICAgIDxsaW5rIHJlbD1cInByZWNvbm5lY3RcIiBocmVmPVwiaHR0cHM6Ly9mb250cy5nb29nbGVhcGlzLmNvbVwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cInByZWNvbm5lY3RcIiBocmVmPVwiaHR0cHM6Ly9mb250cy5nc3RhdGljLmNvbVwiIGNyb3NzT3JpZ2luPVwiYW5vbnltb3VzXCIgLz5cblxuICAgICAgICB7LyogRmF2aWNvbiBhbmQgYXBwIGljb25zICovfVxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIHNpemVzPVwiYW55XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvaWNvbi5zdmdcIiB0eXBlPVwiaW1hZ2Uvc3ZnK3htbFwiIC8+XG4gICAgICAgIDxsaW5rIHJlbD1cImFwcGxlLXRvdWNoLWljb25cIiBocmVmPVwiL2FwcGxlLXRvdWNoLWljb24ucG5nXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwibWFuaWZlc3RcIiBocmVmPVwiL21hbmlmZXN0Lmpzb25cIiAvPlxuICAgICAgPC9oZWFkPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHtpbnRlci5jbGFzc05hbWV9IGFudGlhbGlhc2VkYH0+XG4gICAgICAgIDxkaXYgaWQ9XCJyb290XCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZWZhdWx0IiwidGVtcGxhdGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJjcmVhdG9yIiwicHVibGlzaGVyIiwiZm9ybWF0RGV0ZWN0aW9uIiwiZW1haWwiLCJhZGRyZXNzIiwidGVsZXBob25lIiwibWV0YWRhdGFCYXNlIiwiVVJMIiwiYWx0ZXJuYXRlcyIsImNhbm9uaWNhbCIsIm9wZW5HcmFwaCIsInR5cGUiLCJsb2NhbGUiLCJ1cmwiLCJzaXRlTmFtZSIsImltYWdlcyIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwidHdpdHRlciIsImNhcmQiLCJyb2JvdHMiLCJpbmRleCIsImZvbGxvdyIsImdvb2dsZUJvdCIsInZlcmlmaWNhdGlvbiIsImdvb2dsZSIsInlhbmRleCIsInlhaG9vIiwidmlld3BvcnQiLCJpbml0aWFsU2NhbGUiLCJtYXhpbXVtU2NhbGUiLCJ1c2VyU2NhbGFibGUiLCJ0aGVtZUNvbG9yIiwibWVkaWEiLCJjb2xvciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwiaGVhZCIsInNjcmlwdCIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiSlNPTiIsInN0cmluZ2lmeSIsImFwcGxpY2F0aW9uQ2F0ZWdvcnkiLCJvcGVyYXRpbmdTeXN0ZW0iLCJvZmZlcnMiLCJwcmljZSIsInByaWNlQ3VycmVuY3kiLCJhdmFpbGFiaWxpdHkiLCJmZWF0dXJlTGlzdCIsInNjcmVlbnNob3QiLCJzb2Z0d2FyZVZlcnNpb24iLCJhZ2dyZWdhdGVSYXRpbmciLCJyYXRpbmdWYWx1ZSIsInJhdGluZ0NvdW50IiwiYmVzdFJhdGluZyIsIndvcnN0UmF0aW5nIiwibGluayIsInJlbCIsImhyZWYiLCJjcm9zc09yaWdpbiIsInNpemVzIiwiYm9keSIsImRpdiIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(rsc)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Lazy load the UnifiedEditor for better performance\nconst UnifiedEditor = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_UnifiedEditor_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/UnifiedEditor */ \"(rsc)/./src/components/UnifiedEditor.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx -> \" + \"@/components/UnifiedEditor\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-700\",\n                        children: \"Loading Photo Collage Maker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mt-2\",\n                        children: \"Preparing your creative workspace\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 7,\n            columnNumber: 5\n        }, undefined),\n    ssr: false // Disable SSR for this component since it uses browser APIs\n});\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-700\",\n                            children: \"Loading Photo Collage Maker...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mt-2\",\n                            children: \"Preparing your creative workspace\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedEditor, {}, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();