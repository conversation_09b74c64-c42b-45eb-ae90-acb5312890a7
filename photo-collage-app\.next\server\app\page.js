/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CSimpleEditor.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CSimpleEditor.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SimpleEditor.tsx */ \"(ssr)/./src/components/SimpleEditor.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDc3JjJTVDY29tcG9uZW50cyU1Q1NpbXBsZUVkaXRvci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvP2QzNjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxXb3JrU3BhY2VcXFxccGhvdG9Db2xsYWdlXFxcXHBob3RvLWNvbGxhZ2UtYXBwXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFNpbXBsZUVkaXRvci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CSimpleEditor.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Breadcrumb.tsx":
/*!***************************************!*\
  !*** ./src/components/Breadcrumb.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Breadcrumb = ({ items, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        \"aria-label\": \"Breadcrumb\",\n        className: `flex items-center space-x-1 text-sm text-gray-600 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n            className: \"flex items-center space-x-1\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex items-center\",\n                    children: [\n                        index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4 text-gray-400 mx-1\",\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, undefined),\n                        item.current ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-900\",\n                            \"aria-current\": \"page\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 15\n                        }, undefined) : item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: \"hover:text-gray-900 transition-colors\",\n                            children: [\n                                index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-1 inline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 33\n                                }, undefined),\n                                item.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Breadcrumb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SimpleEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/SimpleEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(ssr)/./src/data/templates.ts\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _StructuredData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./StructuredData */ \"(ssr)/./src/components/StructuredData.tsx\");\n/* harmony import */ var _Breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Breadcrumb */ \"(ssr)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst SimpleEditor = ({ initialTemplateId })=>{\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0]);\n    const breadcrumbItems = [\n        {\n            label: \"Home\",\n            href: \"/\"\n        },\n        {\n            label: \"Photo Collage Maker\",\n            current: true\n        }\n    ];\n    const categoryIcons = {\n        grid: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        heart: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        letter: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        number: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        shape: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"WebApplication\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.WebApplicationSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"HowTo\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.HowToSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"FAQPage\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.FAQSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Breadcrumb__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            items: breadcrumbItems\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-6xl mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-800 mb-4\",\n                                children: \"Create Beautiful Photo Collages Online Free\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Design stunning photo collages with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly - no registration required!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ 100% Free\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ No Registration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ High Quality Downloads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ Multiple Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold text-gray-800\",\n                                                children: \"Choose Your Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: \"Select from our collection of beautiful collage templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"default\",\n                                        className: \"text-sm\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                children: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.map((template)=>{\n                                    const IconComponent = categoryIcons[template.category] || _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                                    const isSelected = selectedTemplate?.id === template.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: `cursor-pointer transition-all duration-200 hover:shadow-lg ${isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"\"}`,\n                                        onClick: ()=>setSelectedTemplate(template),\n                                        role: \"button\",\n                                        tabIndex: 0,\n                                        \"aria-label\": `Select ${template.name} template with ${template.slots.length} photo slots`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 114,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 font-medium\",\n                                                                children: [\n                                                                    template.slots.length,\n                                                                    \" Photos\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 text-sm\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 line-clamp-2\",\n                                                            children: template.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs capitalize\",\n                                                                    children: template.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-600 text-xs font-medium\",\n                                                                    children: \"✓ Selected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, template.id, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                            children: [\n                                                \"Ready to Create: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: selectedTemplate.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: selectedTemplate.slots.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Photo Slots\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: selectedTemplate.canvasWidth\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Width (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: selectedTemplate.canvasHeight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Height (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-orange-600 capitalize\",\n                                                            children: selectedTemplate.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedTemplate(null),\n                                        children: \"Choose Different Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        children: \"Start Creating Collage →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"How It Works\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600 font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Choose Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Select from heart shapes, grids, letters, and numbers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Drag and drop or click to upload your favorite photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-purple-600 font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Arrange & Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Position, scale, and rotate photos to perfect your collage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-600 font-bold\",\n                                                    children: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Save your high-quality collage as PNG image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"Why Choose Our Collage Maker?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"100% Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Create unlimited collages without any cost or hidden fees\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Multiple Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Choose from hearts, grids, letters, numbers, and custom shapes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"High Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Download your collages in high resolution perfect for printing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SimpleEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StructuredData.tsx":
/*!*******************************************!*\
  !*** ./src/components/StructuredData.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreativeWorkSchema: () => (/* binding */ CreativeWorkSchema),\n/* harmony export */   FAQSchema: () => (/* binding */ FAQSchema),\n/* harmony export */   HowToSchema: () => (/* binding */ HowToSchema),\n/* harmony export */   WebApplicationSchema: () => (/* binding */ WebApplicationSchema),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default,WebApplicationSchema,HowToSchema,FAQSchema,CreativeWorkSchema auto */ \nconst StructuredData = ({ type, data })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const script = document.createElement(\"script\");\n        script.type = \"application/ld+json\";\n        script.text = JSON.stringify({\n            \"@context\": \"https://schema.org\",\n            \"@type\": type,\n            ...data\n        });\n        document.head.appendChild(script);\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, [\n        type,\n        data\n    ]);\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StructuredData);\n// Predefined structured data for common use cases\nconst WebApplicationSchema = {\n    name: \"Photo Collage Maker\",\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n    url: \"https://photocollagemakerpro.com\",\n    applicationCategory: \"DesignApplication\",\n    operatingSystem: \"Web Browser\",\n    offers: {\n        \"@type\": \"Offer\",\n        price: \"0\",\n        priceCurrency: \"USD\",\n        availability: \"https://schema.org/InStock\"\n    },\n    featureList: [\n        \"Free photo collage maker\",\n        \"Multiple template categories\",\n        \"Heart-shaped collages\",\n        \"Grid layouts\",\n        \"Letter and number templates\",\n        \"Drag and drop interface\",\n        \"High-quality downloads\",\n        \"No registration required\"\n    ],\n    screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n    softwareVersion: \"1.0.0\",\n    aggregateRating: {\n        \"@type\": \"AggregateRating\",\n        ratingValue: \"4.8\",\n        ratingCount: \"1250\",\n        bestRating: \"5\",\n        worstRating: \"1\"\n    },\n    author: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker Team\"\n    },\n    publisher: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker\",\n        logo: {\n            \"@type\": \"ImageObject\",\n            url: \"https://photocollagemakerpro.com/logo.png\"\n        }\n    }\n};\nconst HowToSchema = {\n    name: \"How to Create a Photo Collage\",\n    description: \"Step-by-step guide to creating beautiful photo collages using our online tool\",\n    image: \"https://photocollagemakerpro.com/how-to-guide.jpg\",\n    totalTime: \"PT5M\",\n    estimatedCost: {\n        \"@type\": \"MonetaryAmount\",\n        currency: \"USD\",\n        value: \"0\"\n    },\n    supply: [\n        {\n            \"@type\": \"HowToSupply\",\n            name: \"Digital Photos\"\n        }\n    ],\n    tool: [\n        {\n            \"@type\": \"HowToTool\",\n            name: \"Photo Collage Maker Web Application\"\n        }\n    ],\n    step: [\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Choose Template\",\n            text: \"Select a template from our gallery of heart shapes, grids, letters, and numbers\",\n            image: \"https://photocollagemakerpro.com/step1.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Upload Photos\",\n            text: \"Upload your photos by dragging and dropping or clicking to select files\",\n            image: \"https://photocollagemakerpro.com/step2.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Arrange Photos\",\n            text: \"Drag photos into template slots and adjust position, scale, and rotation\",\n            image: \"https://photocollagemakerpro.com/step3.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Download Collage\",\n            text: \"Preview your collage and download as a high-quality PNG image\",\n            image: \"https://photocollagemakerpro.com/step4.jpg\"\n        }\n    ]\n};\nconst FAQSchema = {\n    mainEntity: [\n        {\n            \"@type\": \"Question\",\n            name: \"Is the photo collage maker free to use?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"Yes, our photo collage maker is completely free to use. You can create unlimited collages without any cost or registration required.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"What image formats are supported?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"We support JPEG, PNG, and WebP image formats. Maximum file size is 10MB per image.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"How many photos can I add to a collage?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"The number of photos depends on the template you choose. Our templates range from 4 photos to 16 photos per collage.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"Can I download my collage in high quality?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"Yes, you can download your finished collage as a high-quality PNG image that's perfect for printing or sharing online.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"Do I need to create an account?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"No account creation is required. You can start creating collages immediately without any registration.\"\n            }\n        }\n    ]\n};\nconst CreativeWorkSchema = {\n    name: \"Photo Collage Templates\",\n    description: \"Collection of creative photo collage templates including hearts, grids, letters, and shapes\",\n    creator: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker Team\"\n    },\n    dateCreated: \"2024-01-01\",\n    dateModified: new Date().toISOString().split(\"T\")[0],\n    genre: \"Design Template\",\n    keywords: [\n        \"photo collage\",\n        \"template\",\n        \"heart shape\",\n        \"grid layout\",\n        \"letter template\",\n        \"number template\",\n        \"photo arrangement\"\n    ],\n    license: \"https://creativecommons.org/licenses/by/4.0/\",\n    usageInfo: \"Free for personal and commercial use\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StructuredData.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/templates.ts":
/*!*******************************!*\
  !*** ./src/data/templates.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTemplateById: () => (/* binding */ getTemplateById),\n/* harmony export */   getTemplatesByCategory: () => (/* binding */ getTemplatesByCategory),\n/* harmony export */   templates: () => (/* binding */ templates)\n/* harmony export */ });\nconst templates = [\n    // Grid Layout Template\n    {\n        id: \"grid-4x4\",\n        name: \"4x4 Grid\",\n        description: \"Classic 16-photo grid layout\",\n        category: \"grid\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#ffffff\",\n        slots: [\n            // Row 1\n            {\n                id: \"slot-1\",\n                x: 2,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-2\",\n                x: 27,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-3\",\n                x: 52,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-4\",\n                x: 77,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 2\n            {\n                id: \"slot-5\",\n                x: 2,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-6\",\n                x: 27,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-7\",\n                x: 52,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-8\",\n                x: 77,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 3\n            {\n                id: \"slot-9\",\n                x: 2,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-10\",\n                x: 27,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-11\",\n                x: 52,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-12\",\n                x: 77,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 4\n            {\n                id: \"slot-13\",\n                x: 2,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-14\",\n                x: 27,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-15\",\n                x: 52,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-16\",\n                x: 77,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Heart Shape Template\n    {\n        id: \"heart-shape\",\n        name: \"Heart Collage\",\n        description: \"Romantic heart-shaped photo arrangement\",\n        category: \"heart\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 700,\n        backgroundColor: \"#ffe6f2\",\n        slots: [\n            // Top left curve\n            {\n                id: \"heart-1\",\n                x: 15,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-2\",\n                x: 32,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-3\",\n                x: 10,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Top right curve\n            {\n                id: \"heart-4\",\n                x: 55,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-5\",\n                x: 72,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-6\",\n                x: 78,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Center area\n            {\n                id: \"heart-7\",\n                x: 35,\n                y: 35,\n                width: 30,\n                height: 20,\n                shape: \"rectangle\"\n            },\n            // Lower sections\n            {\n                id: \"heart-8\",\n                x: 25,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-9\",\n                x: 57,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-10\",\n                x: 42,\n                y: 75,\n                width: 16,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Letter 'A' Template\n    {\n        id: \"letter-a\",\n        name: \"Letter A\",\n        description: \"Letter A shaped photo collage\",\n        category: \"letter\",\n        thumbnail: \"\",\n        canvasWidth: 600,\n        canvasHeight: 800,\n        backgroundColor: \"#f0f8ff\",\n        slots: [\n            // Top point\n            {\n                id: \"a-top\",\n                x: 45,\n                y: 5,\n                width: 10,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Upper left diagonal\n            {\n                id: \"a-ul1\",\n                x: 35,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            {\n                id: \"a-ul2\",\n                x: 25,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            // Upper right diagonal\n            {\n                id: \"a-ur1\",\n                x: 53,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            {\n                id: \"a-ur2\",\n                x: 63,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            // Cross bar\n            {\n                id: \"a-cross1\",\n                x: 35,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-cross2\",\n                x: 53,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            // Lower left leg\n            {\n                id: \"a-ll1\",\n                x: 15,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-ll2\",\n                x: 15,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Lower right leg\n            {\n                id: \"a-lr1\",\n                x: 73,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-lr2\",\n                x: 73,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Number '1' Template\n    {\n        id: \"number-1\",\n        name: \"Number 1\",\n        description: \"Number 1 shaped photo collage\",\n        category: \"number\",\n        thumbnail: \"\",\n        canvasWidth: 400,\n        canvasHeight: 800,\n        backgroundColor: \"#fff5ee\",\n        slots: [\n            // Top diagonal\n            {\n                id: \"num1-top\",\n                x: 25,\n                y: 5,\n                width: 15,\n                height: 12,\n                shape: \"rectangle\",\n                rotation: 45\n            },\n            // Main vertical line\n            {\n                id: \"num1-1\",\n                x: 40,\n                y: 15,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-2\",\n                x: 40,\n                y: 32,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-3\",\n                x: 40,\n                y: 49,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-4\",\n                x: 40,\n                y: 66,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Bottom base\n            {\n                id: \"num1-base1\",\n                x: 20,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base2\",\n                x: 40,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base3\",\n                x: 60,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Circular Pattern Template\n    {\n        id: \"circle-pattern\",\n        name: \"Circle Pattern\",\n        description: \"Circular arrangement of photos\",\n        category: \"shape\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#f5f5f5\",\n        slots: [\n            // Center circle\n            {\n                id: \"center\",\n                x: 37.5,\n                y: 37.5,\n                width: 25,\n                height: 25,\n                shape: \"circle\"\n            },\n            // Inner ring (8 photos)\n            {\n                id: \"inner-1\",\n                x: 50,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-2\",\n                x: 70,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-3\",\n                x: 80,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-4\",\n                x: 70,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-5\",\n                x: 50,\n                y: 85,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-6\",\n                x: 25,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-7\",\n                x: 15,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-8\",\n                x: 25,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            // Outer ring (4 photos)\n            {\n                id: \"outer-1\",\n                x: 50,\n                y: 2,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-2\",\n                x: 88,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-3\",\n                x: 50,\n                y: 88,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-4\",\n                x: 2,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            }\n        ]\n    }\n];\nconst getTemplateById = (id)=>{\n    return templates.find((template)=>template.id === id);\n};\nconst getTemplatesByCategory = (category)=>{\n    return templates.filter((template)=>template.category === category);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/templates.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: {\n        default: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        template: \"%s | Photo Collage Maker\"\n    },\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly.\",\n    keywords: [\n        \"photo collage maker\",\n        \"collage creator\",\n        \"photo editor\",\n        \"free collage maker\",\n        \"online photo collage\",\n        \"picture collage\",\n        \"photo montage\",\n        \"template collage\",\n        \"heart collage\",\n        \"grid collage\",\n        \"letter collage\",\n        \"number collage\"\n    ],\n    authors: [\n        {\n            name: \"Photo Collage Maker Team\"\n        }\n    ],\n    creator: \"Photo Collage Maker\",\n    publisher: \"Photo Collage Maker\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://photocollagemakerpro.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n        siteName: \"Photo Collage Maker\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Photo Collage Maker - Create Beautiful Collages Online\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@photocollagemakerpro\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\",\n        yandex: \"your-yandex-verification-code\",\n        yahoo: \"your-yahoo-verification-code\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 5,\n    userScalable: true,\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#000000\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                name: \"Photo Collage Maker\",\n                                description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n                                url: \"https://photocollagemakerpro.com\",\n                                applicationCategory: \"DesignApplication\",\n                                operatingSystem: \"Web Browser\",\n                                offers: {\n                                    \"@type\": \"Offer\",\n                                    price: \"0\",\n                                    priceCurrency: \"USD\",\n                                    availability: \"https://schema.org/InStock\"\n                                },\n                                featureList: [\n                                    \"Free photo collage maker\",\n                                    \"Multiple template categories\",\n                                    \"Heart-shaped collages\",\n                                    \"Grid layouts\",\n                                    \"Letter and number templates\",\n                                    \"Drag and drop interface\",\n                                    \"High-quality downloads\",\n                                    \"No registration required\"\n                                ],\n                                screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n                                softwareVersion: \"1.0.0\",\n                                aggregateRating: {\n                                    \"@type\": \"AggregateRating\",\n                                    ratingValue: \"4.8\",\n                                    ratingCount: \"1250\",\n                                    bestRating: \"5\",\n                                    worstRating: \"1\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_SimpleEditor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/SimpleEditor */ \"(rsc)/./src/components/SimpleEditor.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleEditor__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFEO0FBRXRDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0YsZ0VBQVlBOzs7Ozs7Ozs7O0FBR25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpbXBsZUVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvU2ltcGxlRWRpdG9yJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbj5cbiAgICAgIDxTaW1wbGVFZGl0b3IgLz5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2ltcGxlRWRpdG9yIiwiSG9tZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SimpleEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/SimpleEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\SimpleEditor.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();