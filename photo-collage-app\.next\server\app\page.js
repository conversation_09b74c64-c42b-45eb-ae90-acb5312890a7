/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CCollageApp.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CCollageApp.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CollageApp.tsx */ \"(ssr)/./src/components/CollageApp.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDc3JjJTVDY29tcG9uZW50cyU1Q0NvbGxhZ2VBcHAudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLz9lM2U0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxDb2xsYWdlQXBwLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CCollageApp.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(ssr)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(ssr)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImageUpload */ \"(ssr)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _CollageEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CollageEditor */ \"(ssr)/./src/components/CollageEditor.tsx\");\n/* harmony import */ var _CollageRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CollageRenderer */ \"(ssr)/./src/components/CollageRenderer.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst CollageApp = ()=>{\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const handlePlacedPhotosChange = (newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    };\n    const handlePreview = ()=>{\n        setCurrentState(\"preview\");\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 132,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate?.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, undefined),\n                uploadedPhotos.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                                children: [\n                                                    \"Add photos to use in your collage. You need \",\n                                                    selectedTemplate?.slots.length,\n                                                    \" photos for this template.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            onPhotosUploaded: setUploadedPhotos,\n                                            maxFiles: selectedTemplate?.slots.length || 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                children: \"Template Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                                children: selectedTemplate?.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    template: selectedTemplate,\n                                                    width: 280,\n                                                    showSlotNumbers: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate?.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Photo Slots:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate?.slots.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate?.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate?.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                            children: [\n                                                \"Add More Photos (\",\n                                                uploadedPhotos.length,\n                                                \" uploaded)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                            children: \"You can upload more photos or start arranging the ones you have.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onPhotosUploaded: setUploadedPhotos,\n                                        maxFiles: selectedTemplate?.slots.length || 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollageEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            template: selectedTemplate,\n                            uploadedPhotos: uploadedPhotos,\n                            onPlacedPhotosChange: handlePlacedPhotosChange,\n                            onPreview: handlePreview\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 147,\n            columnNumber: 5\n        }, undefined);\n    const renderPreviewScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Preview: \",\n                                        selectedTemplate?.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Your collage is ready! You can download it or go back to make changes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setCurrentState(\"editing\"),\n                                    children: \"← Back to Editor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollageRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                onDownload: (dataUrl)=>{\n                                    console.log(\"Collage downloaded:\", dataUrl.substring(0, 50) + \"...\");\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                            children: \"Collage Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate?.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Description:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate?.description\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Photos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            placedPhotos.length,\n                                                            \" of \",\n                                                            selectedTemplate?.slots.length,\n                                                            \" slots filled\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate?.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate?.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Category:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate?.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            placedPhotos.length === selectedTemplate?.slots.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-green-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Collage complete!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-600 mt-1\",\n                                                        children: \"All photo slots have been filled. You can now download your collage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            placedPhotos.length < (selectedTemplate?.slots.length || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-yellow-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"⚠\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Incomplete collage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-600 mt-1\",\n                                                        children: [\n                                                            \"Add \",\n                                                            (selectedTemplate?.slots.length || 0) - placedPhotos.length,\n                                                            \" more photos to complete your collage.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 244,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen(),\n                currentState === \"preview\" && renderPreviewScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 347,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CollageApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CollageApp.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _PhotoControls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PhotoControls */ \"(ssr)/./src/components/PhotoControls.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst CollageEditor = ({ template, uploadedPhotos, onPlacedPhotosChange, onPreview })=>{\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const updatePlacedPhoto = (photoId, updates)=>{\n        const updatedPlacedPhotos = placedPhotos.map((p)=>p.photoId === photoId ? {\n                ...p,\n                ...updates\n            } : p);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n        // Update selected photo if it's the one being updated\n        if (selectedPhoto && selectedPhoto.photoId === photoId) {\n            setSelectedPhoto({\n                ...selectedPhoto,\n                ...updates\n            });\n        }\n    };\n    const selectPhoto = (placedPhoto)=>{\n        setSelectedPhoto(placedPhoto);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const isSelected = selectedPhoto && selectedPhoto.slotId === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: `${slot.x}%`,\n            top: `${slot.y}%`,\n            width: `${slot.width}%`,\n            height: `${slot.height}%`,\n            border: `2px ${isSelected ? \"solid\" : isHovered ? \"solid\" : \"dashed\"} ${isSelected ? \"#10b981\" : isHovered ? \"#3b82f6\" : \"#9ca3af\"}`,\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = `rotate(${slot.rotation}deg)`;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            onClick: ()=>placedPhoto && selectPhoto(placedPhoto),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: `scale(${placedPhoto?.scale || 1}) rotate(${placedPhoto?.rotation || 0}deg)`,\n                            objectPosition: `${placedPhoto?.x || 50}% ${placedPhoto?.y || 50}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            removePhotoFromSlot(uploadedPhoto.id);\n                        },\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, undefined),\n                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 border-2 border-green-500 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 145,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 169,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-4 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: \"Drag photos from the sidebar into the slots below.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"md:hidden\",\n                                            children: \"Tap photos below to add them to slots.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        \"Click on placed photos to edit them.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm min-w-0 max-w-full\",\n                                        style: {\n                                            width: `${Math.min(canvasWidth, 600)}px`,\n                                            height: `${Math.min(canvasHeight, 600)}px`,\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        onClick: ()=>setSelectedPhoto(null),\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\",\n                                                selectedPhoto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-green-600\",\n                                                    children: \"• Photo selected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        setSelectedPhoto(null);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 lg:grid-cols-1 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move touch-manipulation\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 active:border-blue-500 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 group-active:bg-opacity-30 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 group-active:opacity-100 text-xs font-medium text-center px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden md:inline\",\n                                                                children: \"Drag to slot\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"md:hidden\",\n                                                                children: \"Tap to select\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: selectedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoControls__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    placedPhoto: selectedPhoto,\n                    uploadedPhoto: getUploadedPhoto(selectedPhoto.photoId),\n                    onUpdate: (updates)=>updatePlacedPhoto(selectedPhoto.photoId, updates),\n                    onRemove: ()=>{\n                        removePhotoFromSlot(selectedPhoto.photoId);\n                        setSelectedPhoto(null);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Photo Controls\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-2\",\n                                        children: \"\\uD83C\\uDF9B️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Click on a placed photo to edit its position, scale, and rotation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CollageEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Db2xsYWdlRWRpdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFZ0Q7QUFFcUI7QUFDaEM7QUFDTztBQVM1QyxNQUFNUyxnQkFBOEMsQ0FBQyxFQUNuREMsUUFBUSxFQUNSQyxjQUFjLEVBQ2RDLG9CQUFvQixFQUNwQkMsU0FBUyxFQUNWO0lBQ0MsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR2QsK0NBQVFBLENBQWdCLEVBQUU7SUFDbEUsTUFBTSxDQUFDZSxjQUFjQyxnQkFBZ0IsR0FBR2hCLCtDQUFRQSxDQUF1QjtJQUN2RSxNQUFNLENBQUNpQixhQUFhQyxlQUFlLEdBQUdsQiwrQ0FBUUEsQ0FBZ0I7SUFDOUQsTUFBTSxDQUFDbUIsZUFBZUMsaUJBQWlCLEdBQUdwQiwrQ0FBUUEsQ0FBcUI7SUFDdkUsTUFBTXFCLFlBQVlwQiw2Q0FBTUEsQ0FBaUI7SUFFekMsTUFBTXFCLGtCQUFrQixDQUFDQztRQUN2QlAsZ0JBQWdCTztJQUNsQjtJQUVBLE1BQU1DLGdCQUFnQjtRQUNwQlIsZ0JBQWdCO1FBQ2hCRSxlQUFlO0lBQ2pCO0lBRUEsTUFBTU8scUJBQXFCLENBQUNDLEdBQW9CQztRQUM5Q0QsRUFBRUUsY0FBYztRQUNoQlYsZUFBZVM7SUFDakI7SUFFQSxNQUFNRSxzQkFBc0I7UUFDMUJYLGVBQWU7SUFDakI7SUFFQSxNQUFNWSxpQkFBaUIsQ0FBQ0osR0FBb0JDO1FBQzFDRCxFQUFFRSxjQUFjO1FBQ2hCVixlQUFlO1FBRWYsSUFBSSxDQUFDSCxjQUFjO1FBRW5CLHNDQUFzQztRQUN0QyxNQUFNZ0Isc0JBQXNCbEIsYUFBYW1CLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsT0FBTyxLQUFLbkIsYUFBYW9CLEVBQUU7UUFFbEYsd0JBQXdCO1FBQ3hCLE1BQU1DLGlCQUE4QjtZQUNsQ0YsU0FBU25CLGFBQWFvQixFQUFFO1lBQ3hCUjtZQUNBVSxHQUFHO1lBQ0hDLEdBQUc7WUFDSEMsT0FBTztZQUNQQyxVQUFVO1FBQ1o7UUFFQSxNQUFNQyxvQkFBb0I7ZUFBSVY7WUFBcUJLO1NBQWU7UUFDbEV0QixnQkFBZ0IyQjtRQUNoQjlCLHFCQUFxQjhCO0lBQ3ZCO0lBRUEsTUFBTUMsc0JBQXNCLENBQUNSO1FBQzNCLE1BQU1ILHNCQUFzQmxCLGFBQWFtQixNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLE9BQU8sS0FBS0E7UUFDbkVwQixnQkFBZ0JpQjtRQUNoQnBCLHFCQUFxQm9CO0lBQ3ZCO0lBRUEsTUFBTVksaUJBQWlCLENBQUNoQjtRQUN0QixPQUFPZCxhQUFhK0IsSUFBSSxDQUFDWCxDQUFBQSxJQUFLQSxFQUFFTixNQUFNLEtBQUtBO0lBQzdDO0lBRUEsTUFBTWtCLG1CQUFtQixDQUFDWDtRQUN4QixPQUFPeEIsZUFBZWtDLElBQUksQ0FBQ1gsQ0FBQUEsSUFBS0EsRUFBRUUsRUFBRSxLQUFLRDtJQUMzQztJQUVBLE1BQU1ZLG9CQUFvQixDQUFDWixTQUFpQmE7UUFDMUMsTUFBTWhCLHNCQUFzQmxCLGFBQWFtQyxHQUFHLENBQUNmLENBQUFBLElBQzNDQSxFQUFFQyxPQUFPLEtBQUtBLFVBQVU7Z0JBQUUsR0FBR0QsQ0FBQztnQkFBRSxHQUFHYyxPQUFPO1lBQUMsSUFBSWQ7UUFFakRuQixnQkFBZ0JpQjtRQUNoQnBCLHFCQUFxQm9CO1FBRXJCLHNEQUFzRDtRQUN0RCxJQUFJWixpQkFBaUJBLGNBQWNlLE9BQU8sS0FBS0EsU0FBUztZQUN0RGQsaUJBQWlCO2dCQUFFLEdBQUdELGFBQWE7Z0JBQUUsR0FBRzRCLE9BQU87WUFBQztRQUNsRDtJQUNGO0lBRUEsTUFBTUUsY0FBYyxDQUFDQztRQUNuQjlCLGlCQUFpQjhCO0lBQ25CO0lBRUEsTUFBTUMsYUFBYSxDQUFDQztRQUNsQixNQUFNRixjQUFjUCxlQUFlUyxLQUFLakIsRUFBRTtRQUMxQyxNQUFNa0IsZ0JBQWdCSCxjQUFjTCxpQkFBaUJLLFlBQVloQixPQUFPLElBQUk7UUFDNUUsTUFBTW9CLFlBQVlyQyxnQkFBZ0JtQyxLQUFLakIsRUFBRTtRQUN6QyxNQUFNb0IsYUFBYXBDLGlCQUFpQkEsY0FBY1EsTUFBTSxLQUFLeUIsS0FBS2pCLEVBQUU7UUFFcEUsTUFBTXFCLFlBQWlDO1lBQ3JDQyxVQUFVO1lBQ1ZDLE1BQU0sQ0FBQyxFQUFFTixLQUFLZixDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ2xCc0IsS0FBSyxDQUFDLEVBQUVQLEtBQUtkLENBQUMsQ0FBQyxDQUFDLENBQUM7WUFDakJzQixPQUFPLENBQUMsRUFBRVIsS0FBS1EsS0FBSyxDQUFDLENBQUMsQ0FBQztZQUN2QkMsUUFBUSxDQUFDLEVBQUVULEtBQUtTLE1BQU0sQ0FBQyxDQUFDLENBQUM7WUFDekJDLFFBQVEsQ0FBQyxJQUFJLEVBQUVQLGFBQWEsVUFBVUQsWUFBWSxVQUFVLFNBQVMsQ0FBQyxFQUNwRUMsYUFBYSxZQUFZRCxZQUFZLFlBQVksVUFDbEQsQ0FBQztZQUNGUyxpQkFBaUJULFlBQVksWUFBWUQsZ0JBQWdCLGdCQUFnQjtZQUN6RVcsU0FBUztZQUNUQyxZQUFZO1lBQ1pDLGdCQUFnQjtZQUNoQkMsUUFBUTtZQUNSQyxZQUFZO1lBQ1pDLFVBQVU7UUFDWjtRQUVBLElBQUlqQixLQUFLa0IsS0FBSyxLQUFLLFVBQVU7WUFDM0JkLFVBQVVlLFlBQVksR0FBRztRQUMzQixPQUFPLElBQUluQixLQUFLa0IsS0FBSyxLQUFLLGFBQWE7WUFDckNkLFVBQVVlLFlBQVksR0FBRztRQUMzQjtRQUVBLElBQUluQixLQUFLWixRQUFRLEVBQUU7WUFDakJnQixVQUFVZ0IsU0FBUyxHQUFHLENBQUMsT0FBTyxFQUFFcEIsS0FBS1osUUFBUSxDQUFDLElBQUksQ0FBQztRQUNyRDtRQUVBLHFCQUNFLDhEQUFDaUM7WUFFQ0MsT0FBT2xCO1lBQ1BtQixZQUFZLENBQUNqRCxJQUFNRCxtQkFBbUJDLEdBQUcwQixLQUFLakIsRUFBRTtZQUNoRHlDLGFBQWEvQztZQUNiZ0QsUUFBUSxDQUFDbkQsSUFBTUksZUFBZUosR0FBRzBCLEtBQUtqQixFQUFFO1lBQ3hDMkMsU0FBUyxJQUFNNUIsZUFBZUQsWUFBWUM7c0JBRXpDRyw4QkFDQyw4REFBQ29CO2dCQUFJTSxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQ0NDLEtBQUs1QixjQUFjNkIsR0FBRzt3QkFDdEJDLEtBQUk7d0JBQ0pKLFdBQVU7d0JBQ1ZMLE9BQU87NEJBQ0xGLFdBQVcsQ0FBQyxNQUFNLEVBQUV0QixhQUFhWCxTQUFTLEVBQUUsU0FBUyxFQUFFVyxhQUFhVixZQUFZLEVBQUUsSUFBSSxDQUFDOzRCQUN2RjRDLGdCQUFnQixDQUFDLEVBQUVsQyxhQUFhYixLQUFLLEdBQUcsRUFBRSxFQUFFYSxhQUFhWixLQUFLLEdBQUcsQ0FBQyxDQUFDO3dCQUNyRTs7Ozs7O2tDQUVGLDhEQUFDK0M7d0JBQ0NQLFNBQVMsQ0FBQ3BEOzRCQUNSQSxFQUFFNEQsZUFBZTs0QkFDakI1QyxvQkFBb0JXLGNBQWNsQixFQUFFO3dCQUN0Qzt3QkFDQTRDLFdBQVU7a0NBQ1g7Ozs7OztvQkFHQXhCLDRCQUNDLDhEQUFDa0I7d0JBQUlNLFdBQVU7Ozs7Ozs7Ozs7OzBDQUluQiw4REFBQ047Z0JBQUlNLFdBQVU7O2tDQUNiLDhEQUFDTjt3QkFBSU0sV0FBVTtrQ0FBZ0I7Ozs7OztrQ0FDL0IsOERBQUNOO3dCQUFJTSxXQUFVO2tDQUFVOzs7Ozs7Ozs7Ozs7V0FsQ3hCM0IsS0FBS2pCLEVBQUU7Ozs7O0lBdUNsQjtJQUVBLE1BQU1vRCxjQUFjOUUsU0FBUytFLFdBQVcsR0FBRy9FLFNBQVNnRixZQUFZO0lBQ2hFLE1BQU1ELGNBQWM7SUFDcEIsTUFBTUMsZUFBZUQsY0FBY0Q7SUFFbkMsTUFBTUcsaUJBQWlCaEYsZUFBZXNCLE1BQU0sQ0FBQ1QsQ0FBQUEsUUFDM0MsQ0FBQ1YsYUFBYThFLElBQUksQ0FBQ0MsQ0FBQUEsU0FBVUEsT0FBTzFELE9BQU8sS0FBS1gsTUFBTVksRUFBRTtJQUcxRCxNQUFNMEQsY0FBY2hGLGFBQWFpRixNQUFNO0lBQ3ZDLE1BQU1DLGFBQWF0RixTQUFTdUYsS0FBSyxDQUFDRixNQUFNO0lBRXhDLHFCQUNFLDhEQUFDckI7UUFBSU0sV0FBVTs7MEJBRWIsOERBQUNOO2dCQUFJTSxXQUFVOzBCQUNiLDRFQUFDN0UsMENBQUlBOztzQ0FDSCw4REFBQ0UsZ0RBQVVBOzs4Q0FDVCw4REFBQ0MsK0NBQVNBOzhDQUFDOzs7Ozs7OENBQ1gsOERBQUNvRTtvQ0FBSU0sV0FBVTs7c0RBQ2IsOERBQUNrQjs0Q0FBS2xCLFdBQVU7c0RBQW1COzs7Ozs7c0RBQ25DLDhEQUFDa0I7NENBQUtsQixXQUFVO3NEQUFZOzs7Ozs7d0NBQzNCO3dDQUFJOzs7Ozs7Ozs7Ozs7O3NDQUdULDhEQUFDNUUsaURBQVdBOzs4Q0FDViw4REFBQ3NFO29DQUFJTSxXQUFVOzhDQUNiLDRFQUFDTjt3Q0FDQ3lCLEtBQUs3RTt3Q0FDTDBELFdBQVU7d0NBQ1ZMLE9BQU87NENBQ0xkLE9BQU8sQ0FBQyxFQUFFdUMsS0FBS0MsR0FBRyxDQUFDWixhQUFhLEtBQUssRUFBRSxDQUFDOzRDQUN4QzNCLFFBQVEsQ0FBQyxFQUFFc0MsS0FBS0MsR0FBRyxDQUFDWCxjQUFjLEtBQUssRUFBRSxDQUFDOzRDQUMxQzFCLGlCQUFpQnRELFNBQVNzRCxlQUFlLElBQUk7d0NBQy9DO3dDQUNBZSxTQUFTLElBQU0xRCxpQkFBaUI7a0RBRS9CWCxTQUFTdUYsS0FBSyxDQUFDaEQsR0FBRyxDQUFDSSxDQUFBQSxPQUFRRCxXQUFXQzs7Ozs7Ozs7Ozs7OENBSTNDLDhEQUFDcUI7b0NBQUlNLFdBQVU7O3NEQUNiLDhEQUFDTjs0Q0FBSU0sV0FBVTs7Z0RBQ1pjO2dEQUFZO2dEQUFLRTtnREFBVztnREFDNUI1RSwrQkFDQyw4REFBQzhFO29EQUFLbEIsV0FBVTs4REFBc0I7Ozs7Ozs7Ozs7OztzREFHMUMsOERBQUNOOzRDQUFJTSxXQUFVOzs4REFDYiw4REFBQ3pFLDhDQUFNQTtvREFDTCtGLFNBQVE7b0RBQ1JDLE1BQUs7b0RBQ0x4QixTQUFTO3dEQUNQaEUsZ0JBQWdCLEVBQUU7d0RBQ2xCTSxpQkFBaUI7d0RBQ2pCVCxxQkFBcUIsRUFBRTtvREFDekI7b0RBQ0E0RixVQUFVMUYsYUFBYWlGLE1BQU0sS0FBSzs4REFDbkM7Ozs7Ozs4REFHRCw4REFBQ3hGLDhDQUFNQTtvREFDTGdHLE1BQUs7b0RBQ0x4QixTQUFTbEU7b0RBQ1QyRixVQUFVMUYsYUFBYWlGLE1BQU0sS0FBSzs4REFDbkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVVYLDhEQUFDckI7Z0JBQUlNLFdBQVU7MEJBQ2IsNEVBQUM3RSwwQ0FBSUE7O3NDQUNILDhEQUFDRSxnREFBVUE7OzhDQUNULDhEQUFDQywrQ0FBU0E7OENBQUM7Ozs7Ozs4Q0FDWCw4REFBQ29FO29DQUFJTSxXQUFVOzt3Q0FDWlcsZUFBZUksTUFBTTt3Q0FBQzs7Ozs7Ozs7Ozs7OztzQ0FHM0IsOERBQUMzRixpREFBV0E7O2dDQUNUdUYsZUFBZUksTUFBTSxHQUFHLGtCQUN2Qiw4REFBQ3JCO29DQUFJTSxXQUFVOzhDQUNaVyxlQUFlMUMsR0FBRyxDQUFDLENBQUN6QixzQkFDbkIsOERBQUNrRDs0Q0FFQ00sV0FBVTs0Q0FDVnlCLFNBQVM7NENBQ1RDLGFBQWEsSUFBTW5GLGdCQUFnQkM7NENBQ25DbUYsV0FBV2xGOzs4REFFWCw4REFBQ2lEO29EQUFJTSxXQUFVOzhEQUNiLDRFQUFDQzt3REFDQ0MsS0FBSzFELE1BQU0yRCxHQUFHO3dEQUNkQyxLQUFJO3dEQUNKSixXQUFVOzs7Ozs7Ozs7Ozs4REFHZCw4REFBQ047b0RBQUlNLFdBQVU7OERBQ2IsNEVBQUNOO3dEQUFJTSxXQUFVOzswRUFDYiw4REFBQ2tCO2dFQUFLbEIsV0FBVTswRUFBbUI7Ozs7OzswRUFDbkMsOERBQUNrQjtnRUFBS2xCLFdBQVU7MEVBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0FoQjNCeEQsTUFBTVksRUFBRTs7Ozs7Ozs7OzhEQXVCbkIsOERBQUNzQztvQ0FBSU0sV0FBVTs7c0RBQ2IsOERBQUNOOzRDQUFJTSxXQUFVO3NEQUFnQjs7Ozs7O3NEQUMvQiw4REFBQ047NENBQUlNLFdBQVU7c0RBQVU7Ozs7Ozs7Ozs7OztnQ0FNNUJyRSxlQUFlb0YsTUFBTSxLQUFLLG1CQUN6Qiw4REFBQ3JCO29DQUFJTSxXQUFVOztzREFDYiw4REFBQ047NENBQUlNLFdBQVU7c0RBQWdCOzs7Ozs7c0RBQy9CLDhEQUFDTjs0Q0FBSU0sV0FBVTtzREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVW5DLDhEQUFDTjtnQkFBSU0sV0FBVTswQkFDWjVELDhCQUNDLDhEQUFDWixzREFBYUE7b0JBQ1oyQyxhQUFhL0I7b0JBQ2JrQyxlQUFlUixpQkFBaUIxQixjQUFjZSxPQUFPO29CQUNyRHlFLFVBQVUsQ0FBQzVELFVBQVlELGtCQUFrQjNCLGNBQWNlLE9BQU8sRUFBRWE7b0JBQ2hFNkQsVUFBVTt3QkFDUmxFLG9CQUFvQnZCLGNBQWNlLE9BQU87d0JBQ3pDZCxpQkFBaUI7b0JBQ25COzs7Ozs4Q0FHRiw4REFBQ2xCLDBDQUFJQTs7c0NBQ0gsOERBQUNFLGdEQUFVQTtzQ0FDVCw0RUFBQ0MsK0NBQVNBO2dDQUFDMEUsV0FBVTswQ0FBVTs7Ozs7Ozs7Ozs7c0NBRWpDLDhEQUFDNUUsaURBQVdBO3NDQUNWLDRFQUFDc0U7Z0NBQUlNLFdBQVU7O2tEQUNiLDhEQUFDTjt3Q0FBSU0sV0FBVTtrREFBZ0I7Ozs7OztrREFDL0IsOERBQUNOO3dDQUFJTSxXQUFVO2tEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXpDO0FBRUEsaUVBQWV2RSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvY29tcG9uZW50cy9Db2xsYWdlRWRpdG9yLnRzeD8zZTE3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDb2xsYWdlVGVtcGxhdGUsIFVwbG9hZGVkUGhvdG8sIFBsYWNlZFBob3RvLCBQaG90b1Nsb3QgfSBmcm9tICdAL3R5cGVzL3RlbXBsYXRlJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICcuL3VpL2NhcmQnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnLi91aS9idXR0b24nO1xuaW1wb3J0IFBob3RvQ29udHJvbHMgZnJvbSAnLi9QaG90b0NvbnRyb2xzJztcblxuaW50ZXJmYWNlIENvbGxhZ2VFZGl0b3JQcm9wcyB7XG4gIHRlbXBsYXRlOiBDb2xsYWdlVGVtcGxhdGU7XG4gIHVwbG9hZGVkUGhvdG9zOiBVcGxvYWRlZFBob3RvW107XG4gIG9uUGxhY2VkUGhvdG9zQ2hhbmdlOiAocGxhY2VkUGhvdG9zOiBQbGFjZWRQaG90b1tdKSA9PiB2b2lkO1xuICBvblByZXZpZXc6ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IENvbGxhZ2VFZGl0b3I6IFJlYWN0LkZDPENvbGxhZ2VFZGl0b3JQcm9wcz4gPSAoe1xuICB0ZW1wbGF0ZSxcbiAgdXBsb2FkZWRQaG90b3MsXG4gIG9uUGxhY2VkUGhvdG9zQ2hhbmdlLFxuICBvblByZXZpZXdcbn0pID0+IHtcbiAgY29uc3QgW3BsYWNlZFBob3Rvcywgc2V0UGxhY2VkUGhvdG9zXSA9IHVzZVN0YXRlPFBsYWNlZFBob3RvW10+KFtdKTtcbiAgY29uc3QgW2RyYWdnZWRQaG90bywgc2V0RHJhZ2dlZFBob3RvXSA9IHVzZVN0YXRlPFVwbG9hZGVkUGhvdG8gfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2hvdmVyZWRTbG90LCBzZXRIb3ZlcmVkU2xvdF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkUGhvdG8sIHNldFNlbGVjdGVkUGhvdG9dID0gdXNlU3RhdGU8UGxhY2VkUGhvdG8gfCBudWxsPihudWxsKTtcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcblxuICBjb25zdCBoYW5kbGVEcmFnU3RhcnQgPSAocGhvdG86IFVwbG9hZGVkUGhvdG8pID0+IHtcbiAgICBzZXREcmFnZ2VkUGhvdG8ocGhvdG8pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURyYWdFbmQgPSAoKSA9PiB7XG4gICAgc2V0RHJhZ2dlZFBob3RvKG51bGwpO1xuICAgIHNldEhvdmVyZWRTbG90KG51bGwpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNsb3REcmFnT3ZlciA9IChlOiBSZWFjdC5EcmFnRXZlbnQsIHNsb3RJZDogc3RyaW5nKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldEhvdmVyZWRTbG90KHNsb3RJZCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2xvdERyYWdMZWF2ZSA9ICgpID0+IHtcbiAgICBzZXRIb3ZlcmVkU2xvdChudWxsKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTbG90RHJvcCA9IChlOiBSZWFjdC5EcmFnRXZlbnQsIHNsb3RJZDogc3RyaW5nKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldEhvdmVyZWRTbG90KG51bGwpO1xuXG4gICAgaWYgKCFkcmFnZ2VkUGhvdG8pIHJldHVybjtcblxuICAgIC8vIFJlbW92ZSBwaG90byBmcm9tIGFueSBleGlzdGluZyBzbG90XG4gICAgY29uc3QgdXBkYXRlZFBsYWNlZFBob3RvcyA9IHBsYWNlZFBob3Rvcy5maWx0ZXIocCA9PiBwLnBob3RvSWQgIT09IGRyYWdnZWRQaG90by5pZCk7XG4gICAgXG4gICAgLy8gQWRkIHBob3RvIHRvIG5ldyBzbG90XG4gICAgY29uc3QgbmV3UGxhY2VkUGhvdG86IFBsYWNlZFBob3RvID0ge1xuICAgICAgcGhvdG9JZDogZHJhZ2dlZFBob3RvLmlkLFxuICAgICAgc2xvdElkLFxuICAgICAgeDogNTAsIC8vIENlbnRlciBwb3NpdGlvblxuICAgICAgeTogNTAsIC8vIENlbnRlciBwb3NpdGlvblxuICAgICAgc2NhbGU6IDEuMCxcbiAgICAgIHJvdGF0aW9uOiAwXG4gICAgfTtcblxuICAgIGNvbnN0IGZpbmFsUGxhY2VkUGhvdG9zID0gWy4uLnVwZGF0ZWRQbGFjZWRQaG90b3MsIG5ld1BsYWNlZFBob3RvXTtcbiAgICBzZXRQbGFjZWRQaG90b3MoZmluYWxQbGFjZWRQaG90b3MpO1xuICAgIG9uUGxhY2VkUGhvdG9zQ2hhbmdlKGZpbmFsUGxhY2VkUGhvdG9zKTtcbiAgfTtcblxuICBjb25zdCByZW1vdmVQaG90b0Zyb21TbG90ID0gKHBob3RvSWQ6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHVwZGF0ZWRQbGFjZWRQaG90b3MgPSBwbGFjZWRQaG90b3MuZmlsdGVyKHAgPT4gcC5waG90b0lkICE9PSBwaG90b0lkKTtcbiAgICBzZXRQbGFjZWRQaG90b3ModXBkYXRlZFBsYWNlZFBob3Rvcyk7XG4gICAgb25QbGFjZWRQaG90b3NDaGFuZ2UodXBkYXRlZFBsYWNlZFBob3Rvcyk7XG4gIH07XG5cbiAgY29uc3QgZ2V0UGhvdG9JblNsb3QgPSAoc2xvdElkOiBzdHJpbmcpOiBQbGFjZWRQaG90byB8IHVuZGVmaW5lZCA9PiB7XG4gICAgcmV0dXJuIHBsYWNlZFBob3Rvcy5maW5kKHAgPT4gcC5zbG90SWQgPT09IHNsb3RJZCk7XG4gIH07XG5cbiAgY29uc3QgZ2V0VXBsb2FkZWRQaG90byA9IChwaG90b0lkOiBzdHJpbmcpOiBVcGxvYWRlZFBob3RvIHwgdW5kZWZpbmVkID0+IHtcbiAgICByZXR1cm4gdXBsb2FkZWRQaG90b3MuZmluZChwID0+IHAuaWQgPT09IHBob3RvSWQpO1xuICB9O1xuXG4gIGNvbnN0IHVwZGF0ZVBsYWNlZFBob3RvID0gKHBob3RvSWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxQbGFjZWRQaG90bz4pID0+IHtcbiAgICBjb25zdCB1cGRhdGVkUGxhY2VkUGhvdG9zID0gcGxhY2VkUGhvdG9zLm1hcChwID0+XG4gICAgICBwLnBob3RvSWQgPT09IHBob3RvSWQgPyB7IC4uLnAsIC4uLnVwZGF0ZXMgfSA6IHBcbiAgICApO1xuICAgIHNldFBsYWNlZFBob3Rvcyh1cGRhdGVkUGxhY2VkUGhvdG9zKTtcbiAgICBvblBsYWNlZFBob3Rvc0NoYW5nZSh1cGRhdGVkUGxhY2VkUGhvdG9zKTtcblxuICAgIC8vIFVwZGF0ZSBzZWxlY3RlZCBwaG90byBpZiBpdCdzIHRoZSBvbmUgYmVpbmcgdXBkYXRlZFxuICAgIGlmIChzZWxlY3RlZFBob3RvICYmIHNlbGVjdGVkUGhvdG8ucGhvdG9JZCA9PT0gcGhvdG9JZCkge1xuICAgICAgc2V0U2VsZWN0ZWRQaG90byh7IC4uLnNlbGVjdGVkUGhvdG8sIC4uLnVwZGF0ZXMgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHNlbGVjdFBob3RvID0gKHBsYWNlZFBob3RvOiBQbGFjZWRQaG90bykgPT4ge1xuICAgIHNldFNlbGVjdGVkUGhvdG8ocGxhY2VkUGhvdG8pO1xuICB9O1xuXG4gIGNvbnN0IHJlbmRlclNsb3QgPSAoc2xvdDogUGhvdG9TbG90KSA9PiB7XG4gICAgY29uc3QgcGxhY2VkUGhvdG8gPSBnZXRQaG90b0luU2xvdChzbG90LmlkKTtcbiAgICBjb25zdCB1cGxvYWRlZFBob3RvID0gcGxhY2VkUGhvdG8gPyBnZXRVcGxvYWRlZFBob3RvKHBsYWNlZFBob3RvLnBob3RvSWQpIDogbnVsbDtcbiAgICBjb25zdCBpc0hvdmVyZWQgPSBob3ZlcmVkU2xvdCA9PT0gc2xvdC5pZDtcbiAgICBjb25zdCBpc1NlbGVjdGVkID0gc2VsZWN0ZWRQaG90byAmJiBzZWxlY3RlZFBob3RvLnNsb3RJZCA9PT0gc2xvdC5pZDtcblxuICAgIGNvbnN0IHNsb3RTdHlsZTogUmVhY3QuQ1NTUHJvcGVydGllcyA9IHtcbiAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgbGVmdDogYCR7c2xvdC54fSVgLFxuICAgICAgdG9wOiBgJHtzbG90Lnl9JWAsXG4gICAgICB3aWR0aDogYCR7c2xvdC53aWR0aH0lYCxcbiAgICAgIGhlaWdodDogYCR7c2xvdC5oZWlnaHR9JWAsXG4gICAgICBib3JkZXI6IGAycHggJHtpc1NlbGVjdGVkID8gJ3NvbGlkJyA6IGlzSG92ZXJlZCA/ICdzb2xpZCcgOiAnZGFzaGVkJ30gJHtcbiAgICAgICAgaXNTZWxlY3RlZCA/ICcjMTBiOTgxJyA6IGlzSG92ZXJlZCA/ICcjM2I4MmY2JyA6ICcjOWNhM2FmJ1xuICAgICAgfWAsXG4gICAgICBiYWNrZ3JvdW5kQ29sb3I6IGlzSG92ZXJlZCA/ICcjZGJlYWZlJyA6IHVwbG9hZGVkUGhvdG8gPyAndHJhbnNwYXJlbnQnIDogJyNmM2Y0ZjYnLFxuICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlJyxcbiAgICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgICB9O1xuXG4gICAgaWYgKHNsb3Quc2hhcGUgPT09ICdjaXJjbGUnKSB7XG4gICAgICBzbG90U3R5bGUuYm9yZGVyUmFkaXVzID0gJzUwJSc7XG4gICAgfSBlbHNlIGlmIChzbG90LnNoYXBlID09PSAncmVjdGFuZ2xlJykge1xuICAgICAgc2xvdFN0eWxlLmJvcmRlclJhZGl1cyA9ICc0cHgnO1xuICAgIH1cblxuICAgIGlmIChzbG90LnJvdGF0aW9uKSB7XG4gICAgICBzbG90U3R5bGUudHJhbnNmb3JtID0gYHJvdGF0ZSgke3Nsb3Qucm90YXRpb259ZGVnKWA7XG4gICAgfVxuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXZcbiAgICAgICAga2V5PXtzbG90LmlkfVxuICAgICAgICBzdHlsZT17c2xvdFN0eWxlfVxuICAgICAgICBvbkRyYWdPdmVyPXsoZSkgPT4gaGFuZGxlU2xvdERyYWdPdmVyKGUsIHNsb3QuaWQpfVxuICAgICAgICBvbkRyYWdMZWF2ZT17aGFuZGxlU2xvdERyYWdMZWF2ZX1cbiAgICAgICAgb25Ecm9wPXsoZSkgPT4gaGFuZGxlU2xvdERyb3AoZSwgc2xvdC5pZCl9XG4gICAgICAgIG9uQ2xpY2s9eygpID0+IHBsYWNlZFBob3RvICYmIHNlbGVjdFBob3RvKHBsYWNlZFBob3RvKX1cbiAgICAgID5cbiAgICAgICAge3VwbG9hZGVkUGhvdG8gPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB3LWZ1bGwgaC1mdWxsIGdyb3VwXCI+XG4gICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgIHNyYz17dXBsb2FkZWRQaG90by51cmx9XG4gICAgICAgICAgICAgIGFsdD1cIlBsYWNlZCBwaG90b1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IGBzY2FsZSgke3BsYWNlZFBob3RvPy5zY2FsZSB8fCAxfSkgcm90YXRlKCR7cGxhY2VkUGhvdG8/LnJvdGF0aW9uIHx8IDB9ZGVnKWAsXG4gICAgICAgICAgICAgICAgb2JqZWN0UG9zaXRpb246IGAke3BsYWNlZFBob3RvPy54IHx8IDUwfSUgJHtwbGFjZWRQaG90bz8ueSB8fCA1MH0lYFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgIHJlbW92ZVBob3RvRnJvbVNsb3QodXBsb2FkZWRQaG90by5pZCk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xIHJpZ2h0LTEgYmctcmVkLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCB3LTUgaC01IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHRleHQteHMgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIMOXXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIHtpc1NlbGVjdGVkICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJvcmRlci0yIGJvcmRlci1ncmVlbi01MDAgcG9pbnRlci1ldmVudHMtbm9uZVwiIC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtYi0xXCI+8J+TtzwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+RHJvcCBwaG90byBoZXJlPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIGNvbnN0IGFzcGVjdFJhdGlvID0gdGVtcGxhdGUuY2FudmFzV2lkdGggLyB0ZW1wbGF0ZS5jYW52YXNIZWlnaHQ7XG4gIGNvbnN0IGNhbnZhc1dpZHRoID0gNjAwO1xuICBjb25zdCBjYW52YXNIZWlnaHQgPSBjYW52YXNXaWR0aCAvIGFzcGVjdFJhdGlvO1xuXG4gIGNvbnN0IHVucGxhY2VkUGhvdG9zID0gdXBsb2FkZWRQaG90b3MuZmlsdGVyKHBob3RvID0+IFxuICAgICFwbGFjZWRQaG90b3Muc29tZShwbGFjZWQgPT4gcGxhY2VkLnBob3RvSWQgPT09IHBob3RvLmlkKVxuICApO1xuXG4gIGNvbnN0IGZpbGxlZFNsb3RzID0gcGxhY2VkUGhvdG9zLmxlbmd0aDtcbiAgY29uc3QgdG90YWxTbG90cyA9IHRlbXBsYXRlLnNsb3RzLmxlbmd0aDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgey8qIENhbnZhcyBBcmVhICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCI+XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZT5Db2xsYWdlIENhbnZhczwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmlubGluZVwiPkRyYWcgcGhvdG9zIGZyb20gdGhlIHNpZGViYXIgaW50byB0aGUgc2xvdHMgYmVsb3cuPC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIj5UYXAgcGhvdG9zIGJlbG93IHRvIGFkZCB0aGVtIHRvIHNsb3RzLjwvc3Bhbj5cbiAgICAgICAgICAgICAge1wiIFwifUNsaWNrIG9uIHBsYWNlZCBwaG90b3MgdG8gZWRpdCB0aGVtLlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIHJlZj17Y2FudmFzUmVmfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIGJvcmRlciByb3VuZGVkLWxnIHNoYWRvdy1zbSBtaW4tdy0wIG1heC13LWZ1bGxcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogYCR7TWF0aC5taW4oY2FudmFzV2lkdGgsIDYwMCl9cHhgLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBgJHtNYXRoLm1pbihjYW52YXNIZWlnaHQsIDYwMCl9cHhgLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiB0ZW1wbGF0ZS5iYWNrZ3JvdW5kQ29sb3IgfHwgJyNmZmZmZmYnLFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRQaG90byhudWxsKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5zbG90cy5tYXAoc2xvdCA9PiByZW5kZXJTbG90KHNsb3QpKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIHtmaWxsZWRTbG90c30gb2Yge3RvdGFsU2xvdHN9IHNsb3RzIGZpbGxlZFxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFBob3RvICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmVlbi02MDBcIj7igKIgUGhvdG8gc2VsZWN0ZWQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHNldFBsYWNlZFBob3RvcyhbXSk7XG4gICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkUGhvdG8obnVsbCk7XG4gICAgICAgICAgICAgICAgICAgIG9uUGxhY2VkUGhvdG9zQ2hhbmdlKFtdKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17cGxhY2VkUGhvdG9zLmxlbmd0aCA9PT0gMH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDbGVhciBBbGxcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25QcmV2aWV3fVxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3BsYWNlZFBob3Rvcy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgUHJldmlldyBDb2xsYWdlXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQaG90byBMaWJyYXJ5ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZT5QaG90byBMaWJyYXJ5PC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICB7dW5wbGFjZWRQaG90b3MubGVuZ3RofSBwaG90b3MgYXZhaWxhYmxlXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAge3VucGxhY2VkUGhvdG9zLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMSBnYXAtM1wiPlxuICAgICAgICAgICAgICAgIHt1bnBsYWNlZFBob3Rvcy5tYXAoKHBob3RvKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17cGhvdG8uaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwIGN1cnNvci1tb3ZlIHRvdWNoLW1hbmlwdWxhdGlvblwiXG4gICAgICAgICAgICAgICAgICAgIGRyYWdnYWJsZVxuICAgICAgICAgICAgICAgICAgICBvbkRyYWdTdGFydD17KCkgPT4gaGFuZGxlRHJhZ1N0YXJ0KHBob3RvKX1cbiAgICAgICAgICAgICAgICAgICAgb25EcmFnRW5kPXtoYW5kbGVEcmFnRW5kfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC1zcXVhcmUgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gYm9yZGVyLTIgYm9yZGVyLXRyYW5zcGFyZW50IGhvdmVyOmJvcmRlci1ibHVlLTMwMCBhY3RpdmU6Ym9yZGVyLWJsdWUtNTAwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgICAgc3JjPXtwaG90by51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCJBdmFpbGFibGUgcGhvdG9cIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS0wIGdyb3VwLWhvdmVyOmJnLW9wYWNpdHktMjAgZ3JvdXAtYWN0aXZlOmJnLW9wYWNpdHktMzAgdHJhbnNpdGlvbi1hbGwgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBvcGFjaXR5LTAgZ3JvdXAtaG92ZXI6b3BhY2l0eS0xMDAgZ3JvdXAtYWN0aXZlOm9wYWNpdHktMTAwIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1jZW50ZXIgcHgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmlubGluZVwiPkRyYWcgdG8gc2xvdDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1kOmhpZGRlblwiPlRhcCB0byBzZWxlY3Q8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNHhsIG1iLTJcIj7inKg8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIEFsbCBwaG90b3MgaGF2ZSBiZWVuIHBsYWNlZCFcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7dXBsb2FkZWRQaG90b3MubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNHhsIG1iLTJcIj7wn5O3PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICBVcGxvYWQgcGhvdG9zIHRvIGdldCBzdGFydGVkXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFBob3RvIENvbnRyb2xzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgIHtzZWxlY3RlZFBob3RvID8gKFxuICAgICAgICAgIDxQaG90b0NvbnRyb2xzXG4gICAgICAgICAgICBwbGFjZWRQaG90bz17c2VsZWN0ZWRQaG90b31cbiAgICAgICAgICAgIHVwbG9hZGVkUGhvdG89e2dldFVwbG9hZGVkUGhvdG8oc2VsZWN0ZWRQaG90by5waG90b0lkKSF9XG4gICAgICAgICAgICBvblVwZGF0ZT17KHVwZGF0ZXMpID0+IHVwZGF0ZVBsYWNlZFBob3RvKHNlbGVjdGVkUGhvdG8ucGhvdG9JZCwgdXBkYXRlcyl9XG4gICAgICAgICAgICBvblJlbW92ZT17KCkgPT4ge1xuICAgICAgICAgICAgICByZW1vdmVQaG90b0Zyb21TbG90KHNlbGVjdGVkUGhvdG8ucGhvdG9JZCk7XG4gICAgICAgICAgICAgIHNldFNlbGVjdGVkUGhvdG8obnVsbCk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+UGhvdG8gQ29udHJvbHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNHhsIG1iLTJcIj7wn46b77iPPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICBDbGljayBvbiBhIHBsYWNlZCBwaG90byB0byBlZGl0IGl0cyBwb3NpdGlvbiwgc2NhbGUsIGFuZCByb3RhdGlvblxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb2xsYWdlRWRpdG9yO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiUGhvdG9Db250cm9scyIsIkNvbGxhZ2VFZGl0b3IiLCJ0ZW1wbGF0ZSIsInVwbG9hZGVkUGhvdG9zIiwib25QbGFjZWRQaG90b3NDaGFuZ2UiLCJvblByZXZpZXciLCJwbGFjZWRQaG90b3MiLCJzZXRQbGFjZWRQaG90b3MiLCJkcmFnZ2VkUGhvdG8iLCJzZXREcmFnZ2VkUGhvdG8iLCJob3ZlcmVkU2xvdCIsInNldEhvdmVyZWRTbG90Iiwic2VsZWN0ZWRQaG90byIsInNldFNlbGVjdGVkUGhvdG8iLCJjYW52YXNSZWYiLCJoYW5kbGVEcmFnU3RhcnQiLCJwaG90byIsImhhbmRsZURyYWdFbmQiLCJoYW5kbGVTbG90RHJhZ092ZXIiLCJlIiwic2xvdElkIiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVTbG90RHJhZ0xlYXZlIiwiaGFuZGxlU2xvdERyb3AiLCJ1cGRhdGVkUGxhY2VkUGhvdG9zIiwiZmlsdGVyIiwicCIsInBob3RvSWQiLCJpZCIsIm5ld1BsYWNlZFBob3RvIiwieCIsInkiLCJzY2FsZSIsInJvdGF0aW9uIiwiZmluYWxQbGFjZWRQaG90b3MiLCJyZW1vdmVQaG90b0Zyb21TbG90IiwiZ2V0UGhvdG9JblNsb3QiLCJmaW5kIiwiZ2V0VXBsb2FkZWRQaG90byIsInVwZGF0ZVBsYWNlZFBob3RvIiwidXBkYXRlcyIsIm1hcCIsInNlbGVjdFBob3RvIiwicGxhY2VkUGhvdG8iLCJyZW5kZXJTbG90Iiwic2xvdCIsInVwbG9hZGVkUGhvdG8iLCJpc0hvdmVyZWQiLCJpc1NlbGVjdGVkIiwic2xvdFN0eWxlIiwicG9zaXRpb24iLCJsZWZ0IiwidG9wIiwid2lkdGgiLCJoZWlnaHQiLCJib3JkZXIiLCJiYWNrZ3JvdW5kQ29sb3IiLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiY3Vyc29yIiwidHJhbnNpdGlvbiIsIm92ZXJmbG93Iiwic2hhcGUiLCJib3JkZXJSYWRpdXMiLCJ0cmFuc2Zvcm0iLCJkaXYiLCJzdHlsZSIsIm9uRHJhZ092ZXIiLCJvbkRyYWdMZWF2ZSIsIm9uRHJvcCIsIm9uQ2xpY2siLCJjbGFzc05hbWUiLCJpbWciLCJzcmMiLCJ1cmwiLCJhbHQiLCJvYmplY3RQb3NpdGlvbiIsImJ1dHRvbiIsInN0b3BQcm9wYWdhdGlvbiIsImFzcGVjdFJhdGlvIiwiY2FudmFzV2lkdGgiLCJjYW52YXNIZWlnaHQiLCJ1bnBsYWNlZFBob3RvcyIsInNvbWUiLCJwbGFjZWQiLCJmaWxsZWRTbG90cyIsImxlbmd0aCIsInRvdGFsU2xvdHMiLCJzbG90cyIsInNwYW4iLCJyZWYiLCJNYXRoIiwibWluIiwidmFyaWFudCIsInNpemUiLCJkaXNhYmxlZCIsImRyYWdnYWJsZSIsIm9uRHJhZ1N0YXJ0Iiwib25EcmFnRW5kIiwib25VcGRhdGUiLCJvblJlbW92ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CollageEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CollageRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/CollageRenderer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CollageRenderer = ({ template, uploadedPhotos, placedPhotos, onDownload })=>{\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isRendering, setIsRendering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewDataUrl, setPreviewDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const getPlacedPhoto = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const loadImage = (src)=>{\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            img.onload = ()=>resolve(img);\n            img.onerror = reject;\n            img.src = src;\n        });\n    };\n    const renderSlotToCanvas = async (ctx, slot, placedPhoto, uploadedPhoto)=>{\n        try {\n            const img = await loadImage(uploadedPhoto.url);\n            // Calculate slot dimensions in pixels\n            const slotX = slot.x / 100 * template.canvasWidth;\n            const slotY = slot.y / 100 * template.canvasHeight;\n            const slotWidth = slot.width / 100 * template.canvasWidth;\n            const slotHeight = slot.height / 100 * template.canvasHeight;\n            // Save canvas state\n            ctx.save();\n            // Create clipping path for the slot shape\n            ctx.beginPath();\n            if (slot.shape === \"circle\") {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                const radius = Math.min(slotWidth, slotHeight) / 2;\n                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\n            } else {\n                // Rectangle or custom shape\n                ctx.rect(slotX, slotY, slotWidth, slotHeight);\n            }\n            ctx.clip();\n            // Apply slot rotation if any\n            if (slot.rotation) {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(slot.rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Calculate image positioning and scaling\n            const scale = placedPhoto.scale || 1;\n            const rotation = placedPhoto.rotation || 0;\n            const posX = (placedPhoto.x || 50) / 100;\n            const posY = (placedPhoto.y || 50) / 100;\n            // Calculate scaled image dimensions\n            const scaledWidth = slotWidth * scale;\n            const scaledHeight = slotHeight * scale;\n            // Calculate position offset based on positioning\n            const offsetX = slotX + (slotWidth - scaledWidth) * posX;\n            const offsetY = slotY + (slotHeight - scaledHeight) * posY;\n            // Apply image rotation\n            if (rotation !== 0) {\n                const centerX = offsetX + scaledWidth / 2;\n                const centerY = offsetY + scaledHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Draw the image\n            ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);\n            // Restore canvas state\n            ctx.restore();\n        } catch (error) {\n            console.error(\"Error rendering slot:\", error);\n        }\n    };\n    const renderCollage = async ()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return null;\n        setIsRendering(true);\n        setRenderError(null);\n        try {\n            const ctx = canvas.getContext(\"2d\");\n            if (!ctx) {\n                throw new Error(\"Could not get canvas context\");\n            }\n            // Set canvas dimensions\n            canvas.width = template.canvasWidth;\n            canvas.height = template.canvasHeight;\n            // Clear canvas and set background\n            ctx.fillStyle = template.backgroundColor || \"#ffffff\";\n            ctx.fillRect(0, 0, template.canvasWidth, template.canvasHeight);\n            // Render each slot with its photo\n            for (const slot of template.slots){\n                const placedPhoto = getPlacedPhoto(slot.id);\n                if (placedPhoto) {\n                    const uploadedPhoto = getUploadedPhoto(placedPhoto.photoId);\n                    if (uploadedPhoto) {\n                        await renderSlotToCanvas(ctx, slot, placedPhoto, uploadedPhoto);\n                    }\n                }\n            }\n            // Get the data URL\n            const dataUrl = canvas.toDataURL(\"image/png\", 1.0);\n            setPreviewDataUrl(dataUrl);\n            return dataUrl;\n        } catch (error) {\n            console.error(\"Error rendering collage:\", error);\n            setRenderError(error instanceof Error ? error.message : \"Failed to render collage\");\n            return null;\n        } finally{\n            setIsRendering(false);\n        }\n    };\n    const handleDownload = async ()=>{\n        const dataUrl = await renderCollage();\n        if (dataUrl) {\n            // Create download link\n            const link = document.createElement(\"a\");\n            link.download = `collage-${template.name.toLowerCase().replace(/\\s+/g, \"-\")}-${Date.now()}.png`;\n            link.href = dataUrl;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            if (onDownload) {\n                onDownload(dataUrl);\n            }\n        }\n    };\n    const handlePreview = async ()=>{\n        await renderCollage();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Auto-render preview when component mounts or data changes\n        if (placedPhotos.length > 0) {\n            handlePreview();\n        }\n    }, [\n        template,\n        placedPhotos,\n        uploadedPhotos\n    ]);\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    const isComplete = filledSlots === totalSlots;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        children: \"Collage Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: isComplete ? \"Your collage is ready!\" : `${filledSlots} of ${totalSlots} slots filled`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                        ref: canvasRef,\n                                        className: \"border rounded-lg shadow-sm max-w-full h-auto\",\n                                        style: {\n                                            maxHeight: \"400px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isRendering && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm\",\n                                            children: \"Rendering...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handlePreview,\n                                    disabled: isRendering || placedPhotos.length === 0,\n                                    children: isRendering ? \"Rendering...\" : \"Refresh Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: isRendering || !isComplete,\n                                    children: \"Download Collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-sm text-gray-600\",\n                            children: [\n                                renderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-700 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Error:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \" \",\n                                            renderError\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined),\n                                !isComplete && !renderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add photos to all slots to enable download\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, undefined),\n                                isComplete && !renderError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600\",\n                                    children: \"✓ Ready to download!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Canvas Size: \",\n                                        template.canvasWidth,\n                                        \" \\xd7 \",\n                                        template.canvasHeight,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Format: PNG • Quality: High\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CollageRenderer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Db2xsYWdlUmVuZGVyZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTJEO0FBRXRCO0FBQ2dDO0FBU3JFLE1BQU1TLGtCQUFrRCxDQUFDLEVBQ3ZEQyxRQUFRLEVBQ1JDLGNBQWMsRUFDZEMsWUFBWSxFQUNaQyxVQUFVLEVBQ1g7SUFDQyxNQUFNQyxZQUFZYiw2Q0FBTUEsQ0FBb0I7SUFDNUMsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdiLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2MsZ0JBQWdCQyxrQkFBa0IsR0FBR2YsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ2dCLGFBQWFDLGVBQWUsR0FBR2pCLCtDQUFRQSxDQUFnQjtJQUU5RCxNQUFNa0IsbUJBQW1CLENBQUNDO1FBQ3hCLE9BQU9YLGVBQWVZLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLSDtJQUMzQztJQUVBLE1BQU1JLGlCQUFpQixDQUFDQztRQUN0QixPQUFPZixhQUFhVyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVHLE1BQU0sS0FBS0E7SUFDN0M7SUFFQSxNQUFNQyxZQUFZLENBQUNDO1FBQ2pCLE9BQU8sSUFBSUMsUUFBUSxDQUFDQyxTQUFTQztZQUMzQixNQUFNQyxNQUFNLElBQUlDO1lBQ2hCRCxJQUFJRSxNQUFNLEdBQUcsSUFBTUosUUFBUUU7WUFDM0JBLElBQUlHLE9BQU8sR0FBR0o7WUFDZEMsSUFBSUosR0FBRyxHQUFHQTtRQUNaO0lBQ0Y7SUFFQSxNQUFNUSxxQkFBcUIsT0FDekJDLEtBQ0FDLE1BQ0FDLGFBQ0FDO1FBRUEsSUFBSTtZQUNGLE1BQU1SLE1BQU0sTUFBTUwsVUFBVWEsY0FBY0MsR0FBRztZQUU3QyxzQ0FBc0M7WUFDdEMsTUFBTUMsUUFBUSxLQUFNQyxDQUFDLEdBQUcsTUFBT2xDLFNBQVNtQyxXQUFXO1lBQ25ELE1BQU1DLFFBQVEsS0FBTUMsQ0FBQyxHQUFHLE1BQU9yQyxTQUFTc0MsWUFBWTtZQUNwRCxNQUFNQyxZQUFZLEtBQU1DLEtBQUssR0FBRyxNQUFPeEMsU0FBU21DLFdBQVc7WUFDM0QsTUFBTU0sYUFBYSxLQUFNQyxNQUFNLEdBQUcsTUFBTzFDLFNBQVNzQyxZQUFZO1lBRTlELG9CQUFvQjtZQUNwQlYsSUFBSWUsSUFBSTtZQUVSLDBDQUEwQztZQUMxQ2YsSUFBSWdCLFNBQVM7WUFDYixJQUFJZixLQUFLZ0IsS0FBSyxLQUFLLFVBQVU7Z0JBQzNCLE1BQU1DLFVBQVViLFFBQVFNLFlBQVk7Z0JBQ3BDLE1BQU1RLFVBQVVYLFFBQVFLLGFBQWE7Z0JBQ3JDLE1BQU1PLFNBQVNDLEtBQUtDLEdBQUcsQ0FBQ1gsV0FBV0UsY0FBYztnQkFDakRiLElBQUl1QixHQUFHLENBQUNMLFNBQVNDLFNBQVNDLFFBQVEsR0FBRyxJQUFJQyxLQUFLRyxFQUFFO1lBQ2xELE9BQU87Z0JBQ0wsNEJBQTRCO2dCQUM1QnhCLElBQUl5QixJQUFJLENBQUNwQixPQUFPRyxPQUFPRyxXQUFXRTtZQUNwQztZQUNBYixJQUFJMEIsSUFBSTtZQUVSLDZCQUE2QjtZQUM3QixJQUFJekIsS0FBSzBCLFFBQVEsRUFBRTtnQkFDakIsTUFBTVQsVUFBVWIsUUFBUU0sWUFBWTtnQkFDcEMsTUFBTVEsVUFBVVgsUUFBUUssYUFBYTtnQkFDckNiLElBQUk0QixTQUFTLENBQUNWLFNBQVNDO2dCQUN2Qm5CLElBQUk2QixNQUFNLENBQUMsS0FBTUYsUUFBUSxHQUFHTixLQUFLRyxFQUFFLEdBQUk7Z0JBQ3ZDeEIsSUFBSTRCLFNBQVMsQ0FBQyxDQUFDVixTQUFTLENBQUNDO1lBQzNCO1lBRUEsMENBQTBDO1lBQzFDLE1BQU1XLFFBQVE1QixZQUFZNEIsS0FBSyxJQUFJO1lBQ25DLE1BQU1ILFdBQVd6QixZQUFZeUIsUUFBUSxJQUFJO1lBQ3pDLE1BQU1JLE9BQU8sQ0FBQzdCLFlBQVlJLENBQUMsSUFBSSxFQUFDLElBQUs7WUFDckMsTUFBTTBCLE9BQU8sQ0FBQzlCLFlBQVlPLENBQUMsSUFBSSxFQUFDLElBQUs7WUFFckMsb0NBQW9DO1lBQ3BDLE1BQU13QixjQUFjdEIsWUFBWW1CO1lBQ2hDLE1BQU1JLGVBQWVyQixhQUFhaUI7WUFFbEMsaURBQWlEO1lBQ2pELE1BQU1LLFVBQVU5QixRQUFRLENBQUNNLFlBQVlzQixXQUFVLElBQUtGO1lBQ3BELE1BQU1LLFVBQVU1QixRQUFRLENBQUNLLGFBQWFxQixZQUFXLElBQUtGO1lBRXRELHVCQUF1QjtZQUN2QixJQUFJTCxhQUFhLEdBQUc7Z0JBQ2xCLE1BQU1ULFVBQVVpQixVQUFVRixjQUFjO2dCQUN4QyxNQUFNZCxVQUFVaUIsVUFBVUYsZUFBZTtnQkFDekNsQyxJQUFJNEIsU0FBUyxDQUFDVixTQUFTQztnQkFDdkJuQixJQUFJNkIsTUFBTSxDQUFDLFdBQVlSLEtBQUtHLEVBQUUsR0FBSTtnQkFDbEN4QixJQUFJNEIsU0FBUyxDQUFDLENBQUNWLFNBQVMsQ0FBQ0M7WUFDM0I7WUFFQSxpQkFBaUI7WUFDakJuQixJQUFJcUMsU0FBUyxDQUFDMUMsS0FBS3dDLFNBQVNDLFNBQVNILGFBQWFDO1lBRWxELHVCQUF1QjtZQUN2QmxDLElBQUlzQyxPQUFPO1FBQ2IsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3pDO0lBQ0Y7SUFFQSxNQUFNRSxnQkFBZ0I7UUFDcEIsTUFBTUMsU0FBU2xFLFVBQVVtRSxPQUFPO1FBQ2hDLElBQUksQ0FBQ0QsUUFBUSxPQUFPO1FBRXBCaEUsZUFBZTtRQUNmSSxlQUFlO1FBRWYsSUFBSTtZQUNGLE1BQU1rQixNQUFNMEMsT0FBT0UsVUFBVSxDQUFDO1lBQzlCLElBQUksQ0FBQzVDLEtBQUs7Z0JBQ1IsTUFBTSxJQUFJNkMsTUFBTTtZQUNsQjtZQUVBLHdCQUF3QjtZQUN4QkgsT0FBTzlCLEtBQUssR0FBR3hDLFNBQVNtQyxXQUFXO1lBQ25DbUMsT0FBTzVCLE1BQU0sR0FBRzFDLFNBQVNzQyxZQUFZO1lBRXJDLGtDQUFrQztZQUNsQ1YsSUFBSThDLFNBQVMsR0FBRzFFLFNBQVMyRSxlQUFlLElBQUk7WUFDNUMvQyxJQUFJZ0QsUUFBUSxDQUFDLEdBQUcsR0FBRzVFLFNBQVNtQyxXQUFXLEVBQUVuQyxTQUFTc0MsWUFBWTtZQUU5RCxrQ0FBa0M7WUFDbEMsS0FBSyxNQUFNVCxRQUFRN0IsU0FBUzZFLEtBQUssQ0FBRTtnQkFDakMsTUFBTS9DLGNBQWNkLGVBQWVhLEtBQUtkLEVBQUU7Z0JBQzFDLElBQUllLGFBQWE7b0JBQ2YsTUFBTUMsZ0JBQWdCcEIsaUJBQWlCbUIsWUFBWWxCLE9BQU87b0JBQzFELElBQUltQixlQUFlO3dCQUNqQixNQUFNSixtQkFBbUJDLEtBQUtDLE1BQU1DLGFBQWFDO29CQUNuRDtnQkFDRjtZQUNGO1lBRUEsbUJBQW1CO1lBQ25CLE1BQU0rQyxVQUFVUixPQUFPUyxTQUFTLENBQUMsYUFBYTtZQUM5Q3ZFLGtCQUFrQnNFO1lBQ2xCLE9BQU9BO1FBQ1QsRUFBRSxPQUFPWCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDekQsZUFBZXlELGlCQUFpQk0sUUFBUU4sTUFBTWEsT0FBTyxHQUFHO1lBQ3hELE9BQU87UUFDVCxTQUFVO1lBQ1IxRSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNMkUsaUJBQWlCO1FBQ3JCLE1BQU1ILFVBQVUsTUFBTVQ7UUFDdEIsSUFBSVMsU0FBUztZQUNYLHVCQUF1QjtZQUN2QixNQUFNSSxPQUFPQyxTQUFTQyxhQUFhLENBQUM7WUFDcENGLEtBQUtHLFFBQVEsR0FBRyxDQUFDLFFBQVEsRUFBRXJGLFNBQVNzRixJQUFJLENBQUNDLFdBQVcsR0FBR0MsT0FBTyxDQUFDLFFBQVEsS0FBSyxDQUFDLEVBQUVDLEtBQUtDLEdBQUcsR0FBRyxJQUFJLENBQUM7WUFDL0ZSLEtBQUtTLElBQUksR0FBR2I7WUFDWkssU0FBU1MsSUFBSSxDQUFDQyxXQUFXLENBQUNYO1lBQzFCQSxLQUFLWSxLQUFLO1lBQ1ZYLFNBQVNTLElBQUksQ0FBQ0csV0FBVyxDQUFDYjtZQUUxQixJQUFJL0UsWUFBWTtnQkFDZEEsV0FBVzJFO1lBQ2I7UUFDRjtJQUNGO0lBRUEsTUFBTWtCLGdCQUFnQjtRQUNwQixNQUFNM0I7SUFDUjtJQUVBN0UsZ0RBQVNBLENBQUM7UUFDUiw0REFBNEQ7UUFDNUQsSUFBSVUsYUFBYStGLE1BQU0sR0FBRyxHQUFHO1lBQzNCRDtRQUNGO0lBQ0YsR0FBRztRQUFDaEc7UUFBVUU7UUFBY0Q7S0FBZTtJQUUzQyxNQUFNaUcsY0FBY2hHLGFBQWErRixNQUFNO0lBQ3ZDLE1BQU1FLGFBQWFuRyxTQUFTNkUsS0FBSyxDQUFDb0IsTUFBTTtJQUN4QyxNQUFNRyxhQUFhRixnQkFBZ0JDO0lBRW5DLHFCQUNFLDhEQUFDeEcsMENBQUlBOzswQkFDSCw4REFBQ0UsZ0RBQVVBOztrQ0FDVCw4REFBQ0MsK0NBQVNBO2tDQUFDOzs7Ozs7a0NBQ1gsOERBQUN1Rzt3QkFBSUMsV0FBVTtrQ0FDWkYsYUFBYSwyQkFBMkIsQ0FBQyxFQUFFRixZQUFZLElBQUksRUFBRUMsV0FBVyxhQUFhLENBQUM7Ozs7Ozs7Ozs7OzswQkFHM0YsOERBQUN2RyxpREFBV0E7MEJBQ1YsNEVBQUN5RztvQkFBSUMsV0FBVTs7c0NBRWIsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNoQzt3Q0FDQ2lDLEtBQUtuRzt3Q0FDTGtHLFdBQVU7d0NBQ1ZFLE9BQU87NENBQUVDLFdBQVc7d0NBQVE7Ozs7OztvQ0FFN0JwRyw2QkFDQyw4REFBQ2dHO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzVDLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM1Ryw4Q0FBTUE7b0NBQ0xnSCxTQUFRO29DQUNSQyxTQUFTWDtvQ0FDVFksVUFBVXZHLGVBQWVILGFBQWErRixNQUFNLEtBQUs7OENBRWhENUYsY0FBYyxpQkFBaUI7Ozs7Ozs4Q0FFbEMsOERBQUNYLDhDQUFNQTtvQ0FDTGlILFNBQVMxQjtvQ0FDVDJCLFVBQVV2RyxlQUFlLENBQUMrRjs4Q0FDM0I7Ozs7Ozs7Ozs7OztzQ0FNSCw4REFBQ0M7NEJBQUlDLFdBQVU7O2dDQUNaN0YsNkJBQ0MsOERBQUM0RjtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3hGO3dDQUFFd0YsV0FBVTs7MERBQ1gsOERBQUNPOzBEQUFPOzs7Ozs7NENBQWU7NENBQUVwRzs7Ozs7Ozs7Ozs7O2dDQUk5QixDQUFDMkYsY0FBYyxDQUFDM0YsNkJBQ2YsOERBQUNLOzhDQUFFOzs7Ozs7Z0NBRUpzRixjQUFjLENBQUMzRiw2QkFDZCw4REFBQ0s7b0NBQUV3RixXQUFVOzhDQUFpQjs7Ozs7Ozs7Ozs7O3NDQUtsQyw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDeEY7O3dDQUFFO3dDQUFjZCxTQUFTbUMsV0FBVzt3Q0FBQzt3Q0FBSW5DLFNBQVNzQyxZQUFZO3dDQUFDOzs7Ozs7OzhDQUNoRSw4REFBQ3hCOzhDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1mO0FBRUEsaUVBQWVmLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9waG90by1jb2xsYWdlLWFwcC8uL3NyYy9jb21wb25lbnRzL0NvbGxhZ2VSZW5kZXJlci50c3g/ZDlhMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VSZWYsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBDb2xsYWdlVGVtcGxhdGUsIFVwbG9hZGVkUGhvdG8sIFBsYWNlZFBob3RvLCBQaG90b1Nsb3QgfSBmcm9tICdAL3R5cGVzL3RlbXBsYXRlJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4vdWkvYnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICcuL3VpL2NhcmQnO1xuXG5pbnRlcmZhY2UgQ29sbGFnZVJlbmRlcmVyUHJvcHMge1xuICB0ZW1wbGF0ZTogQ29sbGFnZVRlbXBsYXRlO1xuICB1cGxvYWRlZFBob3RvczogVXBsb2FkZWRQaG90b1tdO1xuICBwbGFjZWRQaG90b3M6IFBsYWNlZFBob3RvW107XG4gIG9uRG93bmxvYWQ/OiAoZGF0YVVybDogc3RyaW5nKSA9PiB2b2lkO1xufVxuXG5jb25zdCBDb2xsYWdlUmVuZGVyZXI6IFJlYWN0LkZDPENvbGxhZ2VSZW5kZXJlclByb3BzPiA9ICh7XG4gIHRlbXBsYXRlLFxuICB1cGxvYWRlZFBob3RvcyxcbiAgcGxhY2VkUGhvdG9zLFxuICBvbkRvd25sb2FkXG59KSA9PiB7XG4gIGNvbnN0IGNhbnZhc1JlZiA9IHVzZVJlZjxIVE1MQ2FudmFzRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IFtpc1JlbmRlcmluZywgc2V0SXNSZW5kZXJpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcHJldmlld0RhdGFVcmwsIHNldFByZXZpZXdEYXRhVXJsXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbcmVuZGVyRXJyb3IsIHNldFJlbmRlckVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IGdldFVwbG9hZGVkUGhvdG8gPSAocGhvdG9JZDogc3RyaW5nKTogVXBsb2FkZWRQaG90byB8IHVuZGVmaW5lZCA9PiB7XG4gICAgcmV0dXJuIHVwbG9hZGVkUGhvdG9zLmZpbmQocCA9PiBwLmlkID09PSBwaG90b0lkKTtcbiAgfTtcblxuICBjb25zdCBnZXRQbGFjZWRQaG90byA9IChzbG90SWQ6IHN0cmluZyk6IFBsYWNlZFBob3RvIHwgdW5kZWZpbmVkID0+IHtcbiAgICByZXR1cm4gcGxhY2VkUGhvdG9zLmZpbmQocCA9PiBwLnNsb3RJZCA9PT0gc2xvdElkKTtcbiAgfTtcblxuICBjb25zdCBsb2FkSW1hZ2UgPSAoc3JjOiBzdHJpbmcpOiBQcm9taXNlPEhUTUxJbWFnZUVsZW1lbnQ+ID0+IHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgY29uc3QgaW1nID0gbmV3IEltYWdlKCk7XG4gICAgICBpbWcub25sb2FkID0gKCkgPT4gcmVzb2x2ZShpbWcpO1xuICAgICAgaW1nLm9uZXJyb3IgPSByZWplY3Q7XG4gICAgICBpbWcuc3JjID0gc3JjO1xuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IHJlbmRlclNsb3RUb0NhbnZhcyA9IGFzeW5jIChcbiAgICBjdHg6IENhbnZhc1JlbmRlcmluZ0NvbnRleHQyRCxcbiAgICBzbG90OiBQaG90b1Nsb3QsXG4gICAgcGxhY2VkUGhvdG86IFBsYWNlZFBob3RvLFxuICAgIHVwbG9hZGVkUGhvdG86IFVwbG9hZGVkUGhvdG9cbiAgKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGltZyA9IGF3YWl0IGxvYWRJbWFnZSh1cGxvYWRlZFBob3RvLnVybCk7XG4gICAgICBcbiAgICAgIC8vIENhbGN1bGF0ZSBzbG90IGRpbWVuc2lvbnMgaW4gcGl4ZWxzXG4gICAgICBjb25zdCBzbG90WCA9IChzbG90LnggLyAxMDApICogdGVtcGxhdGUuY2FudmFzV2lkdGg7XG4gICAgICBjb25zdCBzbG90WSA9IChzbG90LnkgLyAxMDApICogdGVtcGxhdGUuY2FudmFzSGVpZ2h0O1xuICAgICAgY29uc3Qgc2xvdFdpZHRoID0gKHNsb3Qud2lkdGggLyAxMDApICogdGVtcGxhdGUuY2FudmFzV2lkdGg7XG4gICAgICBjb25zdCBzbG90SGVpZ2h0ID0gKHNsb3QuaGVpZ2h0IC8gMTAwKSAqIHRlbXBsYXRlLmNhbnZhc0hlaWdodDtcblxuICAgICAgLy8gU2F2ZSBjYW52YXMgc3RhdGVcbiAgICAgIGN0eC5zYXZlKCk7XG5cbiAgICAgIC8vIENyZWF0ZSBjbGlwcGluZyBwYXRoIGZvciB0aGUgc2xvdCBzaGFwZVxuICAgICAgY3R4LmJlZ2luUGF0aCgpO1xuICAgICAgaWYgKHNsb3Quc2hhcGUgPT09ICdjaXJjbGUnKSB7XG4gICAgICAgIGNvbnN0IGNlbnRlclggPSBzbG90WCArIHNsb3RXaWR0aCAvIDI7XG4gICAgICAgIGNvbnN0IGNlbnRlclkgPSBzbG90WSArIHNsb3RIZWlnaHQgLyAyO1xuICAgICAgICBjb25zdCByYWRpdXMgPSBNYXRoLm1pbihzbG90V2lkdGgsIHNsb3RIZWlnaHQpIC8gMjtcbiAgICAgICAgY3R4LmFyYyhjZW50ZXJYLCBjZW50ZXJZLCByYWRpdXMsIDAsIDIgKiBNYXRoLlBJKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIFJlY3RhbmdsZSBvciBjdXN0b20gc2hhcGVcbiAgICAgICAgY3R4LnJlY3Qoc2xvdFgsIHNsb3RZLCBzbG90V2lkdGgsIHNsb3RIZWlnaHQpO1xuICAgICAgfVxuICAgICAgY3R4LmNsaXAoKTtcblxuICAgICAgLy8gQXBwbHkgc2xvdCByb3RhdGlvbiBpZiBhbnlcbiAgICAgIGlmIChzbG90LnJvdGF0aW9uKSB7XG4gICAgICAgIGNvbnN0IGNlbnRlclggPSBzbG90WCArIHNsb3RXaWR0aCAvIDI7XG4gICAgICAgIGNvbnN0IGNlbnRlclkgPSBzbG90WSArIHNsb3RIZWlnaHQgLyAyO1xuICAgICAgICBjdHgudHJhbnNsYXRlKGNlbnRlclgsIGNlbnRlclkpO1xuICAgICAgICBjdHgucm90YXRlKChzbG90LnJvdGF0aW9uICogTWF0aC5QSSkgLyAxODApO1xuICAgICAgICBjdHgudHJhbnNsYXRlKC1jZW50ZXJYLCAtY2VudGVyWSk7XG4gICAgICB9XG5cbiAgICAgIC8vIENhbGN1bGF0ZSBpbWFnZSBwb3NpdGlvbmluZyBhbmQgc2NhbGluZ1xuICAgICAgY29uc3Qgc2NhbGUgPSBwbGFjZWRQaG90by5zY2FsZSB8fCAxO1xuICAgICAgY29uc3Qgcm90YXRpb24gPSBwbGFjZWRQaG90by5yb3RhdGlvbiB8fCAwO1xuICAgICAgY29uc3QgcG9zWCA9IChwbGFjZWRQaG90by54IHx8IDUwKSAvIDEwMDtcbiAgICAgIGNvbnN0IHBvc1kgPSAocGxhY2VkUGhvdG8ueSB8fCA1MCkgLyAxMDA7XG5cbiAgICAgIC8vIENhbGN1bGF0ZSBzY2FsZWQgaW1hZ2UgZGltZW5zaW9uc1xuICAgICAgY29uc3Qgc2NhbGVkV2lkdGggPSBzbG90V2lkdGggKiBzY2FsZTtcbiAgICAgIGNvbnN0IHNjYWxlZEhlaWdodCA9IHNsb3RIZWlnaHQgKiBzY2FsZTtcblxuICAgICAgLy8gQ2FsY3VsYXRlIHBvc2l0aW9uIG9mZnNldCBiYXNlZCBvbiBwb3NpdGlvbmluZ1xuICAgICAgY29uc3Qgb2Zmc2V0WCA9IHNsb3RYICsgKHNsb3RXaWR0aCAtIHNjYWxlZFdpZHRoKSAqIHBvc1g7XG4gICAgICBjb25zdCBvZmZzZXRZID0gc2xvdFkgKyAoc2xvdEhlaWdodCAtIHNjYWxlZEhlaWdodCkgKiBwb3NZO1xuXG4gICAgICAvLyBBcHBseSBpbWFnZSByb3RhdGlvblxuICAgICAgaWYgKHJvdGF0aW9uICE9PSAwKSB7XG4gICAgICAgIGNvbnN0IGNlbnRlclggPSBvZmZzZXRYICsgc2NhbGVkV2lkdGggLyAyO1xuICAgICAgICBjb25zdCBjZW50ZXJZID0gb2Zmc2V0WSArIHNjYWxlZEhlaWdodCAvIDI7XG4gICAgICAgIGN0eC50cmFuc2xhdGUoY2VudGVyWCwgY2VudGVyWSk7XG4gICAgICAgIGN0eC5yb3RhdGUoKHJvdGF0aW9uICogTWF0aC5QSSkgLyAxODApO1xuICAgICAgICBjdHgudHJhbnNsYXRlKC1jZW50ZXJYLCAtY2VudGVyWSk7XG4gICAgICB9XG5cbiAgICAgIC8vIERyYXcgdGhlIGltYWdlXG4gICAgICBjdHguZHJhd0ltYWdlKGltZywgb2Zmc2V0WCwgb2Zmc2V0WSwgc2NhbGVkV2lkdGgsIHNjYWxlZEhlaWdodCk7XG5cbiAgICAgIC8vIFJlc3RvcmUgY2FudmFzIHN0YXRlXG4gICAgICBjdHgucmVzdG9yZSgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZW5kZXJpbmcgc2xvdDonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbmRlckNvbGxhZ2UgPSBhc3luYyAoKTogUHJvbWlzZTxzdHJpbmcgfCBudWxsPiA9PiB7XG4gICAgY29uc3QgY2FudmFzID0gY2FudmFzUmVmLmN1cnJlbnQ7XG4gICAgaWYgKCFjYW52YXMpIHJldHVybiBudWxsO1xuXG4gICAgc2V0SXNSZW5kZXJpbmcodHJ1ZSk7XG4gICAgc2V0UmVuZGVyRXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgY3R4ID0gY2FudmFzLmdldENvbnRleHQoJzJkJyk7XG4gICAgICBpZiAoIWN0eCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NvdWxkIG5vdCBnZXQgY2FudmFzIGNvbnRleHQnKTtcbiAgICAgIH1cblxuICAgICAgLy8gU2V0IGNhbnZhcyBkaW1lbnNpb25zXG4gICAgICBjYW52YXMud2lkdGggPSB0ZW1wbGF0ZS5jYW52YXNXaWR0aDtcbiAgICAgIGNhbnZhcy5oZWlnaHQgPSB0ZW1wbGF0ZS5jYW52YXNIZWlnaHQ7XG5cbiAgICAgIC8vIENsZWFyIGNhbnZhcyBhbmQgc2V0IGJhY2tncm91bmRcbiAgICAgIGN0eC5maWxsU3R5bGUgPSB0ZW1wbGF0ZS5iYWNrZ3JvdW5kQ29sb3IgfHwgJyNmZmZmZmYnO1xuICAgICAgY3R4LmZpbGxSZWN0KDAsIDAsIHRlbXBsYXRlLmNhbnZhc1dpZHRoLCB0ZW1wbGF0ZS5jYW52YXNIZWlnaHQpO1xuXG4gICAgICAvLyBSZW5kZXIgZWFjaCBzbG90IHdpdGggaXRzIHBob3RvXG4gICAgICBmb3IgKGNvbnN0IHNsb3Qgb2YgdGVtcGxhdGUuc2xvdHMpIHtcbiAgICAgICAgY29uc3QgcGxhY2VkUGhvdG8gPSBnZXRQbGFjZWRQaG90byhzbG90LmlkKTtcbiAgICAgICAgaWYgKHBsYWNlZFBob3RvKSB7XG4gICAgICAgICAgY29uc3QgdXBsb2FkZWRQaG90byA9IGdldFVwbG9hZGVkUGhvdG8ocGxhY2VkUGhvdG8ucGhvdG9JZCk7XG4gICAgICAgICAgaWYgKHVwbG9hZGVkUGhvdG8pIHtcbiAgICAgICAgICAgIGF3YWl0IHJlbmRlclNsb3RUb0NhbnZhcyhjdHgsIHNsb3QsIHBsYWNlZFBob3RvLCB1cGxvYWRlZFBob3RvKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gR2V0IHRoZSBkYXRhIFVSTFxuICAgICAgY29uc3QgZGF0YVVybCA9IGNhbnZhcy50b0RhdGFVUkwoJ2ltYWdlL3BuZycsIDEuMCk7XG4gICAgICBzZXRQcmV2aWV3RGF0YVVybChkYXRhVXJsKTtcbiAgICAgIHJldHVybiBkYXRhVXJsO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZW5kZXJpbmcgY29sbGFnZTonLCBlcnJvcik7XG4gICAgICBzZXRSZW5kZXJFcnJvcihlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdGYWlsZWQgdG8gcmVuZGVyIGNvbGxhZ2UnKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1JlbmRlcmluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURvd25sb2FkID0gYXN5bmMgKCkgPT4ge1xuICAgIGNvbnN0IGRhdGFVcmwgPSBhd2FpdCByZW5kZXJDb2xsYWdlKCk7XG4gICAgaWYgKGRhdGFVcmwpIHtcbiAgICAgIC8vIENyZWF0ZSBkb3dubG9hZCBsaW5rXG4gICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgICAgbGluay5kb3dubG9hZCA9IGBjb2xsYWdlLSR7dGVtcGxhdGUubmFtZS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xccysvZywgJy0nKX0tJHtEYXRlLm5vdygpfS5wbmdgO1xuICAgICAgbGluay5ocmVmID0gZGF0YVVybDtcbiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7XG4gICAgICBsaW5rLmNsaWNrKCk7XG4gICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGxpbmspO1xuXG4gICAgICBpZiAob25Eb3dubG9hZCkge1xuICAgICAgICBvbkRvd25sb2FkKGRhdGFVcmwpO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVQcmV2aWV3ID0gYXN5bmMgKCkgPT4ge1xuICAgIGF3YWl0IHJlbmRlckNvbGxhZ2UoKTtcbiAgfTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIEF1dG8tcmVuZGVyIHByZXZpZXcgd2hlbiBjb21wb25lbnQgbW91bnRzIG9yIGRhdGEgY2hhbmdlc1xuICAgIGlmIChwbGFjZWRQaG90b3MubGVuZ3RoID4gMCkge1xuICAgICAgaGFuZGxlUHJldmlldygpO1xuICAgIH1cbiAgfSwgW3RlbXBsYXRlLCBwbGFjZWRQaG90b3MsIHVwbG9hZGVkUGhvdG9zXSk7XG5cbiAgY29uc3QgZmlsbGVkU2xvdHMgPSBwbGFjZWRQaG90b3MubGVuZ3RoO1xuICBjb25zdCB0b3RhbFNsb3RzID0gdGVtcGxhdGUuc2xvdHMubGVuZ3RoO1xuICBjb25zdCBpc0NvbXBsZXRlID0gZmlsbGVkU2xvdHMgPT09IHRvdGFsU2xvdHM7XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZFRpdGxlPkNvbGxhZ2UgUHJldmlldzwvQ2FyZFRpdGxlPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgIHtpc0NvbXBsZXRlID8gJ1lvdXIgY29sbGFnZSBpcyByZWFkeSEnIDogYCR7ZmlsbGVkU2xvdHN9IG9mICR7dG90YWxTbG90c30gc2xvdHMgZmlsbGVkYH1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgey8qIFByZXZpZXcgQ2FudmFzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICA8Y2FudmFzXG4gICAgICAgICAgICAgICAgcmVmPXtjYW52YXNSZWZ9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIHJvdW5kZWQtbGcgc2hhZG93LXNtIG1heC13LWZ1bGwgaC1hdXRvXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBtYXhIZWlnaHQ6ICc0MDBweCcgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAge2lzUmVuZGVyaW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbVwiPlJlbmRlcmluZy4uLjwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQ29udHJvbHMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlUHJldmlld31cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzUmVuZGVyaW5nIHx8IHBsYWNlZFBob3Rvcy5sZW5ndGggPT09IDB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpc1JlbmRlcmluZyA/ICdSZW5kZXJpbmcuLi4nIDogJ1JlZnJlc2ggUHJldmlldyd9XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRG93bmxvYWR9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc1JlbmRlcmluZyB8fCAhaXNDb21wbGV0ZX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgRG93bmxvYWQgQ29sbGFnZVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU3RhdHVzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICB7cmVuZGVyRXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTMgcC0zIGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTcwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPkVycm9yOjwvc3Ryb25nPiB7cmVuZGVyRXJyb3J9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICB7IWlzQ29tcGxldGUgJiYgIXJlbmRlckVycm9yICYmIChcbiAgICAgICAgICAgICAgPHA+QWRkIHBob3RvcyB0byBhbGwgc2xvdHMgdG8gZW5hYmxlIGRvd25sb2FkPC9wPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHtpc0NvbXBsZXRlICYmICFyZW5kZXJFcnJvciAmJiAoXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwXCI+4pyTIFJlYWR5IHRvIGRvd25sb2FkITwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogVGVjaG5pY2FsIEluZm8gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgdGV4dC1jZW50ZXIgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICA8cD5DYW52YXMgU2l6ZToge3RlbXBsYXRlLmNhbnZhc1dpZHRofSDDlyB7dGVtcGxhdGUuY2FudmFzSGVpZ2h0fXB4PC9wPlxuICAgICAgICAgICAgPHA+Rm9ybWF0OiBQTkcg4oCiIFF1YWxpdHk6IEhpZ2g8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDb2xsYWdlUmVuZGVyZXI7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDb2xsYWdlUmVuZGVyZXIiLCJ0ZW1wbGF0ZSIsInVwbG9hZGVkUGhvdG9zIiwicGxhY2VkUGhvdG9zIiwib25Eb3dubG9hZCIsImNhbnZhc1JlZiIsImlzUmVuZGVyaW5nIiwic2V0SXNSZW5kZXJpbmciLCJwcmV2aWV3RGF0YVVybCIsInNldFByZXZpZXdEYXRhVXJsIiwicmVuZGVyRXJyb3IiLCJzZXRSZW5kZXJFcnJvciIsImdldFVwbG9hZGVkUGhvdG8iLCJwaG90b0lkIiwiZmluZCIsInAiLCJpZCIsImdldFBsYWNlZFBob3RvIiwic2xvdElkIiwibG9hZEltYWdlIiwic3JjIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJpbWciLCJJbWFnZSIsIm9ubG9hZCIsIm9uZXJyb3IiLCJyZW5kZXJTbG90VG9DYW52YXMiLCJjdHgiLCJzbG90IiwicGxhY2VkUGhvdG8iLCJ1cGxvYWRlZFBob3RvIiwidXJsIiwic2xvdFgiLCJ4IiwiY2FudmFzV2lkdGgiLCJzbG90WSIsInkiLCJjYW52YXNIZWlnaHQiLCJzbG90V2lkdGgiLCJ3aWR0aCIsInNsb3RIZWlnaHQiLCJoZWlnaHQiLCJzYXZlIiwiYmVnaW5QYXRoIiwic2hhcGUiLCJjZW50ZXJYIiwiY2VudGVyWSIsInJhZGl1cyIsIk1hdGgiLCJtaW4iLCJhcmMiLCJQSSIsInJlY3QiLCJjbGlwIiwicm90YXRpb24iLCJ0cmFuc2xhdGUiLCJyb3RhdGUiLCJzY2FsZSIsInBvc1giLCJwb3NZIiwic2NhbGVkV2lkdGgiLCJzY2FsZWRIZWlnaHQiLCJvZmZzZXRYIiwib2Zmc2V0WSIsImRyYXdJbWFnZSIsInJlc3RvcmUiLCJlcnJvciIsImNvbnNvbGUiLCJyZW5kZXJDb2xsYWdlIiwiY2FudmFzIiwiY3VycmVudCIsImdldENvbnRleHQiLCJFcnJvciIsImZpbGxTdHlsZSIsImJhY2tncm91bmRDb2xvciIsImZpbGxSZWN0Iiwic2xvdHMiLCJkYXRhVXJsIiwidG9EYXRhVVJMIiwibWVzc2FnZSIsImhhbmRsZURvd25sb2FkIiwibGluayIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsImRvd25sb2FkIiwibmFtZSIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsIkRhdGUiLCJub3ciLCJocmVmIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsImhhbmRsZVByZXZpZXciLCJsZW5ndGgiLCJmaWxsZWRTbG90cyIsInRvdGFsU2xvdHMiLCJpc0NvbXBsZXRlIiwiZGl2IiwiY2xhc3NOYW1lIiwicmVmIiwic3R5bGUiLCJtYXhIZWlnaHQiLCJ2YXJpYW50Iiwib25DbGljayIsImRpc2FibGVkIiwic3Ryb25nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CollageRenderer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ImageUpload = ({ onPhotosUploaded, maxFiles = 20, acceptedTypes = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\"\n] })=>{\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            const img = new Image();\n            reader.onload = (e)=>{\n                const url = e.target?.result;\n                img.onload = ()=>{\n                    const photo = {\n                        id: `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                        file,\n                        url,\n                        width: img.width,\n                        height: img.height\n                    };\n                    resolve(photo);\n                };\n                img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n                img.src = url;\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsDataURL(file);\n        });\n    }, []);\n    const validateFile = (file)=>{\n        if (!acceptedTypes.includes(file.type)) {\n            return `File type ${file.type} is not supported. Please use JPEG, PNG, or WebP images.`;\n        }\n        if (file.size > 10 * 1024 * 1024) {\n            return \"File size must be less than 10MB.\";\n        }\n        return null;\n    };\n    const processFiles = async (files)=>{\n        setIsUploading(true);\n        const newPhotos = [];\n        const errors = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            if (uploadedPhotos.length + newPhotos.length >= maxFiles) {\n                errors.push(`Maximum ${maxFiles} files allowed. Some files were skipped.`);\n                break;\n            }\n            const validationError = validateFile(file);\n            if (validationError) {\n                errors.push(`${file.name}: ${validationError}`);\n                continue;\n            }\n            try {\n                const photo = await createPhotoFromFile(file);\n                newPhotos.push(photo);\n            } catch (error) {\n                errors.push(`${file.name}: Failed to process image`);\n            }\n        }\n        if (errors.length > 0) {\n            // Better error handling - could be replaced with a toast notification\n            console.error(\"Upload errors:\", errors);\n            alert(errors.join(\"\\n\"));\n        }\n        const updatedPhotos = [\n            ...uploadedPhotos,\n            ...newPhotos\n        ];\n        setUploadedPhotos(updatedPhotos);\n        onPhotosUploaded(updatedPhotos);\n        setIsUploading(false);\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const removePhoto = (photoId)=>{\n        const updatedPhotos = uploadedPhotos.filter((photo)=>photo.id !== photoId);\n        setUploadedPhotos(updatedPhotos);\n        onPhotosUploaded(updatedPhotos);\n    };\n    const openFileDialog = ()=>{\n        fileInputRef.current?.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: `mb-6 transition-all duration-200 ${isDragOver ? \"border-blue-500 border-2 bg-blue-50\" : \"border-dashed border-2 border-gray-300\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center cursor-pointer\",\n                        onDragOver: handleDragOver,\n                        onDragLeave: handleDragLeave,\n                        onDrop: handleDrop,\n                        onClick: openFileDialog,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: isUploading ? \"⏳\" : isDragOver ? \"\\uD83D\\uDCE4\" : \"\\uD83D\\uDCF7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: isUploading ? \"Processing images...\" : \"Upload Your Photos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: isDragOver ? \"Drop your images here\" : \"Drag and drop images here, or click to select files\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Supported formats: JPEG, PNG, WebP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Maximum file size: 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Maximum \",\n                                            maxFiles,\n                                            \" files\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            !isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"mt-4\",\n                                disabled: isUploading,\n                                children: \"Choose Files\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                multiple: true,\n                accept: acceptedTypes.join(\",\"),\n                onChange: handleFileSelect,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            uploadedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: [\n                                        \"Uploaded Photos (\",\n                                        uploadedPhotos.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        setUploadedPhotos([]);\n                                        onPhotosUploaded([]);\n                                    },\n                                    children: \"Clear All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                            children: uploadedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: photo.url,\n                                                alt: \"Uploaded photo\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removePhoto(photo.id),\n                                            className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-xs text-gray-500 truncate\",\n                                            children: photo.file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                photo.width,\n                                                \" \\xd7 \",\n                                                photo.height\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, photo.id, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageUpload);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PhotoControls.tsx":
/*!******************************************!*\
  !*** ./src/components/PhotoControls.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PhotoControls = ({ placedPhoto, uploadedPhoto, onUpdate, onRemove })=>{\n    const handleScaleChange = (scale)=>{\n        onUpdate({\n            scale: Math.max(0.1, Math.min(3.0, scale))\n        });\n    };\n    const handleRotationChange = (rotation)=>{\n        onUpdate({\n            rotation: rotation % 360\n        });\n    };\n    const handlePositionChange = (x, y)=>{\n        onUpdate({\n            x: Math.max(0, Math.min(100, x)),\n            y: Math.max(0, Math.min(100, y))\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-sm\",\n                    children: \"Photo Controls\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-square w-20 mx-auto bg-gray-100 rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: uploadedPhoto.url,\n                            alt: \"Photo preview\",\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Scale: \",\n                                    (placedPhoto.scale * 100).toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale - 0.1),\n                                        disabled: placedPhoto.scale <= 0.1,\n                                        className: \"touch-manipulation\",\n                                        children: \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0.1\",\n                                        max: \"3.0\",\n                                        step: \"0.1\",\n                                        value: placedPhoto.scale,\n                                        onChange: (e)=>handleScaleChange(parseFloat(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale + 0.1),\n                                        disabled: placedPhoto.scale >= 3.0,\n                                        className: \"touch-manipulation\",\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Rotation: \",\n                                    placedPhoto.rotation,\n                                    \"\\xb0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation - 15),\n                                        className: \"touch-manipulation\",\n                                        children: \"↺\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"360\",\n                                        step: \"15\",\n                                        value: placedPhoto.rotation,\n                                        onChange: (e)=>handleRotationChange(parseInt(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation + 15),\n                                        className: \"touch-manipulation\",\n                                        children: \"↻\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: \"Position\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"X: \",\n                                                    placedPhoto.x.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.x,\n                                                onChange: (e)=>handlePositionChange(parseInt(e.target.value), placedPhoto.y),\n                                                className: \"w-full touch-manipulation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Y: \",\n                                                    placedPhoto.y.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.y,\n                                                onChange: (e)=>handlePositionChange(placedPhoto.x, parseInt(e.target.value)),\n                                                className: \"w-full touch-manipulation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onUpdate({\n                                        scale: 1.0,\n                                        rotation: 0,\n                                        x: 50,\n                                        y: 50\n                                    }),\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"destructive\",\n                                onClick: onRemove,\n                                children: \"Remove\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit width\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio > 1 ? 1.0 : aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Width\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit height\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio < 1 ? 1.0 : 1 / aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Height\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PhotoControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PhotoControls.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TemplateGallery.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateGallery.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(ssr)/./src/data/templates.ts\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(ssr)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst TemplateGallery = ({ onTemplateSelect })=>{\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: \"all\",\n            name: \"All Templates\",\n            icon: \"\\uD83C\\uDFA8\"\n        },\n        {\n            id: \"grid\",\n            name: \"Grid Layouts\",\n            icon: \"⚏\"\n        },\n        {\n            id: \"heart\",\n            name: \"Heart Shapes\",\n            icon: \"❤️\"\n        },\n        {\n            id: \"letter\",\n            name: \"Letters\",\n            icon: \"\\uD83D\\uDD24\"\n        },\n        {\n            id: \"number\",\n            name: \"Numbers\",\n            icon: \"\\uD83D\\uDD22\"\n        },\n        {\n            id: \"shape\",\n            name: \"Shapes\",\n            icon: \"\\uD83D\\uDD35\"\n        }\n    ];\n    const filteredTemplates = selectedCategory === \"all\" ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((template)=>template.category === selectedCategory);\n    const handleTemplateClick = (template)=>{\n        setSelectedTemplate(template);\n    };\n    const handleUseTemplate = ()=>{\n        if (selectedTemplate) {\n            onTemplateSelect(selectedTemplate);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            children: \"Choose Your Template\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                            children: \"Select a template to start creating your collage. Browse by category or view all available options.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedCategory(category.id),\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: category.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                category.name\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `group cursor-pointer transition-all duration-200 ${selectedTemplate?.id === template.id ? \"ring-2 ring-blue-500 ring-offset-2\" : \"hover:shadow-lg\"}`,\n                                    onClick: ()=>handleTemplateClick(template),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        template: template,\n                                                        width: 250,\n                                                        showSlotNumbers: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm mb-2\",\n                                                            children: template.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center text-xs text-gray-500 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-100 px-2 py-1 rounded\",\n                                                                    children: template.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                                    lineNumber: 95,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        template.slots.length,\n                                                                        \" photos\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        selectedTemplate?.id === template.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-600 text-sm font-medium\",\n                                                            children: \"✓ Selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, template.id, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        filteredTemplates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-6xl mb-4\",\n                                    children: \"\\uD83C\\uDFA8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                    children: \"No templates found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Try selecting a different category to see more templates.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-lg text-blue-900\",\n                                                children: selectedTemplate.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-700 text-sm\",\n                                                children: [\n                                                    \"Ready to create your collage with \",\n                                                    selectedTemplate.slots.length,\n                                                    \" photos\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setSelectedTemplate(null),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                onClick: handleUseTemplate,\n                                                children: \"Use This Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplateGallery);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TemplateGallery.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TemplatePreview.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplatePreview.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst TemplatePreview = ({ template, width = 200, height = 200, showSlotNumbers = false })=>{\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const previewHeight = width / aspectRatio;\n    const renderSlot = (slot, index)=>{\n        const slotStyle = {\n            position: \"absolute\",\n            left: `${slot.x}%`,\n            top: `${slot.y}%`,\n            width: `${slot.width}%`,\n            height: `${slot.height}%`,\n            backgroundColor: \"#e5e7eb\",\n            border: \"2px solid #9ca3af\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            fontSize: \"10px\",\n            color: \"#6b7280\",\n            fontWeight: \"bold\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = `rotate(${slot.rotation}deg)`;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            children: showSlotNumbers && index + 1\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative border rounded-lg overflow-hidden shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                style: {\n                    width: `${width}px`,\n                    height: `${previewHeight}px`,\n                    backgroundColor: template.backgroundColor || \"#ffffff\"\n                },\n                children: template.slots.map((slot, index)=>renderSlot(slot, index))\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-semibold\",\n                        children: template.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"opacity-75\",\n                        children: [\n                            template.slots.length,\n                            \" photos\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplatePreview.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TemplatePreview);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TemplatePreview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/templates.ts":
/*!*******************************!*\
  !*** ./src/data/templates.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTemplateById: () => (/* binding */ getTemplateById),\n/* harmony export */   getTemplatesByCategory: () => (/* binding */ getTemplatesByCategory),\n/* harmony export */   templates: () => (/* binding */ templates)\n/* harmony export */ });\nconst templates = [\n    // Grid Layout Template\n    {\n        id: \"grid-4x4\",\n        name: \"4x4 Grid\",\n        description: \"Classic 16-photo grid layout\",\n        category: \"grid\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#ffffff\",\n        slots: [\n            // Row 1\n            {\n                id: \"slot-1\",\n                x: 2,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-2\",\n                x: 27,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-3\",\n                x: 52,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-4\",\n                x: 77,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 2\n            {\n                id: \"slot-5\",\n                x: 2,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-6\",\n                x: 27,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-7\",\n                x: 52,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-8\",\n                x: 77,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 3\n            {\n                id: \"slot-9\",\n                x: 2,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-10\",\n                x: 27,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-11\",\n                x: 52,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-12\",\n                x: 77,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 4\n            {\n                id: \"slot-13\",\n                x: 2,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-14\",\n                x: 27,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-15\",\n                x: 52,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-16\",\n                x: 77,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Heart Shape Template\n    {\n        id: \"heart-shape\",\n        name: \"Heart Collage\",\n        description: \"Romantic heart-shaped photo arrangement\",\n        category: \"heart\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 700,\n        backgroundColor: \"#ffe6f2\",\n        slots: [\n            // Top left curve\n            {\n                id: \"heart-1\",\n                x: 15,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-2\",\n                x: 32,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-3\",\n                x: 10,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Top right curve\n            {\n                id: \"heart-4\",\n                x: 55,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-5\",\n                x: 72,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-6\",\n                x: 78,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Center area\n            {\n                id: \"heart-7\",\n                x: 35,\n                y: 35,\n                width: 30,\n                height: 20,\n                shape: \"rectangle\"\n            },\n            // Lower sections\n            {\n                id: \"heart-8\",\n                x: 25,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-9\",\n                x: 57,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-10\",\n                x: 42,\n                y: 75,\n                width: 16,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Letter 'A' Template\n    {\n        id: \"letter-a\",\n        name: \"Letter A\",\n        description: \"Letter A shaped photo collage\",\n        category: \"letter\",\n        thumbnail: \"\",\n        canvasWidth: 600,\n        canvasHeight: 800,\n        backgroundColor: \"#f0f8ff\",\n        slots: [\n            // Top point\n            {\n                id: \"a-top\",\n                x: 45,\n                y: 5,\n                width: 10,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Upper left diagonal\n            {\n                id: \"a-ul1\",\n                x: 35,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            {\n                id: \"a-ul2\",\n                x: 25,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            // Upper right diagonal\n            {\n                id: \"a-ur1\",\n                x: 53,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            {\n                id: \"a-ur2\",\n                x: 63,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            // Cross bar\n            {\n                id: \"a-cross1\",\n                x: 35,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-cross2\",\n                x: 53,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            // Lower left leg\n            {\n                id: \"a-ll1\",\n                x: 15,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-ll2\",\n                x: 15,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Lower right leg\n            {\n                id: \"a-lr1\",\n                x: 73,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-lr2\",\n                x: 73,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Number '1' Template\n    {\n        id: \"number-1\",\n        name: \"Number 1\",\n        description: \"Number 1 shaped photo collage\",\n        category: \"number\",\n        thumbnail: \"\",\n        canvasWidth: 400,\n        canvasHeight: 800,\n        backgroundColor: \"#fff5ee\",\n        slots: [\n            // Top diagonal\n            {\n                id: \"num1-top\",\n                x: 25,\n                y: 5,\n                width: 15,\n                height: 12,\n                shape: \"rectangle\",\n                rotation: 45\n            },\n            // Main vertical line\n            {\n                id: \"num1-1\",\n                x: 40,\n                y: 15,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-2\",\n                x: 40,\n                y: 32,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-3\",\n                x: 40,\n                y: 49,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-4\",\n                x: 40,\n                y: 66,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Bottom base\n            {\n                id: \"num1-base1\",\n                x: 20,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base2\",\n                x: 40,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base3\",\n                x: 60,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Circular Pattern Template\n    {\n        id: \"circle-pattern\",\n        name: \"Circle Pattern\",\n        description: \"Circular arrangement of photos\",\n        category: \"shape\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#f5f5f5\",\n        slots: [\n            // Center circle\n            {\n                id: \"center\",\n                x: 37.5,\n                y: 37.5,\n                width: 25,\n                height: 25,\n                shape: \"circle\"\n            },\n            // Inner ring (8 photos)\n            {\n                id: \"inner-1\",\n                x: 50,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-2\",\n                x: 70,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-3\",\n                x: 80,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-4\",\n                x: 70,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-5\",\n                x: 50,\n                y: 85,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-6\",\n                x: 25,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-7\",\n                x: 15,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-8\",\n                x: 25,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            // Outer ring (4 photos)\n            {\n                id: \"outer-1\",\n                x: 50,\n                y: 2,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-2\",\n                x: 88,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-3\",\n                x: 50,\n                y: 88,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-4\",\n                x: 2,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            }\n        ]\n    }\n];\nconst getTemplateById = (id)=>{\n    return templates.find((template)=>template.id === id);\n};\nconst getTemplatesByCategory = (category)=>{\n    return templates.filter((template)=>template.category === category);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/templates.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Photo Collage Maker\",\n    description: \"Create beautiful photo collages with custom templates\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1Bob3RvIENvbGxhZ2UgTWFrZXInLFxuICBkZXNjcmlwdGlvbjogJ0NyZWF0ZSBiZWF1dGlmdWwgcGhvdG8gY29sbGFnZXMgd2l0aCBjdXN0b20gdGVtcGxhdGVzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_CollageApp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/CollageApp */ \"(rsc)/./src/components/CollageApp.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CollageApp__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWlEO0FBRWxDLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCw4REFBVUE7Ozs7O0FBQ3BCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IENvbGxhZ2VBcHAgZnJvbSAnQC9jb21wb25lbnRzL0NvbGxhZ2VBcHAnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICByZXR1cm4gPENvbGxhZ2VBcHAgLz47XG59XG4iXSwibmFtZXMiOlsiQ29sbGFnZUFwcCIsIkhvbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\CollageApp.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();