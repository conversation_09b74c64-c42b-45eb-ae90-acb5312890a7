{"c": ["app/page", "app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cshared%5Clib%5Clazy-dynamic%5Cdynamic-no-ssr.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CUnifiedEditor.tsx&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js", "(app-pages-browser)/./node_modules/next/navigation.js", "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs", "(app-pages-browser)/./src/components/LiveCanvas.tsx", "(app-pages-browser)/./src/components/PhotoControls.tsx", "(app-pages-browser)/./src/components/PhotoUploadSidebar.tsx", "(app-pages-browser)/./src/components/TemplatePreview.tsx", "(app-pages-browser)/./src/components/TemplateSidebar.tsx", "(app-pages-browser)/./src/components/UnifiedEditor.tsx", "(app-pages-browser)/./src/components/ui/badge.tsx", "(app-pages-browser)/./src/components/ui/button.tsx", "(app-pages-browser)/./src/components/ui/card.tsx", "(app-pages-browser)/./src/components/ui/input.tsx", "(app-pages-browser)/./src/components/ui/toast.tsx", "(app-pages-browser)/./src/data/templates.ts", "(app-pages-browser)/./src/lib/imageOptimization.ts", "(app-pages-browser)/./src/lib/utils.ts"]}