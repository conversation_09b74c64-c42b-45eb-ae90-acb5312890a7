"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(app-pages-browser)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(app-pages-browser)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImageUpload */ \"(app-pages-browser)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _CollageEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CollageEditor */ \"(app-pages-browser)/./src/components/CollageEditor.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CollageApp = ()=>{\n    _s();\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const handlePlacedPhotosChange = (newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    };\n    const handlePreview = ()=>{\n        setCurrentState(\"preview\");\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 132,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, undefined),\n                uploadedPhotos.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                children: [\n                                                    \"Add photos to use in your collage. You need \",\n                                                    selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length,\n                                                    \" photos for this template.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            onPhotosUploaded: setUploadedPhotos,\n                                            maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Template Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    template: selectedTemplate,\n                                                    width: 280,\n                                                    showSlotNumbers: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Photo Slots:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            children: [\n                                                \"Add More Photos (\",\n                                                uploadedPhotos.length,\n                                                \" uploaded)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                            children: \"You can upload more photos or start arranging the ones you have.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onPhotosUploaded: setUploadedPhotos,\n                                        maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollageEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            template: selectedTemplate,\n                            uploadedPhotos: uploadedPhotos,\n                            onPlacedPhotosChange: handlePlacedPhotosChange,\n                            onPreview: handlePreview\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 147,\n            columnNumber: 5\n        }, undefined);\n    const renderPreviewScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Preview: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Your collage is ready! You can download it or go back to make changes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setCurrentState(\"editing\"),\n                                    children: \"← Back to Editor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDDBC️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                    children: \"Preview Coming Soon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-6\",\n                                    children: \"The preview and download functionality is currently under development. You can go back to the editor to continue working on your collage.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Template: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 28\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Photos Placed: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: [\n                                                        placedPhotos.length,\n                                                        \" of \",\n                                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Canvas Size: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: [\n                                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                        \" \\xd7 \",\n                                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                        \"px\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 244,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen(),\n                currentState === \"preview\" && renderPreviewScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 288,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageApp, \"VRQBFVBNyGs2ie7xBu6SudtNgyg=\");\n_c = CollageApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageApp);\nvar _c;\n$RefreshReg$(_c, \"CollageApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageApp.tsx\n"));

/***/ })

});