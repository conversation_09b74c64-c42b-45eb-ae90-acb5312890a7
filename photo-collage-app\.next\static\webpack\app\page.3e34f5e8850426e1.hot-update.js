"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PhotoControls.tsx":
/*!******************************************!*\
  !*** ./src/components/PhotoControls.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PhotoControls = (param)=>{\n    let { placedPhoto, uploadedPhoto, onUpdate, onRemove } = param;\n    const handleScaleChange = (scale)=>{\n        onUpdate({\n            scale: Math.max(0.1, Math.min(3.0, scale))\n        });\n    };\n    const handleRotationChange = (rotation)=>{\n        onUpdate({\n            rotation: rotation % 360\n        });\n    };\n    const handlePositionChange = (x, y)=>{\n        onUpdate({\n            x: Math.max(0, Math.min(100, x)),\n            y: Math.max(0, Math.min(100, y))\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-sm\",\n                    children: \"Photo Controls\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-square w-20 mx-auto bg-gray-100 rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: uploadedPhoto.url,\n                            alt: \"Photo preview\",\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Scale: \",\n                                    (placedPhoto.scale * 100).toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale - 0.1),\n                                        disabled: placedPhoto.scale <= 0.1,\n                                        className: \"touch-manipulation\",\n                                        children: \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0.1\",\n                                        max: \"3.0\",\n                                        step: \"0.1\",\n                                        value: placedPhoto.scale,\n                                        onChange: (e)=>handleScaleChange(parseFloat(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale + 0.1),\n                                        disabled: placedPhoto.scale >= 3.0,\n                                        className: \"touch-manipulation\",\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Rotation: \",\n                                    placedPhoto.rotation,\n                                    \"\\xb0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation - 15),\n                                        children: \"↺\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"360\",\n                                        step: \"15\",\n                                        value: placedPhoto.rotation,\n                                        onChange: (e)=>handleRotationChange(parseInt(e.target.value)),\n                                        className: \"flex-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation + 15),\n                                        children: \"↻\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: \"Position\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"X: \",\n                                                    placedPhoto.x.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.x,\n                                                onChange: (e)=>handlePositionChange(parseInt(e.target.value), placedPhoto.y),\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Y: \",\n                                                    placedPhoto.y.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.y,\n                                                onChange: (e)=>handlePositionChange(placedPhoto.x, parseInt(e.target.value)),\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onUpdate({\n                                        scale: 1.0,\n                                        rotation: 0,\n                                        x: 50,\n                                        y: 50\n                                    }),\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"destructive\",\n                                onClick: onRemove,\n                                children: \"Remove\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit width\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio > 1 ? 1.0 : aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Width\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit height\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio < 1 ? 1.0 : 1 / aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Height\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PhotoControls;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PhotoControls);\nvar _c;\n$RefreshReg$(_c, \"PhotoControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Bob3RvQ29udHJvbHMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFMEI7QUFFVztBQUNnQztBQVNyRSxNQUFNTSxnQkFBOEM7UUFBQyxFQUNuREMsV0FBVyxFQUNYQyxhQUFhLEVBQ2JDLFFBQVEsRUFDUkMsUUFBUSxFQUNUO0lBQ0MsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCSCxTQUFTO1lBQUVHLE9BQU9DLEtBQUtDLEdBQUcsQ0FBQyxLQUFLRCxLQUFLRSxHQUFHLENBQUMsS0FBS0g7UUFBUTtJQUN4RDtJQUVBLE1BQU1JLHVCQUF1QixDQUFDQztRQUM1QlIsU0FBUztZQUFFUSxVQUFVQSxXQUFXO1FBQUk7SUFDdEM7SUFFQSxNQUFNQyx1QkFBdUIsQ0FBQ0MsR0FBV0M7UUFDdkNYLFNBQVM7WUFDUFUsR0FBR04sS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxLQUFLSTtZQUM3QkMsR0FBR1AsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxLQUFLSztRQUMvQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNsQiwwQ0FBSUE7UUFBQ21CLFdBQVU7OzBCQUNkLDhEQUFDakIsZ0RBQVVBOzBCQUNULDRFQUFDQywrQ0FBU0E7b0JBQUNnQixXQUFVOzhCQUFVOzs7Ozs7Ozs7OzswQkFFakMsOERBQUNsQixpREFBV0E7Z0JBQUNrQixXQUFVOztrQ0FFckIsOERBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDRTs0QkFDQ0MsS0FBS2hCLGNBQWNpQixHQUFHOzRCQUN0QkMsS0FBSTs0QkFDSkwsV0FBVTs7Ozs7Ozs7Ozs7a0NBS2QsOERBQUNDOzswQ0FDQyw4REFBQ0s7Z0NBQU1OLFdBQVU7O29DQUErQztvQ0FDckRkLENBQUFBLFlBQVlLLEtBQUssR0FBRyxHQUFFLEVBQUdnQixPQUFPLENBQUM7b0NBQUc7Ozs7Ozs7MENBRS9DLDhEQUFDTjtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNwQiw4Q0FBTUE7d0NBQ0w0QixNQUFLO3dDQUNMQyxTQUFRO3dDQUNSQyxTQUFTLElBQU1wQixrQkFBa0JKLFlBQVlLLEtBQUssR0FBRzt3Q0FDckRvQixVQUFVekIsWUFBWUssS0FBSyxJQUFJO3dDQUMvQlMsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDWTt3Q0FDQ0MsTUFBSzt3Q0FDTG5CLEtBQUk7d0NBQ0pELEtBQUk7d0NBQ0pxQixNQUFLO3dDQUNMQyxPQUFPN0IsWUFBWUssS0FBSzt3Q0FDeEJ5QixVQUFVLENBQUNDLElBQU0zQixrQkFBa0I0QixXQUFXRCxFQUFFRSxNQUFNLENBQUNKLEtBQUs7d0NBQzVEZixXQUFVOzs7Ozs7a0RBRVosOERBQUNwQiw4Q0FBTUE7d0NBQ0w0QixNQUFLO3dDQUNMQyxTQUFRO3dDQUNSQyxTQUFTLElBQU1wQixrQkFBa0JKLFlBQVlLLEtBQUssR0FBRzt3Q0FDckRvQixVQUFVekIsWUFBWUssS0FBSyxJQUFJO3dDQUMvQlMsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9MLDhEQUFDQzs7MENBQ0MsOERBQUNLO2dDQUFNTixXQUFVOztvQ0FBK0M7b0NBQ25EZCxZQUFZVSxRQUFRO29DQUFDOzs7Ozs7OzBDQUVsQyw4REFBQ0s7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDcEIsOENBQU1BO3dDQUNMNEIsTUFBSzt3Q0FDTEMsU0FBUTt3Q0FDUkMsU0FBUyxJQUFNZixxQkFBcUJULFlBQVlVLFFBQVEsR0FBRztrREFDNUQ7Ozs7OztrREFHRCw4REFBQ2dCO3dDQUNDQyxNQUFLO3dDQUNMbkIsS0FBSTt3Q0FDSkQsS0FBSTt3Q0FDSnFCLE1BQUs7d0NBQ0xDLE9BQU83QixZQUFZVSxRQUFRO3dDQUMzQm9CLFVBQVUsQ0FBQ0MsSUFBTXRCLHFCQUFxQnlCLFNBQVNILEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzt3Q0FDN0RmLFdBQVU7Ozs7OztrREFFWiw4REFBQ3BCLDhDQUFNQTt3Q0FDTDRCLE1BQUs7d0NBQ0xDLFNBQVE7d0NBQ1JDLFNBQVMsSUFBTWYscUJBQXFCVCxZQUFZVSxRQUFRLEdBQUc7a0RBQzVEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT0wsOERBQUNLOzswQ0FDQyw4REFBQ0s7Z0NBQU1OLFdBQVU7MENBQStDOzs7Ozs7MENBR2hFLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNDOzswREFDQyw4REFBQ0s7Z0RBQU1OLFdBQVU7O29EQUF3QjtvREFBSWQsWUFBWVksQ0FBQyxDQUFDUyxPQUFPLENBQUM7b0RBQUc7Ozs7Ozs7MERBQ3RFLDhEQUFDSztnREFDQ0MsTUFBSztnREFDTG5CLEtBQUk7Z0RBQ0pELEtBQUk7Z0RBQ0pxQixNQUFLO2dEQUNMQyxPQUFPN0IsWUFBWVksQ0FBQztnREFDcEJrQixVQUFVLENBQUNDLElBQU1wQixxQkFBcUJ1QixTQUFTSCxFQUFFRSxNQUFNLENBQUNKLEtBQUssR0FBRzdCLFlBQVlhLENBQUM7Z0RBQzdFQyxXQUFVOzs7Ozs7Ozs7Ozs7a0RBR2QsOERBQUNDOzswREFDQyw4REFBQ0s7Z0RBQU1OLFdBQVU7O29EQUF3QjtvREFBSWQsWUFBWWEsQ0FBQyxDQUFDUSxPQUFPLENBQUM7b0RBQUc7Ozs7Ozs7MERBQ3RFLDhEQUFDSztnREFDQ0MsTUFBSztnREFDTG5CLEtBQUk7Z0RBQ0pELEtBQUk7Z0RBQ0pxQixNQUFLO2dEQUNMQyxPQUFPN0IsWUFBWWEsQ0FBQztnREFDcEJpQixVQUFVLENBQUNDLElBQU1wQixxQkFBcUJYLFlBQVlZLENBQUMsRUFBRXNCLFNBQVNILEVBQUVFLE1BQU0sQ0FBQ0osS0FBSztnREFDNUVmLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPbEIsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3BCLDhDQUFNQTtnQ0FDTDRCLE1BQUs7Z0NBQ0xDLFNBQVE7Z0NBQ1JDLFNBQVMsSUFBTXRCLFNBQVM7d0NBQUVHLE9BQU87d0NBQUtLLFVBQVU7d0NBQUdFLEdBQUc7d0NBQUlDLEdBQUc7b0NBQUc7MENBQ2pFOzs7Ozs7MENBR0QsOERBQUNuQiw4Q0FBTUE7Z0NBQ0w0QixNQUFLO2dDQUNMQyxTQUFRO2dDQUNSQyxTQUFTckI7MENBQ1Y7Ozs7Ozs7Ozs7OztrQ0FNSCw4REFBQ1k7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDcEIsOENBQU1BO2dDQUNMNEIsTUFBSztnQ0FDTEMsU0FBUTtnQ0FDUkMsU0FBUztvQ0FDUCwrQkFBK0I7b0NBQy9CLE1BQU1XLGNBQWNsQyxjQUFjbUMsS0FBSyxHQUFHbkMsY0FBY29DLE1BQU07b0NBQzlEbkMsU0FBUzt3Q0FBRUcsT0FBTzhCLGNBQWMsSUFBSSxNQUFNQTt3Q0FBYXZCLEdBQUc7d0NBQUlDLEdBQUc7b0NBQUc7Z0NBQ3RFOzBDQUNEOzs7Ozs7MENBR0QsOERBQUNuQiw4Q0FBTUE7Z0NBQ0w0QixNQUFLO2dDQUNMQyxTQUFRO2dDQUNSQyxTQUFTO29DQUNQLGdDQUFnQztvQ0FDaEMsTUFBTVcsY0FBY2xDLGNBQWNtQyxLQUFLLEdBQUduQyxjQUFjb0MsTUFBTTtvQ0FDOURuQyxTQUFTO3dDQUFFRyxPQUFPOEIsY0FBYyxJQUFJLE1BQU0sSUFBRUE7d0NBQWF2QixHQUFHO3dDQUFJQyxHQUFHO29DQUFHO2dDQUN4RTswQ0FDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT1g7S0F2TE1kO0FBeUxOLCtEQUFlQSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL1Bob3RvQ29udHJvbHMudHN4Pzg2M2QiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUGxhY2VkUGhvdG8sIFVwbG9hZGVkUGhvdG8gfSBmcm9tICdAL3R5cGVzL3RlbXBsYXRlJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJy4vdWkvYnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICcuL3VpL2NhcmQnO1xuXG5pbnRlcmZhY2UgUGhvdG9Db250cm9sc1Byb3BzIHtcbiAgcGxhY2VkUGhvdG86IFBsYWNlZFBob3RvO1xuICB1cGxvYWRlZFBob3RvOiBVcGxvYWRlZFBob3RvO1xuICBvblVwZGF0ZTogKHVwZGF0ZXM6IFBhcnRpYWw8UGxhY2VkUGhvdG8+KSA9PiB2b2lkO1xuICBvblJlbW92ZTogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgUGhvdG9Db250cm9sczogUmVhY3QuRkM8UGhvdG9Db250cm9sc1Byb3BzPiA9ICh7XG4gIHBsYWNlZFBob3RvLFxuICB1cGxvYWRlZFBob3RvLFxuICBvblVwZGF0ZSxcbiAgb25SZW1vdmVcbn0pID0+IHtcbiAgY29uc3QgaGFuZGxlU2NhbGVDaGFuZ2UgPSAoc2NhbGU6IG51bWJlcikgPT4ge1xuICAgIG9uVXBkYXRlKHsgc2NhbGU6IE1hdGgubWF4KDAuMSwgTWF0aC5taW4oMy4wLCBzY2FsZSkpIH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJvdGF0aW9uQ2hhbmdlID0gKHJvdGF0aW9uOiBudW1iZXIpID0+IHtcbiAgICBvblVwZGF0ZSh7IHJvdGF0aW9uOiByb3RhdGlvbiAlIDM2MCB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVQb3NpdGlvbkNoYW5nZSA9ICh4OiBudW1iZXIsIHk6IG51bWJlcikgPT4ge1xuICAgIG9uVXBkYXRlKHsgXG4gICAgICB4OiBNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIHgpKSxcbiAgICAgIHk6IE1hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgeSkpXG4gICAgfSk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZCBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc21cIj5QaG90byBDb250cm9sczwvQ2FyZFRpdGxlPlxuICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICB7LyogUGhvdG8gUHJldmlldyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3Qtc3F1YXJlIHctMjAgbXgtYXV0byBiZy1ncmF5LTEwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgIHNyYz17dXBsb2FkZWRQaG90by51cmx9XG4gICAgICAgICAgICBhbHQ9XCJQaG90byBwcmV2aWV3XCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogU2NhbGUgQ29udHJvbCAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJsb2NrIG1iLTFcIj5cbiAgICAgICAgICAgIFNjYWxlOiB7KHBsYWNlZFBob3RvLnNjYWxlICogMTAwKS50b0ZpeGVkKDApfSVcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTY2FsZUNoYW5nZShwbGFjZWRQaG90by5zY2FsZSAtIDAuMSl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtwbGFjZWRQaG90by5zY2FsZSA8PSAwLjF9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRvdWNoLW1hbmlwdWxhdGlvblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIC1cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgIG1pbj1cIjAuMVwiXG4gICAgICAgICAgICAgIG1heD1cIjMuMFwiXG4gICAgICAgICAgICAgIHN0ZXA9XCIwLjFcIlxuICAgICAgICAgICAgICB2YWx1ZT17cGxhY2VkUGhvdG8uc2NhbGV9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2NhbGVDaGFuZ2UocGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgdG91Y2gtbWFuaXB1bGF0aW9uXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU2NhbGVDaGFuZ2UocGxhY2VkUGhvdG8uc2NhbGUgKyAwLjEpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17cGxhY2VkUGhvdG8uc2NhbGUgPj0gMy4wfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0b3VjaC1tYW5pcHVsYXRpb25cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICArXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJvdGF0aW9uIENvbnRyb2wgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBibG9jayBtYi0xXCI+XG4gICAgICAgICAgICBSb3RhdGlvbjoge3BsYWNlZFBob3RvLnJvdGF0aW9ufcKwXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUm90YXRpb25DaGFuZ2UocGxhY2VkUGhvdG8ucm90YXRpb24gLSAxNSl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOKGulxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgIG1heD1cIjM2MFwiXG4gICAgICAgICAgICAgIHN0ZXA9XCIxNVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtwbGFjZWRQaG90by5yb3RhdGlvbn1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVSb3RhdGlvbkNoYW5nZShwYXJzZUludChlLnRhcmdldC52YWx1ZSkpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSb3RhdGlvbkNoYW5nZShwbGFjZWRQaG90by5yb3RhdGlvbiArIDE1KX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4oa7XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBvc2l0aW9uIENvbnRyb2xzICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmxvY2sgbWItMVwiPlxuICAgICAgICAgICAgUG9zaXRpb25cbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlg6IHtwbGFjZWRQaG90by54LnRvRml4ZWQoMCl9JTwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgbWF4PVwiMTAwXCJcbiAgICAgICAgICAgICAgICBzdGVwPVwiMVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3BsYWNlZFBob3RvLnh9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVQb3NpdGlvbkNoYW5nZShwYXJzZUludChlLnRhcmdldC52YWx1ZSksIHBsYWNlZFBob3RvLnkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5ZOiB7cGxhY2VkUGhvdG8ueS50b0ZpeGVkKDApfSU8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwicmFuZ2VcIlxuICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgIG1heD1cIjEwMFwiXG4gICAgICAgICAgICAgICAgc3RlcD1cIjFcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtwbGFjZWRQaG90by55fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlUG9zaXRpb25DaGFuZ2UocGxhY2VkUGhvdG8ueCwgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBRdWljayBBY3Rpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTJcIj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25VcGRhdGUoeyBzY2FsZTogMS4wLCByb3RhdGlvbjogMCwgeDogNTAsIHk6IDUwIH0pfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIFJlc2V0XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXtvblJlbW92ZX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBSZW1vdmVcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZpdCBPcHRpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTJcIj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAvLyBDYWxjdWxhdGUgc2NhbGUgdG8gZml0IHdpZHRoXG4gICAgICAgICAgICAgIGNvbnN0IGFzcGVjdFJhdGlvID0gdXBsb2FkZWRQaG90by53aWR0aCAvIHVwbG9hZGVkUGhvdG8uaGVpZ2h0O1xuICAgICAgICAgICAgICBvblVwZGF0ZSh7IHNjYWxlOiBhc3BlY3RSYXRpbyA+IDEgPyAxLjAgOiBhc3BlY3RSYXRpbywgeDogNTAsIHk6IDUwIH0pO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBGaXQgV2lkdGhcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAvLyBDYWxjdWxhdGUgc2NhbGUgdG8gZml0IGhlaWdodFxuICAgICAgICAgICAgICBjb25zdCBhc3BlY3RSYXRpbyA9IHVwbG9hZGVkUGhvdG8ud2lkdGggLyB1cGxvYWRlZFBob3RvLmhlaWdodDtcbiAgICAgICAgICAgICAgb25VcGRhdGUoeyBzY2FsZTogYXNwZWN0UmF0aW8gPCAxID8gMS4wIDogMS9hc3BlY3RSYXRpbywgeDogNTAsIHk6IDUwIH0pO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBGaXQgSGVpZ2h0XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBQaG90b0NvbnRyb2xzO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlBob3RvQ29udHJvbHMiLCJwbGFjZWRQaG90byIsInVwbG9hZGVkUGhvdG8iLCJvblVwZGF0ZSIsIm9uUmVtb3ZlIiwiaGFuZGxlU2NhbGVDaGFuZ2UiLCJzY2FsZSIsIk1hdGgiLCJtYXgiLCJtaW4iLCJoYW5kbGVSb3RhdGlvbkNoYW5nZSIsInJvdGF0aW9uIiwiaGFuZGxlUG9zaXRpb25DaGFuZ2UiLCJ4IiwieSIsImNsYXNzTmFtZSIsImRpdiIsImltZyIsInNyYyIsInVybCIsImFsdCIsImxhYmVsIiwidG9GaXhlZCIsInNpemUiLCJ2YXJpYW50Iiwib25DbGljayIsImRpc2FibGVkIiwiaW5wdXQiLCJ0eXBlIiwic3RlcCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwicGFyc2VGbG9hdCIsInRhcmdldCIsInBhcnNlSW50IiwiYXNwZWN0UmF0aW8iLCJ3aWR0aCIsImhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PhotoControls.tsx\n"));

/***/ })

});