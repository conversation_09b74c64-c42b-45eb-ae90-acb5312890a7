{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/LeftRightDialogHeader/LeftRightDialogHeader.tsx"], "names": ["LeftRightDialogHeader", "children", "className", "previous", "next", "close", "buttonLeft", "React", "useRef", "buttonRight", "buttonClose", "nav", "set<PERSON><PERSON>", "useState", "onNav", "useCallback", "el", "useEffect", "root", "getRootNode", "d", "self", "document", "handler", "e", "key", "stopPropagation", "current", "focus", "ShadowRoot", "a", "activeElement", "HTMLElement", "blur", "addEventListener", "removeEventListener", "div", "data-nextjs-dialog-left-right", "ref", "button", "type", "disabled", "undefined", "aria-disabled", "onClick", "svg", "viewBox", "fill", "xmlns", "title", "path", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "data-nextjs-errors-dialog-left-right-close-button", "aria-label", "span", "aria-hidden", "CloseIcon"], "mappings": ";;;;+BAwKSA;;;eAAAA;;;;iEAxKc;2BACG;AAU1B,MAAMA,wBACJ,SAASA,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACN,GAN8B;IAO7B,MAAMC,aAAaC,OAAMC,MAAM,CAA2B;IAC1D,MAAMC,cAAcF,OAAMC,MAAM,CAA2B;IAC3D,MAAME,cAAcH,OAAMC,MAAM,CAA2B;IAE3D,MAAM,CAACG,KAAKC,OAAO,GAAGL,OAAMM,QAAQ,CAAqB;IACzD,MAAMC,QAAQP,OAAMQ,WAAW,CAAC,CAACC;QAC/BJ,OAAOI;IACT,GAAG,EAAE;IAELT,OAAMU,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,MAAMC,IAAIC,KAAKC,QAAQ;QAEvB,SAASC,QAAQC,CAAgB;YAC/B,IAAIA,EAAEC,GAAG,KAAK,aAAa;gBACzBD,EAAEE,eAAe;gBACjB,IAAIpB,WAAWqB,OAAO,EAAE;oBACtBrB,WAAWqB,OAAO,CAACC,KAAK;gBAC1B;gBACAzB,YAAYA;YACd,OAAO,IAAIqB,EAAEC,GAAG,KAAK,cAAc;gBACjCD,EAAEE,eAAe;gBACjB,IAAIjB,YAAYkB,OAAO,EAAE;oBACvBlB,YAAYkB,OAAO,CAACC,KAAK;gBAC3B;gBACAxB,QAAQA;YACV,OAAO,IAAIoB,EAAEC,GAAG,KAAK,UAAU;gBAC7BD,EAAEE,eAAe;gBACjB,IAAIR,gBAAgBW,YAAY;oBAC9B,MAAMC,IAAIZ,KAAKa,aAAa;oBAC5B,IAAID,KAAKA,MAAMpB,YAAYiB,OAAO,IAAIG,aAAaE,aAAa;wBAC9DF,EAAEG,IAAI;wBACN;oBACF;gBACF;gBAEA,IAAI5B,OAAO;oBACTA;gBACF;YACF;QACF;QAEAa,KAAKgB,gBAAgB,CAAC,WAAWX;QACjC,IAAIL,SAASE,GAAG;YACdA,EAAEc,gBAAgB,CAAC,WAAWX;QAChC;QACA,OAAO;YACLL,KAAKiB,mBAAmB,CAAC,WAAWZ;YACpC,IAAIL,SAASE,GAAG;gBACdA,EAAEe,mBAAmB,CAAC,WAAWZ;YACnC;QACF;IACF,GAAG;QAAClB;QAAOM;QAAKP;QAAMD;KAAS;IAE/B,2EAA2E;IAC3E,2CAA2C;IAC3CI,OAAMU,SAAS,CAAC;QACd,IAAIN,OAAO,MAAM;YACf;QACF;QAEA,MAAMO,OAAOP,IAAIQ,WAAW;QAC5B,8CAA8C;QAC9C,IAAID,gBAAgBW,YAAY;YAC9B,MAAMC,IAAIZ,KAAKa,aAAa;YAE5B,IAAI5B,YAAY,MAAM;gBACpB,IAAIG,WAAWqB,OAAO,IAAIG,MAAMxB,WAAWqB,OAAO,EAAE;oBAClDrB,WAAWqB,OAAO,CAACM,IAAI;gBACzB;YACF,OAAO,IAAI7B,QAAQ,MAAM;gBACvB,IAAIK,YAAYkB,OAAO,IAAIG,MAAMrB,YAAYkB,OAAO,EAAE;oBACpDlB,YAAYkB,OAAO,CAACM,IAAI;gBAC1B;YACF;QACF;IACF,GAAG;QAACtB;QAAKP;QAAMD;KAAS;IAExB,qBACE,qBAACiC;QAAIC,iCAAAA;QAA8BnC,WAAWA;qBAC5C,qBAACS;QAAI2B,KAAKxB;qBACR,qBAACyB;QACCD,KAAKhC;QACLkC,MAAK;QACLC,UAAUtC,YAAY,OAAO,OAAOuC;QACpCC,iBAAexC,YAAY,OAAO,OAAOuC;QACzCE,SAASzC,mBAAAA,WAAYuC;qBAErB,qBAACG;QACCC,SAAQ;QACRC,MAAK;QACLC,OAAM;qBAEN,qBAACC,eAAM,2BACP,qBAACC;QACC9B,GAAE;QACF+B,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;wBAIrB,qBAACf;QACCD,KAAK7B;QACL+B,MAAK;QACLC,UAAUrC,QAAQ,OAAO,OAAOsC;QAChCC,iBAAevC,QAAQ,OAAO,OAAOsC;QACrCE,SAASxC,eAAAA,OAAQsC;qBAEjB,qBAACG;QACCC,SAAQ;QACRC,MAAK;QACLC,OAAM;qBAEN,qBAACC,eAAM,uBACP,qBAACC;QACC9B,GAAE;QACF+B,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;UAGZ,QAERrD,WAEFI,sBACC,qBAACkC;QACCgB,qDAAAA;QACAjB,KAAK5B;QACL8B,MAAK;QACLI,SAASvC;QACTmD,cAAW;qBAEX,qBAACC;QAAKC,eAAY;qBAChB,qBAACC,oBAAS,YAGZ;AAGV"}