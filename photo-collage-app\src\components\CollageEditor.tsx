'use client';

import React, { useState, useRef } from 'react';
import { CollageTemplate, UploadedPhoto, PlacedPhoto, PhotoSlot } from '@/types/template';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import PhotoControls from './PhotoControls';

interface CollageEditorProps {
  template: CollageTemplate;
  uploadedPhotos: UploadedPhoto[];
  onPlacedPhotosChange: (placedPhotos: PlacedPhoto[]) => void;
  onPreview: () => void;
}

const CollageEditor: React.FC<CollageEditorProps> = ({
  template,
  uploadedPhotos,
  onPlacedPhotosChange,
  onPreview
}) => {
  const [placedPhotos, setPlacedPhotos] = useState<PlacedPhoto[]>([]);
  const [draggedPhoto, setDraggedPhoto] = useState<UploadedPhoto | null>(null);
  const [hoveredSlot, setHoveredSlot] = useState<string | null>(null);
  const [selectedPhoto, setSelectedPhoto] = useState<PlacedPhoto | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleDragStart = (photo: UploadedPhoto) => {
    setDraggedPhoto(photo);
  };

  const handleDragEnd = () => {
    setDraggedPhoto(null);
    setHoveredSlot(null);
  };

  const handleSlotDragOver = (e: React.DragEvent, slotId: string) => {
    e.preventDefault();
    setHoveredSlot(slotId);
  };

  const handleSlotDragLeave = () => {
    setHoveredSlot(null);
  };

  const handleSlotDrop = (e: React.DragEvent, slotId: string) => {
    e.preventDefault();
    setHoveredSlot(null);

    if (!draggedPhoto) return;

    // Remove photo from any existing slot
    const updatedPlacedPhotos = placedPhotos.filter(p => p.photoId !== draggedPhoto.id);
    
    // Add photo to new slot
    const newPlacedPhoto: PlacedPhoto = {
      photoId: draggedPhoto.id,
      slotId,
      x: 50, // Center position
      y: 50, // Center position
      scale: 1.0,
      rotation: 0
    };

    const finalPlacedPhotos = [...updatedPlacedPhotos, newPlacedPhoto];
    setPlacedPhotos(finalPlacedPhotos);
    onPlacedPhotosChange(finalPlacedPhotos);
  };

  const removePhotoFromSlot = (photoId: string) => {
    const updatedPlacedPhotos = placedPhotos.filter(p => p.photoId !== photoId);
    setPlacedPhotos(updatedPlacedPhotos);
    onPlacedPhotosChange(updatedPlacedPhotos);
  };

  const getPhotoInSlot = (slotId: string): PlacedPhoto | undefined => {
    return placedPhotos.find(p => p.slotId === slotId);
  };

  const getUploadedPhoto = (photoId: string): UploadedPhoto | undefined => {
    return uploadedPhotos.find(p => p.id === photoId);
  };

  const updatePlacedPhoto = (photoId: string, updates: Partial<PlacedPhoto>) => {
    const updatedPlacedPhotos = placedPhotos.map(p =>
      p.photoId === photoId ? { ...p, ...updates } : p
    );
    setPlacedPhotos(updatedPlacedPhotos);
    onPlacedPhotosChange(updatedPlacedPhotos);

    // Update selected photo if it's the one being updated
    if (selectedPhoto && selectedPhoto.photoId === photoId) {
      setSelectedPhoto({ ...selectedPhoto, ...updates });
    }
  };

  const selectPhoto = (placedPhoto: PlacedPhoto) => {
    setSelectedPhoto(placedPhoto);
  };

  const renderSlot = (slot: PhotoSlot) => {
    const placedPhoto = getPhotoInSlot(slot.id);
    const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;
    const isHovered = hoveredSlot === slot.id;
    const isSelected = selectedPhoto && selectedPhoto.slotId === slot.id;

    const slotStyle: React.CSSProperties = {
      position: 'absolute',
      left: `${slot.x}%`,
      top: `${slot.y}%`,
      width: `${slot.width}%`,
      height: `${slot.height}%`,
      border: `2px ${isSelected ? 'solid' : isHovered ? 'solid' : 'dashed'} ${
        isSelected ? '#10b981' : isHovered ? '#3b82f6' : '#9ca3af'
      }`,
      backgroundColor: isHovered ? '#dbeafe' : uploadedPhoto ? 'transparent' : '#f3f4f6',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      overflow: 'hidden',
    };

    if (slot.shape === 'circle') {
      slotStyle.borderRadius = '50%';
    } else if (slot.shape === 'rectangle') {
      slotStyle.borderRadius = '4px';
    }

    if (slot.rotation) {
      slotStyle.transform = `rotate(${slot.rotation}deg)`;
    }

    return (
      <div
        key={slot.id}
        style={slotStyle}
        onDragOver={(e) => handleSlotDragOver(e, slot.id)}
        onDragLeave={handleSlotDragLeave}
        onDrop={(e) => handleSlotDrop(e, slot.id)}
        onClick={() => placedPhoto && selectPhoto(placedPhoto)}
      >
        {uploadedPhoto ? (
          <div className="relative w-full h-full group">
            <img
              src={uploadedPhoto.url}
              alt="Placed photo"
              className="w-full h-full object-cover"
              style={{
                transform: `scale(${placedPhoto?.scale || 1}) rotate(${placedPhoto?.rotation || 0}deg)`,
                objectPosition: `${placedPhoto?.x || 50}% ${placedPhoto?.y || 50}%`
              }}
            />
            <button
              onClick={(e) => {
                e.stopPropagation();
                removePhotoFromSlot(uploadedPhoto.id);
              }}
              className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
            >
              ×
            </button>
            {isSelected && (
              <div className="absolute inset-0 border-2 border-green-500 pointer-events-none" />
            )}
          </div>
        ) : (
          <div className="text-center text-gray-500">
            <div className="text-2xl mb-1">📷</div>
            <div className="text-xs">Drop photo here</div>
          </div>
        )}
      </div>
    );
  };

  const aspectRatio = template.canvasWidth / template.canvasHeight;
  const canvasWidth = 600;
  const canvasHeight = canvasWidth / aspectRatio;

  const unplacedPhotos = uploadedPhotos.filter(photo => 
    !placedPhotos.some(placed => placed.photoId === photo.id)
  );

  const filledSlots = placedPhotos.length;
  const totalSlots = template.slots.length;

  return (
    <div className="grid lg:grid-cols-4 gap-6">
      {/* Canvas Area */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Collage Canvas</CardTitle>
            <div className="text-sm text-gray-600">
              <span className="hidden md:inline">Drag photos from the sidebar into the slots below.</span>
              <span className="md:hidden">Tap photos below to add them to slots.</span>
              {" "}Click on placed photos to edit them.
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center overflow-x-auto">
              <div
                ref={canvasRef}
                className="relative border rounded-lg shadow-sm min-w-0 max-w-full"
                style={{
                  width: `${Math.min(canvasWidth, 600)}px`,
                  height: `${Math.min(canvasHeight, 600)}px`,
                  backgroundColor: template.backgroundColor || '#ffffff',
                }}
                onClick={() => setSelectedPhoto(null)}
              >
                {template.slots.map(slot => renderSlot(slot))}
              </div>
            </div>

            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {filledSlots} of {totalSlots} slots filled
                {selectedPhoto && (
                  <span className="ml-2 text-green-600">• Photo selected</span>
                )}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setPlacedPhotos([]);
                    setSelectedPhoto(null);
                    onPlacedPhotosChange([]);
                  }}
                  disabled={placedPhotos.length === 0}
                >
                  Clear All
                </Button>
                <Button
                  size="sm"
                  onClick={onPreview}
                  disabled={placedPhotos.length === 0}
                >
                  Preview Collage
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Photo Library */}
      <div className="lg:col-span-1">
        <Card>
          <CardHeader>
            <CardTitle>Photo Library</CardTitle>
            <div className="text-sm text-gray-600">
              {unplacedPhotos.length} photos available
            </div>
          </CardHeader>
          <CardContent>
            {unplacedPhotos.length > 0 ? (
              <div className="grid grid-cols-2 lg:grid-cols-1 gap-3">
                {unplacedPhotos.map((photo) => (
                  <div
                    key={photo.id}
                    className="relative group cursor-move touch-manipulation"
                    draggable
                    onDragStart={() => handleDragStart(photo)}
                    onDragEnd={handleDragEnd}
                  >
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 active:border-blue-500 transition-colors">
                      <img
                        src={photo.url}
                        alt="Available photo"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 group-active:bg-opacity-30 transition-all rounded-lg flex items-center justify-center">
                      <div className="text-white opacity-0 group-hover:opacity-100 group-active:opacity-100 text-xs font-medium text-center px-2">
                        <span className="hidden md:inline">Drag to slot</span>
                        <span className="md:hidden">Tap to select</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-2">✨</div>
                <div className="text-sm">
                  All photos have been placed!
                </div>
              </div>
            )}

            {uploadedPhotos.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-2">📷</div>
                <div className="text-sm">
                  Upload photos to get started
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Photo Controls */}
      <div className="lg:col-span-1">
        {selectedPhoto ? (
          <PhotoControls
            placedPhoto={selectedPhoto}
            uploadedPhoto={getUploadedPhoto(selectedPhoto.photoId)!}
            onUpdate={(updates) => updatePlacedPhoto(selectedPhoto.photoId, updates)}
            onRemove={() => {
              removePhotoFromSlot(selectedPhoto.photoId);
              setSelectedPhoto(null);
            }}
          />
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Photo Controls</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-2">🎛️</div>
                <div className="text-sm">
                  Click on a placed photo to edit its position, scale, and rotation
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default CollageEditor;
