{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Overlay/Overlay.tsx"], "names": ["allyTrap", "React", "lock", "unlock", "Overlay", "className", "children", "fixed", "useEffect", "overlay", "set<PERSON><PERSON>lay", "useState", "onOverlay", "useCallback", "el", "handle2", "context", "disengage", "div", "data-nextjs-dialog-overlay", "ref", "data-nextjs-dialog-backdrop", "data-nextjs-dialog-backdrop-fixed", "undefined"], "mappings": "AAAA,aAAa;AACb,OAAOA,cAAc,wBAAuB;AAC5C,YAAYC,WAAW,QAAO;AAC9B,SAASC,IAAI,EAAEC,MAAM,QAAQ,gBAAe;AAQ5C,MAAMC,UAAkC,SAASA,QAAQ,KAIxD;IAJwD,IAAA,EACvDC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACN,GAJwD;IAKvDN,MAAMO,SAAS,CAAC;QACdN;QACA,OAAO;YACLC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,CAACM,SAASC,WAAW,GAAGT,MAAMU,QAAQ,CAAwB;IACpE,MAAMC,YAAYX,MAAMY,WAAW,CAAC,CAACC;QACnCJ,WAAWI;IACb,GAAG,EAAE;IAELb,MAAMO,SAAS,CAAC;QACd,IAAIC,WAAW,MAAM;YACnB;QACF;QAEA,MAAMM,UAAUf,SAAS;YAAEgB,SAASP;QAAQ;QAC5C,OAAO;YACLM,QAAQE,SAAS;QACnB;IACF,GAAG;QAACR;KAAQ;IAEZ,qBACE,oBAACS;QAAIC,8BAAAA;QAA2Bd,WAAWA;QAAWe,KAAKR;qBACzD,oBAACM;QACCG,+BAAAA;QACAC,qCAAmCf,QAAQ,OAAOgB;QAEnDjB;AAGP;AAEA,SAASF,OAAO,GAAE"}