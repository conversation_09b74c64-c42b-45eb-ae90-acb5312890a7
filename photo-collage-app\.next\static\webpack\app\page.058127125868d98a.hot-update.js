/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/UnifiedEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _TemplateSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateSidebar */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PhotoUploadSidebar */ \"(app-pages-browser)/./src/components/PhotoUploadSidebar.tsx\");\n/* harmony import */ var _LiveCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LiveCanvas */ \"(app-pages-browser)/./src/components/LiveCanvas.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _ui_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst UnifiedEditor = (param)=>{\n    let { initialTemplateId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Get template from URL or use default\n    const getInitialTemplate = ()=>{\n        const templateParam = searchParams.get(\"template\");\n        if (templateParam) {\n            const template = _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates.find((t)=>t.id === templateParam);\n            if (template) return template;\n        }\n        return initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates[0];\n    };\n    // Core state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTemplate());\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // UI state\n    const [isTemplateSidebarOpen, setIsTemplateSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPhotoSidebarOpen, setIsPhotoSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedPhotoId, setSelectedPhotoId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Toast notifications\n    const { toasts, removeToast, success, info, error } = (0,_ui_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Auto-place photos when they are uploaded with smart positioning\n    const autoPlacePhotos = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                // Calculate optimal scale based on photo and slot aspect ratios\n                const photoAspectRatio = photo.width / photo.height;\n                const slotAspectRatio = slot.width / slot.height;\n                // Start with a scale that fits the photo nicely in the slot\n                let optimalScale = 1.0;\n                if (photoAspectRatio > slotAspectRatio) {\n                    // Photo is wider than slot, scale to fit height\n                    optimalScale = Math.min(1.2, 1.0 / (photoAspectRatio / slotAspectRatio));\n                } else {\n                    // Photo is taller than slot, scale to fit width\n                    optimalScale = Math.min(1.2, slotAspectRatio / photoAspectRatio);\n                }\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: Math.max(0.8, Math.min(1.5, optimalScale)),\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n            // Show a brief notification about auto-placement\n            success(\"Auto-placed \".concat(newPlacedPhotos.length, \" photo\").concat(newPlacedPhotos.length > 1 ? \"s\" : \"\"), \"Photos were automatically placed in available template slots\");\n        }\n    }, [\n        selectedTemplate,\n        placedPhotos\n    ]);\n    // Handle photo uploads with auto-placement\n    const handlePhotosUploaded = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        setUploadedPhotos((prev)=>{\n            const updated = [\n                ...prev,\n                ...newPhotos\n            ];\n            // Auto-place new photos\n            autoPlacePhotos(newPhotos);\n            return updated;\n        });\n    }, [\n        autoPlacePhotos\n    ]);\n    // Handle template change\n    const handleTemplateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((template)=>{\n        const previousTemplate = selectedTemplate;\n        setSelectedTemplate(template);\n        // Clear placed photos when template changes\n        setPlacedPhotos([]);\n        setSelectedPhotoId(null);\n        // Show template change notification\n        info(\"Switched to \".concat(template.name), \"Template changed from \".concat((previousTemplate === null || previousTemplate === void 0 ? void 0 : previousTemplate.name) || \"none\", \" to \").concat(template.name));\n        // Auto-place existing photos in new template\n        if (uploadedPhotos.length > 0) {\n            setTimeout(()=>autoPlacePhotos(uploadedPhotos), 100); // Small delay for better UX\n        }\n    }, [\n        uploadedPhotos,\n        autoPlacePhotos,\n        selectedTemplate,\n        info\n    ]);\n    // Handle photo placement changes\n    const handlePlacedPhotosChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    }, []);\n    // Handle photo removal\n    const handlePhotoRemove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((photoId)=>{\n        setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photoId));\n        setPlacedPhotos((prev)=>prev.filter((p)=>p.photoId !== photoId));\n        if (selectedPhotoId === photoId) {\n            setSelectedPhotoId(null);\n        }\n    }, [\n        selectedPhotoId\n    ]);\n    // Calculate completion status\n    const filledSlots = placedPhotos.length;\n    const totalSlots = (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0;\n    const completionPercentage = totalSlots > 0 ? Math.round(filledSlots / totalSlots * 100) : 0;\n    const isComplete = filledSlots === totalSlots && totalSlots > 0;\n    // Handle download\n    const handleDownload = async ()=>{\n        if (!selectedTemplate || placedPhotos.length === 0) return;\n        setIsDownloading(true);\n        try {\n            // This will be implemented with the LiveCanvas component\n            console.log(\"Download initiated\");\n        } catch (error) {\n            console.error(\"Download failed:\", error);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Handle share\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: \"Check out my photo collage!\",\n                    text: \"I created this amazing collage using \".concat(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name, \" template\"),\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Share cancelled or failed\");\n            }\n        } else {\n            // Fallback: copy URL to clipboard\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_toast__WEBPACK_IMPORTED_MODULE_9__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center gap-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: selectedTemplate.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                completionPercentage,\n                                                \"% complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleShare,\n                                    className: \"hidden md:flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Share\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: !isComplete || isDownloading,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isDownloading ? \"Downloading...\" : \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-r border-gray-200 transition-all duration-300 \".concat(isTemplateSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsTemplateSidebarOpen(!isTemplateSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isTemplateSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 42\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 80\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, undefined),\n                                isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    selectedTemplate: selectedTemplate,\n                                    onTemplateSelect: handleTemplateChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-6\",\n                            children: selectedTemplate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LiveCanvas__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                selectedPhotoId: selectedPhotoId,\n                                onPlacedPhotosChange: handlePlacedPhotosChange,\n                                onPhotoSelect: setSelectedPhotoId,\n                                onDownload: handleDownload\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"p-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Select a Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Choose a template from the sidebar to get started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-l border-gray-200 transition-all duration-300 \".concat(isPhotoSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsPhotoSidebarOpen(!isPhotoSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isPhotoSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 39\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 78\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Photos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined),\n                                isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    uploadedPhotos: uploadedPhotos,\n                                    placedPhotos: placedPhotos,\n                                    selectedPhotoId: selectedPhotoId,\n                                    selectedTemplate: selectedTemplate,\n                                    onPhotosUploaded: handlePhotosUploaded,\n                                    onPhotoRemove: handlePhotoRemove,\n                                    onPhotoSelect: setSelectedPhotoId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UnifiedEditor, \"RCCw2X2uo2FjqfoDBT3qR6XpxgM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _ui_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = UnifiedEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedEditor);\nvar _c;\n$RefreshReg$(_c, \"UnifiedEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UnifiedEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/navigation.js":
/*!*****************************************!*\
  !*** ./node_modules/next/navigation.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUEsK0pBQStEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanM/ODcwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/navigation.js\n"));

/***/ })

});