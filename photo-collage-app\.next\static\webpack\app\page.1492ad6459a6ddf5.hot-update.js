"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(app-pages-browser)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(app-pages-browser)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImageUpload */ \"(app-pages-browser)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _CollageEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CollageEditor */ \"(app-pages-browser)/./src/components/CollageEditor.tsx\");\n/* harmony import */ var _CollageRenderer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CollageRenderer */ \"(app-pages-browser)/./src/components/CollageRenderer.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CollageApp = ()=>{\n    _s();\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const handlePlacedPhotosChange = (newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    };\n    const handlePreview = ()=>{\n        setCurrentState(\"preview\");\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 132,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, undefined),\n                uploadedPhotos.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                                children: [\n                                                    \"Add photos to use in your collage. You need \",\n                                                    selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length,\n                                                    \" photos for this template.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            onPhotosUploaded: setUploadedPhotos,\n                                            maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                                children: \"Template Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                                children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    template: selectedTemplate,\n                                                    width: 280,\n                                                    showSlotNumbers: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Photo Slots:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                            children: [\n                                                \"Add More Photos (\",\n                                                uploadedPhotos.length,\n                                                \" uploaded)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardDescription, {\n                                            children: \"You can upload more photos or start arranging the ones you have.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onPhotosUploaded: setUploadedPhotos,\n                                        maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollageEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            template: selectedTemplate,\n                            uploadedPhotos: uploadedPhotos,\n                            onPlacedPhotosChange: handlePlacedPhotosChange,\n                            onPreview: handlePreview\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 147,\n            columnNumber: 5\n        }, undefined);\n    const renderPreviewScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Preview: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Your collage is ready! You can download it or go back to make changes.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setCurrentState(\"editing\"),\n                                    children: \"← Back to Editor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollageRenderer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                onDownload: (dataUrl)=>{\n                                    console.log(\"Collage downloaded:\", dataUrl.substring(0, 50) + \"...\");\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                            children: \"Collage Details\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Description:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.description\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Photos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            placedPhotos.length,\n                                                            \" of \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length,\n                                                            \" slots filled\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Category:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.category\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            placedPhotos.length === (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-green-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Collage complete!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-green-600 mt-1\",\n                                                        children: \"All photo slots have been filled. You can now download your collage.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            placedPhotos.length < ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-yellow-700\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"⚠\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Incomplete collage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-yellow-600 mt-1\",\n                                                        children: [\n                                                            \"Add \",\n                                                            ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) - placedPhotos.length,\n                                                            \" more photos to complete your collage.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 244,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen(),\n                currentState === \"preview\" && renderPreviewScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 347,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageApp, \"VRQBFVBNyGs2ie7xBu6SudtNgyg=\");\n_c = CollageApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageApp);\nvar _c;\n$RefreshReg$(_c, \"CollageApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageApp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CollageRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/CollageRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageRenderer = (param)=>{\n    let { template, uploadedPhotos, placedPhotos, onDownload } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isRendering, setIsRendering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewDataUrl, setPreviewDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const getPlacedPhoto = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const loadImage = (src)=>{\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            img.onload = ()=>resolve(img);\n            img.onerror = reject;\n            img.src = src;\n        });\n    };\n    const renderSlotToCanvas = async (ctx, slot, placedPhoto, uploadedPhoto)=>{\n        try {\n            const img = await loadImage(uploadedPhoto.url);\n            // Calculate slot dimensions in pixels\n            const slotX = slot.x / 100 * template.canvasWidth;\n            const slotY = slot.y / 100 * template.canvasHeight;\n            const slotWidth = slot.width / 100 * template.canvasWidth;\n            const slotHeight = slot.height / 100 * template.canvasHeight;\n            // Save canvas state\n            ctx.save();\n            // Create clipping path for the slot shape\n            ctx.beginPath();\n            if (slot.shape === \"circle\") {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                const radius = Math.min(slotWidth, slotHeight) / 2;\n                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\n            } else {\n                // Rectangle or custom shape\n                ctx.rect(slotX, slotY, slotWidth, slotHeight);\n            }\n            ctx.clip();\n            // Apply slot rotation if any\n            if (slot.rotation) {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(slot.rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Calculate image positioning and scaling\n            const scale = placedPhoto.scale || 1;\n            const rotation = placedPhoto.rotation || 0;\n            const posX = (placedPhoto.x || 50) / 100;\n            const posY = (placedPhoto.y || 50) / 100;\n            // Calculate scaled image dimensions\n            const scaledWidth = slotWidth * scale;\n            const scaledHeight = slotHeight * scale;\n            // Calculate position offset based on positioning\n            const offsetX = slotX + (slotWidth - scaledWidth) * posX;\n            const offsetY = slotY + (slotHeight - scaledHeight) * posY;\n            // Apply image rotation\n            if (rotation !== 0) {\n                const centerX = offsetX + scaledWidth / 2;\n                const centerY = offsetY + scaledHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Draw the image\n            ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);\n            // Restore canvas state\n            ctx.restore();\n        } catch (error) {\n            console.error(\"Error rendering slot:\", error);\n        }\n    };\n    const renderCollage = async ()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return null;\n        setIsRendering(true);\n        try {\n            const ctx = canvas.getContext(\"2d\");\n            if (!ctx) return null;\n            // Set canvas dimensions\n            canvas.width = template.canvasWidth;\n            canvas.height = template.canvasHeight;\n            // Clear canvas and set background\n            ctx.fillStyle = template.backgroundColor || \"#ffffff\";\n            ctx.fillRect(0, 0, template.canvasWidth, template.canvasHeight);\n            // Render each slot with its photo\n            for (const slot of template.slots){\n                const placedPhoto = getPlacedPhoto(slot.id);\n                if (placedPhoto) {\n                    const uploadedPhoto = getUploadedPhoto(placedPhoto.photoId);\n                    if (uploadedPhoto) {\n                        await renderSlotToCanvas(ctx, slot, placedPhoto, uploadedPhoto);\n                    }\n                }\n            }\n            // Get the data URL\n            const dataUrl = canvas.toDataURL(\"image/png\", 1.0);\n            setPreviewDataUrl(dataUrl);\n            return dataUrl;\n        } catch (error) {\n            console.error(\"Error rendering collage:\", error);\n            return null;\n        } finally{\n            setIsRendering(false);\n        }\n    };\n    const handleDownload = async ()=>{\n        const dataUrl = await renderCollage();\n        if (dataUrl) {\n            // Create download link\n            const link = document.createElement(\"a\");\n            link.download = \"collage-\".concat(template.name.toLowerCase().replace(/\\s+/g, \"-\"), \"-\").concat(Date.now(), \".png\");\n            link.href = dataUrl;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            if (onDownload) {\n                onDownload(dataUrl);\n            }\n        }\n    };\n    const handlePreview = async ()=>{\n        await renderCollage();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Auto-render preview when component mounts or data changes\n        if (placedPhotos.length > 0) {\n            handlePreview();\n        }\n    }, [\n        template,\n        placedPhotos,\n        uploadedPhotos\n    ]);\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    const isComplete = filledSlots === totalSlots;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        children: \"Collage Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: isComplete ? \"Your collage is ready!\" : \"\".concat(filledSlots, \" of \").concat(totalSlots, \" slots filled\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                        ref: canvasRef,\n                                        className: \"border rounded-lg shadow-sm max-w-full h-auto\",\n                                        style: {\n                                            maxHeight: \"400px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isRendering && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm\",\n                                            children: \"Rendering...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handlePreview,\n                                    disabled: isRendering || placedPhotos.length === 0,\n                                    children: isRendering ? \"Rendering...\" : \"Refresh Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: isRendering || !isComplete,\n                                    children: \"Download Collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-sm text-gray-600\",\n                            children: [\n                                !isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add photos to all slots to enable download\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, undefined),\n                                isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600\",\n                                    children: \"✓ Ready to download!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Canvas Size: \",\n                                        template.canvasWidth,\n                                        \" \\xd7 \",\n                                        template.canvasHeight,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Format: PNG • Quality: High\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageRenderer, \"NFqb6mHu6wi/Wz3sxktXDlMtN7g=\");\n_c = CollageRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageRenderer);\nvar _c;\n$RefreshReg$(_c, \"CollageRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageRenderer.tsx\n"));

/***/ })

});