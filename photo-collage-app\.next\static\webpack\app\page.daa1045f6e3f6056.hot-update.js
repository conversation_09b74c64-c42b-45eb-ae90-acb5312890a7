"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(app-pages-browser)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CollageApp = ()=>{\n    _s();\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 39,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 120,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: [\n                                                    \"Add photos to use in your collage. You need \",\n                                                    selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length,\n                                                    \" photos for this template.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageUpload, {\n                                            onPhotosUploaded: setUploadedPhotos,\n                                            maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: \"Template Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplatePreview, {\n                                                    template: selectedTemplate,\n                                                    width: 280,\n                                                    showSlotNumbers: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Photo Slots:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Uploaded Photos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 20\n                                                            }, undefined),\n                                                            \" \",\n                                                            uploadedPhotos.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            uploadedPhotos.length >= ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"✓\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Ready to create collage!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            uploadedPhotos.length > 0 && uploadedPhotos.length < ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-yellow-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"⚠\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: [\n                                                                \"Need \",\n                                                                ((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0) - uploadedPhotos.length,\n                                                                \" more photos\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 135,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageApp, \"VRQBFVBNyGs2ie7xBu6SudtNgyg=\");\n_c = CollageApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageApp);\nvar _c;\n$RefreshReg$(_c, \"CollageApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageApp.tsx\n"));

/***/ })

});