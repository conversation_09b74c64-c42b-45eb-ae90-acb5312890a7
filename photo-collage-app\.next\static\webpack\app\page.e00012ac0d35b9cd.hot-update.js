"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/UnifiedEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _TemplateSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplateSidebar */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PhotoUploadSidebar */ \"(app-pages-browser)/./src/components/PhotoUploadSidebar.tsx\");\n/* harmony import */ var _LiveCanvas__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./LiveCanvas */ \"(app-pages-browser)/./src/components/LiveCanvas.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _ui_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst UnifiedEditor = (param)=>{\n    let { initialTemplateId } = param;\n    _s();\n    // Core state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0]);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // UI state\n    const [isTemplateSidebarOpen, setIsTemplateSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPhotoSidebarOpen, setIsPhotoSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedPhotoId, setSelectedPhotoId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Toast notifications\n    const { toasts, removeToast, success, info, error } = (0,_ui_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    // Auto-place photos when they are uploaded with smart positioning\n    const autoPlacePhotos = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                // Calculate optimal scale based on photo and slot aspect ratios\n                const photoAspectRatio = photo.width / photo.height;\n                const slotAspectRatio = slot.width / slot.height;\n                // Start with a scale that fits the photo nicely in the slot\n                let optimalScale = 1.0;\n                if (photoAspectRatio > slotAspectRatio) {\n                    // Photo is wider than slot, scale to fit height\n                    optimalScale = Math.min(1.2, 1.0 / (photoAspectRatio / slotAspectRatio));\n                } else {\n                    // Photo is taller than slot, scale to fit width\n                    optimalScale = Math.min(1.2, slotAspectRatio / photoAspectRatio);\n                }\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: Math.max(0.8, Math.min(1.5, optimalScale)),\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n            // Show a brief notification about auto-placement\n            success(\"Auto-placed \".concat(newPlacedPhotos.length, \" photo\").concat(newPlacedPhotos.length > 1 ? \"s\" : \"\"), \"Photos were automatically placed in available template slots\");\n        }\n    }, [\n        selectedTemplate,\n        placedPhotos\n    ]);\n    // Handle photo uploads with auto-placement\n    const handlePhotosUploaded = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        setUploadedPhotos((prev)=>{\n            const updated = [\n                ...prev,\n                ...newPhotos\n            ];\n            // Auto-place new photos\n            autoPlacePhotos(newPhotos);\n            return updated;\n        });\n    }, [\n        autoPlacePhotos\n    ]);\n    // Handle template change\n    const handleTemplateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((template)=>{\n        const previousTemplate = selectedTemplate;\n        setSelectedTemplate(template);\n        // Clear placed photos when template changes\n        setPlacedPhotos([]);\n        setSelectedPhotoId(null);\n        // Show template change notification\n        info(\"Switched to \".concat(template.name), \"Template changed from \".concat((previousTemplate === null || previousTemplate === void 0 ? void 0 : previousTemplate.name) || \"none\", \" to \").concat(template.name));\n        // Auto-place existing photos in new template\n        if (uploadedPhotos.length > 0) {\n            setTimeout(()=>autoPlacePhotos(uploadedPhotos), 100); // Small delay for better UX\n        }\n    }, [\n        uploadedPhotos,\n        autoPlacePhotos,\n        selectedTemplate,\n        info\n    ]);\n    // Handle photo placement changes\n    const handlePlacedPhotosChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    }, []);\n    // Handle photo removal\n    const handlePhotoRemove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((photoId)=>{\n        setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photoId));\n        setPlacedPhotos((prev)=>prev.filter((p)=>p.photoId !== photoId));\n        if (selectedPhotoId === photoId) {\n            setSelectedPhotoId(null);\n        }\n    }, [\n        selectedPhotoId\n    ]);\n    // Calculate completion status\n    const filledSlots = placedPhotos.length;\n    const totalSlots = (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0;\n    const completionPercentage = totalSlots > 0 ? Math.round(filledSlots / totalSlots * 100) : 0;\n    const isComplete = filledSlots === totalSlots && totalSlots > 0;\n    // Handle download\n    const handleDownload = async ()=>{\n        if (!selectedTemplate || placedPhotos.length === 0) return;\n        setIsDownloading(true);\n        try {\n            // This will be implemented with the LiveCanvas component\n            console.log(\"Download initiated\");\n        } catch (error) {\n            console.error(\"Download failed:\", error);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Handle share\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: \"Check out my photo collage!\",\n                    text: \"I created this amazing collage using \".concat(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name, \" template\"),\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Share cancelled or failed\");\n            }\n        } else {\n            // Fallback: copy URL to clipboard\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_toast__WEBPACK_IMPORTED_MODULE_8__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center gap-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: selectedTemplate.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                completionPercentage,\n                                                \"% complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleShare,\n                                    className: \"hidden md:flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Share\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: !isComplete || isDownloading,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isDownloading ? \"Downloading...\" : \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-r border-gray-200 transition-all duration-300 \".concat(isTemplateSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsTemplateSidebarOpen(!isTemplateSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isTemplateSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 42\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 80\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    selectedTemplate: selectedTemplate,\n                                    onTemplateSelect: handleTemplateChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-6\",\n                            children: selectedTemplate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LiveCanvas__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                selectedPhotoId: selectedPhotoId,\n                                onPlacedPhotosChange: handlePlacedPhotosChange,\n                                onPhotoSelect: setSelectedPhotoId,\n                                onDownload: handleDownload\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"p-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Select a Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Choose a template from the sidebar to get started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-l border-gray-200 transition-all duration-300 \".concat(isPhotoSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsPhotoSidebarOpen(!isPhotoSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isPhotoSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 39\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 78\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Photos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, undefined),\n                                isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    uploadedPhotos: uploadedPhotos,\n                                    placedPhotos: placedPhotos,\n                                    selectedPhotoId: selectedPhotoId,\n                                    selectedTemplate: selectedTemplate,\n                                    onPhotosUploaded: handlePhotosUploaded,\n                                    onPhotoRemove: handlePhotoRemove,\n                                    onPhotoSelect: setSelectedPhotoId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UnifiedEditor, \"kunAZWdxoijVmVkHK/kETbDNdKE=\", false, function() {\n    return [\n        _ui_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = UnifiedEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedEditor);\nvar _c;\n$RefreshReg$(_c, \"UnifiedEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1VuaWZpZWRFZGl0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdFO0FBR25CO0FBQ0c7QUFDTTtBQUNoQjtBQUVEO0FBQ1M7QUFDNkI7QUFDckI7QUFNdEQsTUFBTWdCLGdCQUE4QztRQUFDLEVBQUVDLGlCQUFpQixFQUFFOztJQUN4RSxhQUFhO0lBQ2IsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHbEIsK0NBQVFBLENBQ3REZ0Isb0JBQW9CZCxzREFBU0EsQ0FBQ2lCLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRSxLQUFLTCxzQkFBc0JkLHNEQUFTLENBQUMsRUFBRSxHQUFHQSxzREFBUyxDQUFDLEVBQUU7SUFFcEcsTUFBTSxDQUFDb0IsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZCLCtDQUFRQSxDQUFrQixFQUFFO0lBQ3hFLE1BQU0sQ0FBQ3dCLGNBQWNDLGdCQUFnQixHQUFHekIsK0NBQVFBLENBQWdCLEVBQUU7SUFFbEUsV0FBVztJQUNYLE1BQU0sQ0FBQzBCLHVCQUF1QkMseUJBQXlCLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUM0QixvQkFBb0JDLHNCQUFzQixHQUFHN0IsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDOEIsaUJBQWlCQyxtQkFBbUIsR0FBRy9CLCtDQUFRQSxDQUFnQjtJQUN0RSxNQUFNLENBQUNnQyxlQUFlQyxpQkFBaUIsR0FBR2pDLCtDQUFRQSxDQUFDO0lBRW5ELHNCQUFzQjtJQUN0QixNQUFNLEVBQUVrQyxNQUFNLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHekIsbURBQVFBO0lBRTlELGtFQUFrRTtJQUNsRSxNQUFNMEIsa0JBQWtCdEMsa0RBQVdBLENBQUMsQ0FBQ3VDO1FBQ25DLElBQUksQ0FBQ3ZCLGtCQUFrQjtRQUV2QixNQUFNd0IsaUJBQWlCeEIsaUJBQWlCeUIsS0FBSyxDQUFDQyxNQUFNLENBQUNDLENBQUFBLE9BQ25ELENBQUNwQixhQUFhcUIsSUFBSSxDQUFDQyxDQUFBQSxTQUFVQSxPQUFPQyxNQUFNLEtBQUtILEtBQUt2QixFQUFFO1FBR3hELE1BQU0yQixrQkFBaUMsRUFBRTtRQUV6Q1IsVUFBVVMsT0FBTyxDQUFDLENBQUNDLE9BQU9DO1lBQ3hCLElBQUlBLFFBQVFWLGVBQWVXLE1BQU0sRUFBRTtnQkFDakMsTUFBTVIsT0FBT0gsY0FBYyxDQUFDVSxNQUFNO2dCQUVsQyxnRUFBZ0U7Z0JBQ2hFLE1BQU1FLG1CQUFtQkgsTUFBTUksS0FBSyxHQUFHSixNQUFNSyxNQUFNO2dCQUNuRCxNQUFNQyxrQkFBa0JaLEtBQUtVLEtBQUssR0FBR1YsS0FBS1csTUFBTTtnQkFFaEQsNERBQTREO2dCQUM1RCxJQUFJRSxlQUFlO2dCQUNuQixJQUFJSixtQkFBbUJHLGlCQUFpQjtvQkFDdEMsZ0RBQWdEO29CQUNoREMsZUFBZUMsS0FBS0MsR0FBRyxDQUFDLEtBQUssTUFBT04sQ0FBQUEsbUJBQW1CRyxlQUFjO2dCQUN2RSxPQUFPO29CQUNMLGdEQUFnRDtvQkFDaERDLGVBQWVDLEtBQUtDLEdBQUcsQ0FBQyxLQUFLSCxrQkFBa0JIO2dCQUNqRDtnQkFFQUwsZ0JBQWdCWSxJQUFJLENBQUM7b0JBQ25CQyxTQUFTWCxNQUFNN0IsRUFBRTtvQkFDakIwQixRQUFRSCxLQUFLdkIsRUFBRTtvQkFDZnlDLEdBQUc7b0JBQ0hDLEdBQUc7b0JBQ0hDLE9BQU9OLEtBQUtPLEdBQUcsQ0FBQyxLQUFLUCxLQUFLQyxHQUFHLENBQUMsS0FBS0Y7b0JBQ25DUyxVQUFVO2dCQUNaO1lBQ0Y7UUFDRjtRQUVBLElBQUlsQixnQkFBZ0JJLE1BQU0sR0FBRyxHQUFHO1lBQzlCM0IsZ0JBQWdCMEMsQ0FBQUEsT0FBUTt1QkFBSUE7dUJBQVNuQjtpQkFBZ0I7WUFFckQsaURBQWlEO1lBQ2pEWixRQUNFLGVBQThDWSxPQUEvQkEsZ0JBQWdCSSxNQUFNLEVBQUMsVUFBOEMsT0FBdENKLGdCQUFnQkksTUFBTSxHQUFHLElBQUksTUFBTSxLQUNoRjtRQUVMO0lBQ0YsR0FBRztRQUFDbkM7UUFBa0JPO0tBQWE7SUFFbkMsMkNBQTJDO0lBQzNDLE1BQU00Qyx1QkFBdUJuRSxrREFBV0EsQ0FBQyxDQUFDdUM7UUFDeENqQixrQkFBa0I0QyxDQUFBQTtZQUNoQixNQUFNRSxVQUFVO21CQUFJRjttQkFBUzNCO2FBQVU7WUFDdkMsd0JBQXdCO1lBQ3hCRCxnQkFBZ0JDO1lBQ2hCLE9BQU82QjtRQUNUO0lBQ0YsR0FBRztRQUFDOUI7S0FBZ0I7SUFFcEIseUJBQXlCO0lBQ3pCLE1BQU0rQix1QkFBdUJyRSxrREFBV0EsQ0FBQyxDQUFDc0U7UUFDeEMsTUFBTUMsbUJBQW1CdkQ7UUFDekJDLG9CQUFvQnFEO1FBQ3BCLDRDQUE0QztRQUM1QzlDLGdCQUFnQixFQUFFO1FBQ2xCTSxtQkFBbUI7UUFFbkIsb0NBQW9DO1FBQ3BDTSxLQUNFLGVBQTZCLE9BQWRrQyxTQUFTRSxJQUFJLEdBQzVCLHlCQUFnRUYsT0FBdkNDLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCQyxJQUFJLEtBQUksUUFBTyxRQUFvQixPQUFkRixTQUFTRSxJQUFJO1FBRy9FLDZDQUE2QztRQUM3QyxJQUFJbkQsZUFBZThCLE1BQU0sR0FBRyxHQUFHO1lBQzdCc0IsV0FBVyxJQUFNbkMsZ0JBQWdCakIsaUJBQWlCLE1BQU0sNEJBQTRCO1FBQ3RGO0lBQ0YsR0FBRztRQUFDQTtRQUFnQmlCO1FBQWlCdEI7UUFBa0JvQjtLQUFLO0lBRTVELGlDQUFpQztJQUNqQyxNQUFNc0MsMkJBQTJCMUUsa0RBQVdBLENBQUMsQ0FBQytDO1FBQzVDdkIsZ0JBQWdCdUI7SUFDbEIsR0FBRyxFQUFFO0lBRUwsdUJBQXVCO0lBQ3ZCLE1BQU00QixvQkFBb0IzRSxrREFBV0EsQ0FBQyxDQUFDNEQ7UUFDckN0QyxrQkFBa0I0QyxDQUFBQSxPQUFRQSxLQUFLeEIsTUFBTSxDQUFDa0MsQ0FBQUEsSUFBS0EsRUFBRXhELEVBQUUsS0FBS3dDO1FBQ3BEcEMsZ0JBQWdCMEMsQ0FBQUEsT0FBUUEsS0FBS3hCLE1BQU0sQ0FBQ2tDLENBQUFBLElBQUtBLEVBQUVoQixPQUFPLEtBQUtBO1FBQ3ZELElBQUkvQixvQkFBb0IrQixTQUFTO1lBQy9COUIsbUJBQW1CO1FBQ3JCO0lBQ0YsR0FBRztRQUFDRDtLQUFnQjtJQUVwQiw4QkFBOEI7SUFDOUIsTUFBTWdELGNBQWN0RCxhQUFhNEIsTUFBTTtJQUN2QyxNQUFNMkIsYUFBYTlELENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCeUIsS0FBSyxDQUFDVSxNQUFNLEtBQUk7SUFDckQsTUFBTTRCLHVCQUF1QkQsYUFBYSxJQUFJckIsS0FBS3VCLEtBQUssQ0FBQyxjQUFlRixhQUFjLE9BQU87SUFDN0YsTUFBTUcsYUFBYUosZ0JBQWdCQyxjQUFjQSxhQUFhO0lBRTlELGtCQUFrQjtJQUNsQixNQUFNSSxpQkFBaUI7UUFDckIsSUFBSSxDQUFDbEUsb0JBQW9CTyxhQUFhNEIsTUFBTSxLQUFLLEdBQUc7UUFFcERuQixpQkFBaUI7UUFDakIsSUFBSTtZQUNGLHlEQUF5RDtZQUN6RG1ELFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBTy9DLE9BQU87WUFDZDhDLFFBQVE5QyxLQUFLLENBQUMsb0JBQW9CQTtRQUNwQyxTQUFVO1lBQ1JMLGlCQUFpQjtRQUNuQjtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU1xRCxjQUFjO1FBQ2xCLElBQUlDLFVBQVVDLEtBQUssRUFBRTtZQUNuQixJQUFJO2dCQUNGLE1BQU1ELFVBQVVDLEtBQUssQ0FBQztvQkFDcEJDLE9BQU87b0JBQ1BDLE1BQU0sd0NBQStELE9BQXZCekUsNkJBQUFBLHVDQUFBQSxpQkFBa0J3RCxJQUFJLEVBQUM7b0JBQ3JFa0IsS0FBS0MsT0FBT0MsUUFBUSxDQUFDQyxJQUFJO2dCQUMzQjtZQUNGLEVBQUUsT0FBT3hELE9BQU87Z0JBQ2Q4QyxRQUFRQyxHQUFHLENBQUM7WUFDZDtRQUNGLE9BQU87WUFDTCxrQ0FBa0M7WUFDbENFLFVBQVVRLFNBQVMsQ0FBQ0MsU0FBUyxDQUFDSixPQUFPQyxRQUFRLENBQUNDLElBQUk7UUFDcEQ7SUFDRjtJQUVBLHFCQUNFLDhEQUFDRztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ3BGLHFEQUFjQTtnQkFBQ29CLFFBQVFBO2dCQUFRaUUsVUFBVWhFOzs7Ozs7MEJBRTFDLDhEQUFDaUU7Z0JBQU9GLFdBQVU7MEJBQ2hCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0c7b0NBQUdILFdBQVU7OENBQWtDOzs7Ozs7Z0NBQy9DakYsa0NBQ0MsOERBQUNnRjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNJO3NEQUFLOzs7Ozs7c0RBQ04sOERBQUNBO3NEQUFNckYsaUJBQWlCd0QsSUFBSTs7Ozs7O3NEQUM1Qiw4REFBQzZCO3NEQUFLOzs7Ozs7c0RBQ04sOERBQUNBOztnREFBTXRCO2dEQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLbEMsOERBQUNpQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM1Riw4Q0FBTUE7b0NBQ0xpRyxTQUFRO29DQUNSQyxNQUFLO29DQUNMQyxTQUFTbkI7b0NBQ1RZLFdBQVU7O3NEQUVWLDhEQUFDdEYsb0hBQU1BOzRDQUFDc0YsV0FBVTs7Ozs7O3dDQUFZOzs7Ozs7OzhDQUdoQyw4REFBQzVGLDhDQUFNQTtvQ0FDTG1HLFNBQVN0QjtvQ0FDVHVCLFVBQVUsQ0FBQ3hCLGNBQWNsRDtvQ0FDekJrRSxXQUFVOztzREFFViw4REFBQ3ZGLHFIQUFRQTs0Q0FBQ3VGLFdBQVU7Ozs7Ozt3Q0FDbkJsRSxnQkFBZ0IsbUJBQW1COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzVDLDhEQUFDMkU7Z0JBQUtULFdBQVU7O2tDQUVkLDhEQUFDVTt3QkFBTVYsV0FBVyxpRUFFakIsT0FEQ3hFLHdCQUF3QixTQUFTO2tDQUVqQyw0RUFBQ3VFOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3dDQUNaeEUsdUNBQ0MsOERBQUNtRjs0Q0FBR1gsV0FBVTtzREFBOEI7Ozs7OztzREFFOUMsOERBQUM1Riw4Q0FBTUE7NENBQ0xpRyxTQUFROzRDQUNSQyxNQUFLOzRDQUNMQyxTQUFTLElBQU05RSx5QkFBeUIsQ0FBQ0Q7NENBQ3pDd0UsV0FBVTtzREFFVHhFLHNDQUF3Qiw4REFBQ2pCLHFIQUFXQTtnREFBQ3lGLFdBQVU7Ozs7OzBFQUFlLDhEQUFDeEYscUhBQVlBO2dEQUFDd0YsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBSTFGeEUsdUNBQ0MsOERBQUN2Qix3REFBZUE7b0NBQ2RjLGtCQUFrQkE7b0NBQ2xCNkYsa0JBQWtCeEM7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU8xQiw4REFBQ3lDO3dCQUFRYixXQUFVO2tDQUNqQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ1pqRixpQ0FDQyw4REFBQ1osbURBQVVBO2dDQUNUa0UsVUFBVXREO2dDQUNWSyxnQkFBZ0JBO2dDQUNoQkUsY0FBY0E7Z0NBQ2RNLGlCQUFpQkE7Z0NBQ2pCa0Ysc0JBQXNCckM7Z0NBQ3RCc0MsZUFBZWxGO2dDQUNmbUYsWUFBWS9COzs7OzswREFHZCw4REFBQ2M7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUMzRiwwQ0FBSUE7b0NBQUMyRixXQUFVOzhDQUNkLDRFQUFDMUYsaURBQVdBOzswREFDViw4REFBQ3lGO2dEQUFJQyxXQUFVOzBEQUFnQjs7Ozs7OzBEQUMvQiw4REFBQ2lCO2dEQUFHakIsV0FBVTswREFBNkI7Ozs7OzswREFDM0MsOERBQUNyQjtnREFBRXFCLFdBQVU7MERBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FTekMsOERBQUNVO3dCQUFNVixXQUFXLGlFQUVqQixPQURDdEUscUJBQXFCLFNBQVM7a0NBRTlCLDRFQUFDcUU7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1Riw4Q0FBTUE7NENBQ0xpRyxTQUFROzRDQUNSQyxNQUFLOzRDQUNMQyxTQUFTLElBQU01RSxzQkFBc0IsQ0FBQ0Q7NENBQ3RDc0UsV0FBVTtzREFFVHRFLG1DQUFxQiw4REFBQ2xCLHFIQUFZQTtnREFBQ3dGLFdBQVU7Ozs7OzBFQUFlLDhEQUFDekYscUhBQVdBO2dEQUFDeUYsV0FBVTs7Ozs7Ozs7Ozs7d0NBRXJGdEUsb0NBQ0MsOERBQUNpRjs0Q0FBR1gsV0FBVTtzREFBOEI7Ozs7Ozs7Ozs7OztnQ0FJL0N0RSxvQ0FDQyw4REFBQ3hCLDJEQUFrQkE7b0NBQ2pCa0IsZ0JBQWdCQTtvQ0FDaEJFLGNBQWNBO29DQUNkTSxpQkFBaUJBO29DQUNqQmIsa0JBQWtCQTtvQ0FDbEJtRyxrQkFBa0JoRDtvQ0FDbEJpRCxlQUFlekM7b0NBQ2ZxQyxlQUFlbEY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUS9CO0dBM1JNaEI7O1FBZWtERiwrQ0FBUUE7OztLQWYxREU7QUE2Uk4sK0RBQWVBLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvVW5pZmllZEVkaXRvci50c3g/NmIwMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IENvbGxhZ2VUZW1wbGF0ZSwgVXBsb2FkZWRQaG90bywgUGxhY2VkUGhvdG8gfSBmcm9tICdAL3R5cGVzL3RlbXBsYXRlJztcbmltcG9ydCB7IHRlbXBsYXRlcyB9IGZyb20gJ0AvZGF0YS90ZW1wbGF0ZXMnO1xuaW1wb3J0IFRlbXBsYXRlU2lkZWJhciBmcm9tICcuL1RlbXBsYXRlU2lkZWJhcic7XG5pbXBvcnQgUGhvdG9VcGxvYWRTaWRlYmFyIGZyb20gJy4vUGhvdG9VcGxvYWRTaWRlYmFyJztcbmltcG9ydCBMaXZlQ2FudmFzIGZyb20gJy4vTGl2ZUNhbnZhcyc7XG5pbXBvcnQgQnJlYWRjcnVtYiwgeyBCcmVhZGNydW1iSXRlbSB9IGZyb20gJy4vQnJlYWRjcnVtYic7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICcuL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCB9IGZyb20gJy4vdWkvY2FyZCc7XG5pbXBvcnQgeyBDaGV2cm9uTGVmdCwgQ2hldnJvblJpZ2h0LCBEb3dubG9hZCwgU2hhcmUyIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHVzZVRvYXN0LCBUb2FzdENvbnRhaW5lciB9IGZyb20gJy4vdWkvdG9hc3QnO1xuXG5pbnRlcmZhY2UgVW5pZmllZEVkaXRvclByb3BzIHtcbiAgaW5pdGlhbFRlbXBsYXRlSWQ/OiBzdHJpbmc7XG59XG5cbmNvbnN0IFVuaWZpZWRFZGl0b3I6IFJlYWN0LkZDPFVuaWZpZWRFZGl0b3JQcm9wcz4gPSAoeyBpbml0aWFsVGVtcGxhdGVJZCB9KSA9PiB7XG4gIC8vIENvcmUgc3RhdGVcbiAgY29uc3QgW3NlbGVjdGVkVGVtcGxhdGUsIHNldFNlbGVjdGVkVGVtcGxhdGVdID0gdXNlU3RhdGU8Q29sbGFnZVRlbXBsYXRlIHwgbnVsbD4oXG4gICAgaW5pdGlhbFRlbXBsYXRlSWQgPyB0ZW1wbGF0ZXMuZmluZCh0ID0+IHQuaWQgPT09IGluaXRpYWxUZW1wbGF0ZUlkKSB8fCB0ZW1wbGF0ZXNbMF0gOiB0ZW1wbGF0ZXNbMF1cbiAgKTtcbiAgY29uc3QgW3VwbG9hZGVkUGhvdG9zLCBzZXRVcGxvYWRlZFBob3Rvc10gPSB1c2VTdGF0ZTxVcGxvYWRlZFBob3RvW10+KFtdKTtcbiAgY29uc3QgW3BsYWNlZFBob3Rvcywgc2V0UGxhY2VkUGhvdG9zXSA9IHVzZVN0YXRlPFBsYWNlZFBob3RvW10+KFtdKTtcblxuICAvLyBVSSBzdGF0ZVxuICBjb25zdCBbaXNUZW1wbGF0ZVNpZGViYXJPcGVuLCBzZXRJc1RlbXBsYXRlU2lkZWJhck9wZW5dID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtpc1Bob3RvU2lkZWJhck9wZW4sIHNldElzUGhvdG9TaWRlYmFyT3Blbl0gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW3NlbGVjdGVkUGhvdG9JZCwgc2V0U2VsZWN0ZWRQaG90b0lkXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNEb3dubG9hZGluZywgc2V0SXNEb3dubG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gVG9hc3Qgbm90aWZpY2F0aW9uc1xuICBjb25zdCB7IHRvYXN0cywgcmVtb3ZlVG9hc3QsIHN1Y2Nlc3MsIGluZm8sIGVycm9yIH0gPSB1c2VUb2FzdCgpO1xuXG4gIC8vIEF1dG8tcGxhY2UgcGhvdG9zIHdoZW4gdGhleSBhcmUgdXBsb2FkZWQgd2l0aCBzbWFydCBwb3NpdGlvbmluZ1xuICBjb25zdCBhdXRvUGxhY2VQaG90b3MgPSB1c2VDYWxsYmFjaygobmV3UGhvdG9zOiBVcGxvYWRlZFBob3RvW10pID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkVGVtcGxhdGUpIHJldHVybjtcblxuICAgIGNvbnN0IGF2YWlsYWJsZVNsb3RzID0gc2VsZWN0ZWRUZW1wbGF0ZS5zbG90cy5maWx0ZXIoc2xvdCA9PlxuICAgICAgIXBsYWNlZFBob3Rvcy5zb21lKHBsYWNlZCA9PiBwbGFjZWQuc2xvdElkID09PSBzbG90LmlkKVxuICAgICk7XG5cbiAgICBjb25zdCBuZXdQbGFjZWRQaG90b3M6IFBsYWNlZFBob3RvW10gPSBbXTtcblxuICAgIG5ld1Bob3Rvcy5mb3JFYWNoKChwaG90bywgaW5kZXgpID0+IHtcbiAgICAgIGlmIChpbmRleCA8IGF2YWlsYWJsZVNsb3RzLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBzbG90ID0gYXZhaWxhYmxlU2xvdHNbaW5kZXhdO1xuXG4gICAgICAgIC8vIENhbGN1bGF0ZSBvcHRpbWFsIHNjYWxlIGJhc2VkIG9uIHBob3RvIGFuZCBzbG90IGFzcGVjdCByYXRpb3NcbiAgICAgICAgY29uc3QgcGhvdG9Bc3BlY3RSYXRpbyA9IHBob3RvLndpZHRoIC8gcGhvdG8uaGVpZ2h0O1xuICAgICAgICBjb25zdCBzbG90QXNwZWN0UmF0aW8gPSBzbG90LndpZHRoIC8gc2xvdC5oZWlnaHQ7XG5cbiAgICAgICAgLy8gU3RhcnQgd2l0aCBhIHNjYWxlIHRoYXQgZml0cyB0aGUgcGhvdG8gbmljZWx5IGluIHRoZSBzbG90XG4gICAgICAgIGxldCBvcHRpbWFsU2NhbGUgPSAxLjA7XG4gICAgICAgIGlmIChwaG90b0FzcGVjdFJhdGlvID4gc2xvdEFzcGVjdFJhdGlvKSB7XG4gICAgICAgICAgLy8gUGhvdG8gaXMgd2lkZXIgdGhhbiBzbG90LCBzY2FsZSB0byBmaXQgaGVpZ2h0XG4gICAgICAgICAgb3B0aW1hbFNjYWxlID0gTWF0aC5taW4oMS4yLCAxLjAgLyAocGhvdG9Bc3BlY3RSYXRpbyAvIHNsb3RBc3BlY3RSYXRpbykpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIC8vIFBob3RvIGlzIHRhbGxlciB0aGFuIHNsb3QsIHNjYWxlIHRvIGZpdCB3aWR0aFxuICAgICAgICAgIG9wdGltYWxTY2FsZSA9IE1hdGgubWluKDEuMiwgc2xvdEFzcGVjdFJhdGlvIC8gcGhvdG9Bc3BlY3RSYXRpbyk7XG4gICAgICAgIH1cblxuICAgICAgICBuZXdQbGFjZWRQaG90b3MucHVzaCh7XG4gICAgICAgICAgcGhvdG9JZDogcGhvdG8uaWQsXG4gICAgICAgICAgc2xvdElkOiBzbG90LmlkLFxuICAgICAgICAgIHg6IDUwLCAvLyBDZW50ZXIgaG9yaXpvbnRhbGx5XG4gICAgICAgICAgeTogNTAsIC8vIENlbnRlciB2ZXJ0aWNhbGx5XG4gICAgICAgICAgc2NhbGU6IE1hdGgubWF4KDAuOCwgTWF0aC5taW4oMS41LCBvcHRpbWFsU2NhbGUpKSwgLy8gQ2xhbXAgc2NhbGVcbiAgICAgICAgICByb3RhdGlvbjogMFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGlmIChuZXdQbGFjZWRQaG90b3MubGVuZ3RoID4gMCkge1xuICAgICAgc2V0UGxhY2VkUGhvdG9zKHByZXYgPT4gWy4uLnByZXYsIC4uLm5ld1BsYWNlZFBob3Rvc10pO1xuXG4gICAgICAvLyBTaG93IGEgYnJpZWYgbm90aWZpY2F0aW9uIGFib3V0IGF1dG8tcGxhY2VtZW50XG4gICAgICBzdWNjZXNzKFxuICAgICAgICBgQXV0by1wbGFjZWQgJHtuZXdQbGFjZWRQaG90b3MubGVuZ3RofSBwaG90byR7bmV3UGxhY2VkUGhvdG9zLmxlbmd0aCA+IDEgPyAncycgOiAnJ31gLFxuICAgICAgICBgUGhvdG9zIHdlcmUgYXV0b21hdGljYWxseSBwbGFjZWQgaW4gYXZhaWxhYmxlIHRlbXBsYXRlIHNsb3RzYFxuICAgICAgKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZFRlbXBsYXRlLCBwbGFjZWRQaG90b3NdKTtcblxuICAvLyBIYW5kbGUgcGhvdG8gdXBsb2FkcyB3aXRoIGF1dG8tcGxhY2VtZW50XG4gIGNvbnN0IGhhbmRsZVBob3Rvc1VwbG9hZGVkID0gdXNlQ2FsbGJhY2soKG5ld1Bob3RvczogVXBsb2FkZWRQaG90b1tdKSA9PiB7XG4gICAgc2V0VXBsb2FkZWRQaG90b3MocHJldiA9PiB7XG4gICAgICBjb25zdCB1cGRhdGVkID0gWy4uLnByZXYsIC4uLm5ld1Bob3Rvc107XG4gICAgICAvLyBBdXRvLXBsYWNlIG5ldyBwaG90b3NcbiAgICAgIGF1dG9QbGFjZVBob3RvcyhuZXdQaG90b3MpO1xuICAgICAgcmV0dXJuIHVwZGF0ZWQ7XG4gICAgfSk7XG4gIH0sIFthdXRvUGxhY2VQaG90b3NdKTtcblxuICAvLyBIYW5kbGUgdGVtcGxhdGUgY2hhbmdlXG4gIGNvbnN0IGhhbmRsZVRlbXBsYXRlQ2hhbmdlID0gdXNlQ2FsbGJhY2soKHRlbXBsYXRlOiBDb2xsYWdlVGVtcGxhdGUpID0+IHtcbiAgICBjb25zdCBwcmV2aW91c1RlbXBsYXRlID0gc2VsZWN0ZWRUZW1wbGF0ZTtcbiAgICBzZXRTZWxlY3RlZFRlbXBsYXRlKHRlbXBsYXRlKTtcbiAgICAvLyBDbGVhciBwbGFjZWQgcGhvdG9zIHdoZW4gdGVtcGxhdGUgY2hhbmdlc1xuICAgIHNldFBsYWNlZFBob3RvcyhbXSk7XG4gICAgc2V0U2VsZWN0ZWRQaG90b0lkKG51bGwpO1xuXG4gICAgLy8gU2hvdyB0ZW1wbGF0ZSBjaGFuZ2Ugbm90aWZpY2F0aW9uXG4gICAgaW5mbyhcbiAgICAgIGBTd2l0Y2hlZCB0byAke3RlbXBsYXRlLm5hbWV9YCxcbiAgICAgIGBUZW1wbGF0ZSBjaGFuZ2VkIGZyb20gJHtwcmV2aW91c1RlbXBsYXRlPy5uYW1lIHx8ICdub25lJ30gdG8gJHt0ZW1wbGF0ZS5uYW1lfWBcbiAgICApO1xuXG4gICAgLy8gQXV0by1wbGFjZSBleGlzdGluZyBwaG90b3MgaW4gbmV3IHRlbXBsYXRlXG4gICAgaWYgKHVwbG9hZGVkUGhvdG9zLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gYXV0b1BsYWNlUGhvdG9zKHVwbG9hZGVkUGhvdG9zKSwgMTAwKTsgLy8gU21hbGwgZGVsYXkgZm9yIGJldHRlciBVWFxuICAgIH1cbiAgfSwgW3VwbG9hZGVkUGhvdG9zLCBhdXRvUGxhY2VQaG90b3MsIHNlbGVjdGVkVGVtcGxhdGUsIGluZm9dKTtcblxuICAvLyBIYW5kbGUgcGhvdG8gcGxhY2VtZW50IGNoYW5nZXNcbiAgY29uc3QgaGFuZGxlUGxhY2VkUGhvdG9zQ2hhbmdlID0gdXNlQ2FsbGJhY2soKG5ld1BsYWNlZFBob3RvczogUGxhY2VkUGhvdG9bXSkgPT4ge1xuICAgIHNldFBsYWNlZFBob3RvcyhuZXdQbGFjZWRQaG90b3MpO1xuICB9LCBbXSk7XG5cbiAgLy8gSGFuZGxlIHBob3RvIHJlbW92YWxcbiAgY29uc3QgaGFuZGxlUGhvdG9SZW1vdmUgPSB1c2VDYWxsYmFjaygocGhvdG9JZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0VXBsb2FkZWRQaG90b3MocHJldiA9PiBwcmV2LmZpbHRlcihwID0+IHAuaWQgIT09IHBob3RvSWQpKTtcbiAgICBzZXRQbGFjZWRQaG90b3MocHJldiA9PiBwcmV2LmZpbHRlcihwID0+IHAucGhvdG9JZCAhPT0gcGhvdG9JZCkpO1xuICAgIGlmIChzZWxlY3RlZFBob3RvSWQgPT09IHBob3RvSWQpIHtcbiAgICAgIHNldFNlbGVjdGVkUGhvdG9JZChudWxsKTtcbiAgICB9XG4gIH0sIFtzZWxlY3RlZFBob3RvSWRdKTtcblxuICAvLyBDYWxjdWxhdGUgY29tcGxldGlvbiBzdGF0dXNcbiAgY29uc3QgZmlsbGVkU2xvdHMgPSBwbGFjZWRQaG90b3MubGVuZ3RoO1xuICBjb25zdCB0b3RhbFNsb3RzID0gc2VsZWN0ZWRUZW1wbGF0ZT8uc2xvdHMubGVuZ3RoIHx8IDA7XG4gIGNvbnN0IGNvbXBsZXRpb25QZXJjZW50YWdlID0gdG90YWxTbG90cyA+IDAgPyBNYXRoLnJvdW5kKChmaWxsZWRTbG90cyAvIHRvdGFsU2xvdHMpICogMTAwKSA6IDA7XG4gIGNvbnN0IGlzQ29tcGxldGUgPSBmaWxsZWRTbG90cyA9PT0gdG90YWxTbG90cyAmJiB0b3RhbFNsb3RzID4gMDtcblxuICAvLyBIYW5kbGUgZG93bmxvYWRcbiAgY29uc3QgaGFuZGxlRG93bmxvYWQgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZFRlbXBsYXRlIHx8IHBsYWNlZFBob3Rvcy5sZW5ndGggPT09IDApIHJldHVybjtcbiAgICBcbiAgICBzZXRJc0Rvd25sb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICAvLyBUaGlzIHdpbGwgYmUgaW1wbGVtZW50ZWQgd2l0aCB0aGUgTGl2ZUNhbnZhcyBjb21wb25lbnRcbiAgICAgIGNvbnNvbGUubG9nKCdEb3dubG9hZCBpbml0aWF0ZWQnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRG93bmxvYWQgZmFpbGVkOicsIGVycm9yKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNEb3dubG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIEhhbmRsZSBzaGFyZVxuICBjb25zdCBoYW5kbGVTaGFyZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAobmF2aWdhdG9yLnNoYXJlKSB7XG4gICAgICB0cnkge1xuICAgICAgICBhd2FpdCBuYXZpZ2F0b3Iuc2hhcmUoe1xuICAgICAgICAgIHRpdGxlOiAnQ2hlY2sgb3V0IG15IHBob3RvIGNvbGxhZ2UhJyxcbiAgICAgICAgICB0ZXh0OiBgSSBjcmVhdGVkIHRoaXMgYW1hemluZyBjb2xsYWdlIHVzaW5nICR7c2VsZWN0ZWRUZW1wbGF0ZT8ubmFtZX0gdGVtcGxhdGVgLFxuICAgICAgICAgIHVybDogd2luZG93LmxvY2F0aW9uLmhyZWYsXG4gICAgICAgIH0pO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ1NoYXJlIGNhbmNlbGxlZCBvciBmYWlsZWQnKTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgLy8gRmFsbGJhY2s6IGNvcHkgVVJMIHRvIGNsaXBib2FyZFxuICAgICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQod2luZG93LmxvY2F0aW9uLmhyZWYpO1xuICAgIH1cbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgey8qIFRvYXN0IE5vdGlmaWNhdGlvbnMgKi99XG4gICAgICA8VG9hc3RDb250YWluZXIgdG9hc3RzPXt0b2FzdHN9IG9uUmVtb3ZlPXtyZW1vdmVUb2FzdH0gLz5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBweC00IHB5LTNcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPlBob3RvIENvbGxhZ2UgTWFrZXI8L2gxPlxuICAgICAgICAgICAge3NlbGVjdGVkVGVtcGxhdGUgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8c3Bhbj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4+e3NlbGVjdGVkVGVtcGxhdGUubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4+4oCiPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPntjb21wbGV0aW9uUGVyY2VudGFnZX0lIGNvbXBsZXRlPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNoYXJlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8U2hhcmUyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICBTaGFyZVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZURvd25sb2FkfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17IWlzQ29tcGxldGUgfHwgaXNEb3dubG9hZGluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIHtpc0Rvd25sb2FkaW5nID8gJ0Rvd25sb2FkaW5nLi4uJyA6ICdEb3dubG9hZCd9XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2hlYWRlcj5cblxuICAgICAgey8qIE1haW4gRWRpdG9yICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIHsvKiBUZW1wbGF0ZSBTaWRlYmFyICovfVxuICAgICAgICA8YXNpZGUgY2xhc3NOYW1lPXtgYmctd2hpdGUgYm9yZGVyLXIgYm9yZGVyLWdyYXktMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCAke1xuICAgICAgICAgIGlzVGVtcGxhdGVTaWRlYmFyT3BlbiA/ICd3LTgwJyA6ICd3LTEyJ1xuICAgICAgICB9YH0+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICB7aXNUZW1wbGF0ZVNpZGViYXJPcGVuICYmIChcbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+VGVtcGxhdGVzPC9oMj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1RlbXBsYXRlU2lkZWJhck9wZW4oIWlzVGVtcGxhdGVTaWRlYmFyT3Blbil9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1RlbXBsYXRlU2lkZWJhck9wZW4gPyA8Q2hldnJvbkxlZnQgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+IDogPENoZXZyb25SaWdodCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz59XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHtpc1RlbXBsYXRlU2lkZWJhck9wZW4gJiYgKFxuICAgICAgICAgICAgICA8VGVtcGxhdGVTaWRlYmFyXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRUZW1wbGF0ZT17c2VsZWN0ZWRUZW1wbGF0ZX1cbiAgICAgICAgICAgICAgICBvblRlbXBsYXRlU2VsZWN0PXtoYW5kbGVUZW1wbGF0ZUNoYW5nZX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvYXNpZGU+XG5cbiAgICAgICAgey8qIENhbnZhcyBBcmVhICovfVxuICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNlwiPlxuICAgICAgICAgICAge3NlbGVjdGVkVGVtcGxhdGUgPyAoXG4gICAgICAgICAgICAgIDxMaXZlQ2FudmFzXG4gICAgICAgICAgICAgICAgdGVtcGxhdGU9e3NlbGVjdGVkVGVtcGxhdGV9XG4gICAgICAgICAgICAgICAgdXBsb2FkZWRQaG90b3M9e3VwbG9hZGVkUGhvdG9zfVxuICAgICAgICAgICAgICAgIHBsYWNlZFBob3Rvcz17cGxhY2VkUGhvdG9zfVxuICAgICAgICAgICAgICAgIHNlbGVjdGVkUGhvdG9JZD17c2VsZWN0ZWRQaG90b0lkfVxuICAgICAgICAgICAgICAgIG9uUGxhY2VkUGhvdG9zQ2hhbmdlPXtoYW5kbGVQbGFjZWRQaG90b3NDaGFuZ2V9XG4gICAgICAgICAgICAgICAgb25QaG90b1NlbGVjdD17c2V0U2VsZWN0ZWRQaG90b0lkfVxuICAgICAgICAgICAgICAgIG9uRG93bmxvYWQ9e2hhbmRsZURvd25sb2FkfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJwLTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTZ4bCBtYi00XCI+8J+OqDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIG1iLTJcIj5TZWxlY3QgYSBUZW1wbGF0ZTwvaDM+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5DaG9vc2UgYSB0ZW1wbGF0ZSBmcm9tIHRoZSBzaWRlYmFyIHRvIGdldCBzdGFydGVkPC9wPlxuICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgIHsvKiBQaG90byBVcGxvYWQgU2lkZWJhciAqL31cbiAgICAgICAgPGFzaWRlIGNsYXNzTmFtZT17YGJnLXdoaXRlIGJvcmRlci1sIGJvcmRlci1ncmF5LTIwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgJHtcbiAgICAgICAgICBpc1Bob3RvU2lkZWJhck9wZW4gPyAndy04MCcgOiAndy0xMidcbiAgICAgICAgfWB9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1mdWxsIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc1Bob3RvU2lkZWJhck9wZW4oIWlzUGhvdG9TaWRlYmFyT3Blbil9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc1Bob3RvU2lkZWJhck9wZW4gPyA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPiA6IDxDaGV2cm9uTGVmdCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz59XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICB7aXNQaG90b1NpZGViYXJPcGVuICYmIChcbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+UGhvdG9zPC9oMj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7aXNQaG90b1NpZGViYXJPcGVuICYmIChcbiAgICAgICAgICAgICAgPFBob3RvVXBsb2FkU2lkZWJhclxuICAgICAgICAgICAgICAgIHVwbG9hZGVkUGhvdG9zPXt1cGxvYWRlZFBob3Rvc31cbiAgICAgICAgICAgICAgICBwbGFjZWRQaG90b3M9e3BsYWNlZFBob3Rvc31cbiAgICAgICAgICAgICAgICBzZWxlY3RlZFBob3RvSWQ9e3NlbGVjdGVkUGhvdG9JZH1cbiAgICAgICAgICAgICAgICBzZWxlY3RlZFRlbXBsYXRlPXtzZWxlY3RlZFRlbXBsYXRlfVxuICAgICAgICAgICAgICAgIG9uUGhvdG9zVXBsb2FkZWQ9e2hhbmRsZVBob3Rvc1VwbG9hZGVkfVxuICAgICAgICAgICAgICAgIG9uUGhvdG9SZW1vdmU9e2hhbmRsZVBob3RvUmVtb3ZlfVxuICAgICAgICAgICAgICAgIG9uUGhvdG9TZWxlY3Q9e3NldFNlbGVjdGVkUGhvdG9JZH1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvYXNpZGU+XG4gICAgICA8L21haW4+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBVbmlmaWVkRWRpdG9yO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInRlbXBsYXRlcyIsIlRlbXBsYXRlU2lkZWJhciIsIlBob3RvVXBsb2FkU2lkZWJhciIsIkxpdmVDYW52YXMiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDaGV2cm9uTGVmdCIsIkNoZXZyb25SaWdodCIsIkRvd25sb2FkIiwiU2hhcmUyIiwidXNlVG9hc3QiLCJUb2FzdENvbnRhaW5lciIsIlVuaWZpZWRFZGl0b3IiLCJpbml0aWFsVGVtcGxhdGVJZCIsInNlbGVjdGVkVGVtcGxhdGUiLCJzZXRTZWxlY3RlZFRlbXBsYXRlIiwiZmluZCIsInQiLCJpZCIsInVwbG9hZGVkUGhvdG9zIiwic2V0VXBsb2FkZWRQaG90b3MiLCJwbGFjZWRQaG90b3MiLCJzZXRQbGFjZWRQaG90b3MiLCJpc1RlbXBsYXRlU2lkZWJhck9wZW4iLCJzZXRJc1RlbXBsYXRlU2lkZWJhck9wZW4iLCJpc1Bob3RvU2lkZWJhck9wZW4iLCJzZXRJc1Bob3RvU2lkZWJhck9wZW4iLCJzZWxlY3RlZFBob3RvSWQiLCJzZXRTZWxlY3RlZFBob3RvSWQiLCJpc0Rvd25sb2FkaW5nIiwic2V0SXNEb3dubG9hZGluZyIsInRvYXN0cyIsInJlbW92ZVRvYXN0Iiwic3VjY2VzcyIsImluZm8iLCJlcnJvciIsImF1dG9QbGFjZVBob3RvcyIsIm5ld1Bob3RvcyIsImF2YWlsYWJsZVNsb3RzIiwic2xvdHMiLCJmaWx0ZXIiLCJzbG90Iiwic29tZSIsInBsYWNlZCIsInNsb3RJZCIsIm5ld1BsYWNlZFBob3RvcyIsImZvckVhY2giLCJwaG90byIsImluZGV4IiwibGVuZ3RoIiwicGhvdG9Bc3BlY3RSYXRpbyIsIndpZHRoIiwiaGVpZ2h0Iiwic2xvdEFzcGVjdFJhdGlvIiwib3B0aW1hbFNjYWxlIiwiTWF0aCIsIm1pbiIsInB1c2giLCJwaG90b0lkIiwieCIsInkiLCJzY2FsZSIsIm1heCIsInJvdGF0aW9uIiwicHJldiIsImhhbmRsZVBob3Rvc1VwbG9hZGVkIiwidXBkYXRlZCIsImhhbmRsZVRlbXBsYXRlQ2hhbmdlIiwidGVtcGxhdGUiLCJwcmV2aW91c1RlbXBsYXRlIiwibmFtZSIsInNldFRpbWVvdXQiLCJoYW5kbGVQbGFjZWRQaG90b3NDaGFuZ2UiLCJoYW5kbGVQaG90b1JlbW92ZSIsInAiLCJmaWxsZWRTbG90cyIsInRvdGFsU2xvdHMiLCJjb21wbGV0aW9uUGVyY2VudGFnZSIsInJvdW5kIiwiaXNDb21wbGV0ZSIsImhhbmRsZURvd25sb2FkIiwiY29uc29sZSIsImxvZyIsImhhbmRsZVNoYXJlIiwibmF2aWdhdG9yIiwic2hhcmUiLCJ0aXRsZSIsInRleHQiLCJ1cmwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJkaXYiLCJjbGFzc05hbWUiLCJvblJlbW92ZSIsImhlYWRlciIsImgxIiwic3BhbiIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsImRpc2FibGVkIiwibWFpbiIsImFzaWRlIiwiaDIiLCJvblRlbXBsYXRlU2VsZWN0Iiwic2VjdGlvbiIsIm9uUGxhY2VkUGhvdG9zQ2hhbmdlIiwib25QaG90b1NlbGVjdCIsIm9uRG93bmxvYWQiLCJoMyIsIm9uUGhvdG9zVXBsb2FkZWQiLCJvblBob3RvUmVtb3ZlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UnifiedEditor.tsx\n"));

/***/ })

});