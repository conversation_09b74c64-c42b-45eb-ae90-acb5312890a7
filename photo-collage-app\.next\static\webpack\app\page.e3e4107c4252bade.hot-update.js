"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _PhotoControls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PhotoControls */ \"(app-pages-browser)/./src/components/PhotoControls.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const updatePlacedPhoto = (photoId, updates)=>{\n        const updatedPlacedPhotos = placedPhotos.map((p)=>p.photoId === photoId ? {\n                ...p,\n                ...updates\n            } : p);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n        // Update selected photo if it's the one being updated\n        if (selectedPhoto && selectedPhoto.photoId === photoId) {\n            setSelectedPhoto({\n                ...selectedPhoto,\n                ...updates\n            });\n        }\n    };\n    const selectPhoto = (placedPhoto)=>{\n        setSelectedPhoto(placedPhoto);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const isSelected = selectedPhoto && selectedPhoto.slotId === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isSelected ? \"solid\" : isHovered ? \"solid\" : \"dashed\", \" \").concat(isSelected ? \"#10b981\" : isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            onClick: ()=>placedPhoto && selectPhoto(placedPhoto),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            removePhotoFromSlot(uploadedPhoto.id);\n                        },\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, undefined),\n                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 border-2 border-green-500 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 145,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 169,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-4 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: \"Drag photos from the sidebar into the slots below.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"md:hidden\",\n                                            children: \"Tap photos below to add them to slots.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        \"Click on placed photos to edit them.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm min-w-0 max-w-full\",\n                                        style: {\n                                            width: \"\".concat(Math.min(canvasWidth, 600), \"px\"),\n                                            height: \"\".concat(Math.min(canvasHeight, 600), \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        onClick: ()=>setSelectedPhoto(null),\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\",\n                                                selectedPhoto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-green-600\",\n                                                    children: \"• Photo selected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        setSelectedPhoto(null);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 lg:grid-cols-1 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move touch-manipulation\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 active:border-blue-500 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 group-active:bg-opacity-30 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 group-active:opacity-100 text-xs font-medium text-center px-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden md:inline\",\n                                                                children: \"Drag to slot\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"md:hidden\",\n                                                                children: \"Tap to select\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: selectedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoControls__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    placedPhoto: selectedPhoto,\n                    uploadedPhoto: getUploadedPhoto(selectedPhoto.photoId),\n                    onUpdate: (updates)=>updatePlacedPhoto(selectedPhoto.photoId, updates),\n                    onRemove: ()=>{\n                        removePhotoFromSlot(selectedPhoto.photoId);\n                        setSelectedPhoto(null);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Photo Controls\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-2\",\n                                        children: \"\\uD83C\\uDF9B️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Click on a placed photo to edit its position, scale, and rotation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"VMujsgWc4pV0IVWP8wKp0d6KjJQ=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});