'use client';

import React from 'react';
import { PlacedPhoto, UploadedPhoto } from '@/types/template';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface PhotoControlsProps {
  placedPhoto: PlacedPhoto;
  uploadedPhoto: UploadedPhoto;
  onUpdate: (updates: Partial<PlacedPhoto>) => void;
  onRemove: () => void;
}

const PhotoControls: React.FC<PhotoControlsProps> = ({
  placedPhoto,
  uploadedPhoto,
  onUpdate,
  onRemove
}) => {
  const handleScaleChange = (scale: number) => {
    onUpdate({ scale: Math.max(0.1, Math.min(3.0, scale)) });
  };

  const handleRotationChange = (rotation: number) => {
    onUpdate({ rotation: rotation % 360 });
  };

  const handlePositionChange = (x: number, y: number) => {
    onUpdate({ 
      x: Math.max(0, Math.min(100, x)),
      y: Math.max(0, Math.min(100, y))
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm">Photo Controls</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Photo Preview */}
        <div className="aspect-square w-20 mx-auto bg-gray-100 rounded-lg overflow-hidden">
          <img
            src={uploadedPhoto.url}
            alt="Photo preview"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Scale Control */}
        <div>
          <label className="text-xs font-medium text-gray-700 block mb-1">
            Scale: {(placedPhoto.scale * 100).toFixed(0)}%
          </label>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleScaleChange(placedPhoto.scale - 0.1)}
              disabled={placedPhoto.scale <= 0.1}
              className="touch-manipulation"
            >
              -
            </Button>
            <input
              type="range"
              min="0.1"
              max="3.0"
              step="0.1"
              value={placedPhoto.scale}
              onChange={(e) => handleScaleChange(parseFloat(e.target.value))}
              className="flex-1 touch-manipulation"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleScaleChange(placedPhoto.scale + 0.1)}
              disabled={placedPhoto.scale >= 3.0}
              className="touch-manipulation"
            >
              +
            </Button>
          </div>
        </div>

        {/* Rotation Control */}
        <div>
          <label className="text-xs font-medium text-gray-700 block mb-1">
            Rotation: {placedPhoto.rotation}°
          </label>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRotationChange(placedPhoto.rotation - 15)}
              className="touch-manipulation"
            >
              ↺
            </Button>
            <input
              type="range"
              min="0"
              max="360"
              step="15"
              value={placedPhoto.rotation}
              onChange={(e) => handleRotationChange(parseInt(e.target.value))}
              className="flex-1 touch-manipulation"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRotationChange(placedPhoto.rotation + 15)}
              className="touch-manipulation"
            >
              ↻
            </Button>
          </div>
        </div>

        {/* Position Controls */}
        <div>
          <label className="text-xs font-medium text-gray-700 block mb-1">
            Position
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="text-xs text-gray-500">X: {placedPhoto.x.toFixed(0)}%</label>
              <input
                type="range"
                min="0"
                max="100"
                step="1"
                value={placedPhoto.x}
                onChange={(e) => handlePositionChange(parseInt(e.target.value), placedPhoto.y)}
                className="w-full touch-manipulation"
              />
            </div>
            <div>
              <label className="text-xs text-gray-500">Y: {placedPhoto.y.toFixed(0)}%</label>
              <input
                type="range"
                min="0"
                max="100"
                step="1"
                value={placedPhoto.y}
                onChange={(e) => handlePositionChange(placedPhoto.x, parseInt(e.target.value))}
                className="w-full touch-manipulation"
              />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onUpdate({ scale: 1.0, rotation: 0, x: 50, y: 50 })}
          >
            Reset
          </Button>
          <Button
            size="sm"
            variant="destructive"
            onClick={onRemove}
          >
            Remove
          </Button>
        </div>

        {/* Fit Options */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // Calculate scale to fit width
              const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;
              onUpdate({ scale: aspectRatio > 1 ? 1.0 : aspectRatio, x: 50, y: 50 });
            }}
          >
            Fit Width
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // Calculate scale to fit height
              const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;
              onUpdate({ scale: aspectRatio < 1 ? 1.0 : 1/aspectRatio, x: 50, y: 50 });
            }}
          >
            Fit Height
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PhotoControls;
