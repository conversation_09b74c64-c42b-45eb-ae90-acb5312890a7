{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.ts"], "names": ["parse", "getFilesystemFrame", "frame", "f", "file", "startsWith", "test", "symbolError", "Symbol", "getErrorSource", "error", "decorateServerError", "type", "Object", "defineProperty", "writable", "enumerable", "configurable", "value", "getServerError", "n", "Error", "message", "e", "name", "stack", "toString", "map", "str", "methodName", "loc", "lineNumber", "column", "join"], "mappings": "AAAA,SAASA,KAAK,QAAQ,uCAAsC;AAG5D,OAAO,SAASC,mBAAmBC,KAAiB;IAClD,MAAMC,IAAgB;QAAE,GAAGD,KAAK;IAAC;IAEjC,IAAI,OAAOC,EAAEC,IAAI,KAAK,UAAU;QAC9B,IACE,SAAS;QACTD,EAAEC,IAAI,CAACC,UAAU,CAAC,QAClB,SAAS;QACT,aAAaC,IAAI,CAACH,EAAEC,IAAI,KACxB,aAAa;QACbD,EAAEC,IAAI,CAACC,UAAU,CAAC,SAClB;YACAF,EAAEC,IAAI,GAAG,AAAC,YAASD,EAAEC,IAAI;QAC3B;IACF;IAEA,OAAOD;AACT;AAEA,MAAMI,cAAcC,OAAO;AAE3B,OAAO,SAASC,eAAeC,KAAY;IACzC,OAAO,AAACA,KAAa,CAACH,YAAY,IAAI;AACxC;AAIA,OAAO,SAASI,oBAAoBD,KAAY,EAAEE,IAAe;IAC/DC,OAAOC,cAAc,CAACJ,OAAOH,aAAa;QACxCQ,UAAU;QACVC,YAAY;QACZC,cAAc;QACdC,OAAON;IACT;AACF;AAEA,OAAO,SAASO,eAAeT,KAAY,EAAEE,IAAe;IAC1D,IAAIQ;IACJ,IAAI;QACF,MAAM,IAAIC,MAAMX,MAAMY,OAAO;IAC/B,EAAE,OAAOC,GAAG;QACVH,IAAIG;IACN;IAEAH,EAAEI,IAAI,GAAGd,MAAMc,IAAI;IACnB,IAAI;QACFJ,EAAEK,KAAK,GAAG,AAAGL,EAAEM,QAAQ,KAAG,OAAI1B,MAAMU,MAAMe,KAAK,EAC5CE,GAAG,CAAC1B,oBACJ0B,GAAG,CAAC,CAACxB;YACJ,IAAIyB,MAAM,AAAC,YAASzB,EAAE0B,UAAU;YAChC,IAAI1B,EAAEC,IAAI,EAAE;gBACV,IAAI0B,MAAM3B,EAAEC,IAAI;gBAChB,IAAID,EAAE4B,UAAU,EAAE;oBAChBD,OAAO,AAAC,MAAG3B,EAAE4B,UAAU;oBACvB,IAAI5B,EAAE6B,MAAM,EAAE;wBACZF,OAAO,AAAC,MAAG3B,EAAE6B,MAAM;oBACrB;gBACF;gBACAJ,OAAO,AAAC,OAAIE,MAAI;YAClB;YACA,OAAOF;QACT,GACCK,IAAI,CAAC;IACV,EAAE,UAAM;QACNb,EAAEK,KAAK,GAAGf,MAAMe,KAAK;IACvB;IAEAd,oBAAoBS,GAAGR;IACvB,OAAOQ;AACT"}