{"version": 3, "sources": ["../../../../../../../src/server/future/route-modules/app-page/vendored/ssr/entrypoints.ts"], "names": ["React", "ReactDOM", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactDOMServerEdge", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "ReactServerDOMTurbopackClientEdge", "ReactServerDOMWebpackClientEdge", "TURBOPACK", "require"], "mappings": "AAAA,YAAYA,WAAW,QAAO;AAC9B,YAAYC,cAAc,kCAAiC;AAC3D,YAAYC,wBAAwB,wBAAuB;AAC3D,YAAYC,qBAAqB,oBAAmB;AAEpD,6DAA6D;AAC7D,YAAYC,wBAAwB,wBAAuB;AAE3D,SAASC,0BACPC,IAA6B,EAC7BC,GAE0C;IAE1C,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC;YAE3N;QACF;IAEJ;AACF;AAEA,IAAIS,mCAAmCC;AACvC,IAAId,QAAQC,GAAG,CAACc,SAAS,EAAE;IACzB,6DAA6D;IAC7DF,oCAAoCG,QAAQ;IAC5C,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CY,kCAAkCjB,0BAChC,aACA;IAEJ;AACF,OAAO;IACL,6DAA6D;IAC7DiB,kCAAkCE,QAAQ;IAC1C,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CW,oCAAoChB,0BAClC,WACA;IAEJ;AACF;AAEA,SACEL,KAAK,EACLE,kBAAkB,EAClBC,eAAe,EACfF,QAAQ,EACRG,kBAAkB,EAClBiB,iCAAiC,EACjCC,+BAA+B,KAChC"}