'use client';

import React, { useState } from 'react';
import { CollageTemplate } from '@/types/template';
import { templates } from '@/data/templates';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import StructuredData, { WebApplicationSchema, HowToSchema, FAQSchema } from './StructuredData';
import Breadcrumb from './Breadcrumb';
import { Heart, Grid, Type, Hash, Circle, Download, Share2 } from 'lucide-react';

interface SimpleEditorProps {
  initialTemplateId?: string;
}

const SimpleEditor: React.FC<SimpleEditorProps> = ({ initialTemplateId }) => {
  const [selectedTemplate, setSelectedTemplate] = useState<CollageTemplate | null>(
    initialTemplateId ? templates.find(t => t.id === initialTemplateId) || templates[0] : templates[0]
  );

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Photo Collage Maker', current: true }
  ];

  const categoryIcons = {
    grid: Grid,
    heart: Heart,
    letter: Type,
    number: Hash,
    shape: Circle
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Structured Data */}
      <StructuredData type="WebApplication" data={WebApplicationSchema} />
      <StructuredData type="HowTo" data={HowToSchema} />
      <StructuredData type="FAQPage" data={FAQSchema} />
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold text-gray-900">Photo Collage Maker</h1>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-1" />
                Share
              </Button>
              <Button size="sm" disabled>
                <Download className="w-4 h-4 mr-1" />
                Download
              </Button>
            </div>
          </div>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto p-8">
          {/* Hero Section */}
          <section className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Create Beautiful Photo Collages Online Free
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Design stunning photo collages with our easy-to-use editor. Choose from heart shapes,
              grids, letters, numbers and custom templates. Upload photos and download high-quality
              collages instantly - no registration required!
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <span>✓ 100% Free</span>
              <span>✓ No Registration</span>
              <span>✓ High Quality Downloads</span>
              <span>✓ Multiple Templates</span>
            </div>
          </section>

          {/* Template Gallery */}
          <section className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-800">Choose Your Template</h2>
                <p className="text-gray-600 mt-1">Select from our collection of beautiful collage templates</p>
              </div>
              {selectedTemplate && (
                <Badge variant="default" className="text-sm">
                  {selectedTemplate.name} Selected
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {templates.map(template => {
                const IconComponent = categoryIcons[template.category as keyof typeof categoryIcons] || Circle;
                const isSelected = selectedTemplate?.id === template.id;

                return (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                      isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
                    }`}
                    onClick={() => setSelectedTemplate(template)}
                    role="button"
                    tabIndex={0}
                    aria-label={`Select ${template.name} template with ${template.slots.length} photo slots`}
                  >
                    <CardContent className="p-4">
                      <div className="aspect-square bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center border-2 border-dashed border-gray-300">
                        <div className="text-center">
                          <IconComponent className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <div className="text-xs text-gray-500 font-medium">
                            {template.slots.length} Photos
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h3 className="font-semibold text-gray-900 text-sm">{template.name}</h3>
                        <p className="text-xs text-gray-600 line-clamp-2">{template.description}</p>

                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs capitalize">
                            {template.category}
                          </Badge>
                          {isSelected && (
                            <div className="text-blue-600 text-xs font-medium">
                              ✓ Selected
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </section>

          {/* Selected Template Details */}
          {selectedTemplate && (
            <section className="bg-white rounded-lg shadow-md p-6 mb-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">
                    Ready to Create: {selectedTemplate.name}
                  </h2>
                  <p className="text-gray-600 mb-4">{selectedTemplate.description}</p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{selectedTemplate.slots.length}</div>
                      <div className="text-xs text-gray-600">Photo Slots</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{selectedTemplate.canvasWidth}</div>
                      <div className="text-xs text-gray-600">Width (px)</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{selectedTemplate.canvasHeight}</div>
                      <div className="text-xs text-gray-600">Height (px)</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600 capitalize">{selectedTemplate.category}</div>
                      <div className="text-xs text-gray-600">Category</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-end">
                <Button variant="outline" onClick={() => setSelectedTemplate(null)}>
                  Choose Different Template
                </Button>
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                  Start Creating Collage →
                </Button>
              </div>
            </section>
          )}

          {/* How It Works Section */}
          <section className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">How It Works</h2>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h3 className="font-semibold mb-2">Choose Template</h3>
                <p className="text-sm text-gray-600">Select from heart shapes, grids, letters, and numbers</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 font-bold">2</span>
                </div>
                <h3 className="font-semibold mb-2">Upload Photos</h3>
                <p className="text-sm text-gray-600">Drag and drop or click to upload your favorite photos</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 font-bold">3</span>
                </div>
                <h3 className="font-semibold mb-2">Arrange & Edit</h3>
                <p className="text-sm text-gray-600">Position, scale, and rotate photos to perfect your collage</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-orange-600 font-bold">4</span>
                </div>
                <h3 className="font-semibold mb-2">Download</h3>
                <p className="text-sm text-gray-600">Save your high-quality collage as PNG image</p>
              </div>
            </div>
          </section>

          {/* Features Section */}
          <section className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">Why Choose Our Collage Maker?</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">100% Free</h3>
                <p className="text-sm text-gray-600">Create unlimited collages without any cost or hidden fees</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Grid className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Templates</h3>
                <p className="text-sm text-gray-600">Choose from hearts, grids, letters, numbers, and custom shapes</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Download className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">High Quality</h3>
                <p className="text-sm text-gray-600">Download your collages in high resolution perfect for printing</p>
              </div>
            </div>
        </section>
      </main>
    </div>
  );
};

export default SimpleEditor;
