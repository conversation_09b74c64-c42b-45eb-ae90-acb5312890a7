'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { CollageTemplate, UploadedPhoto, PlacedPhoto } from '@/types/template';
import { templates } from '@/data/templates';
import TemplateSidebar from './TemplateSidebar';
import PhotoUploadSidebar from './PhotoUploadSidebar';
import LiveCanvas from './LiveCanvas';
import Breadcrumb, { BreadcrumbItem } from './Breadcrumb';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { ChevronLeft, ChevronRight, Download, Share2 } from 'lucide-react';
import { useToast, ToastContainer } from './ui/toast';

interface UnifiedEditorProps {
  initialTemplateId?: string;
}

const UnifiedEditor: React.FC<UnifiedEditorProps> = ({ initialTemplateId }) => {
  // Core state
  const [selectedTemplate, setSelectedTemplate] = useState<CollageTemplate | null>(
    initialTemplateId ? templates.find(t => t.id === initialTemplateId) || templates[0] : templates[0]
  );
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([]);
  const [placedPhotos, setPlacedPhotos] = useState<PlacedPhoto[]>([]);

  // UI state
  const [isTemplateSidebarOpen, setIsTemplateSidebarOpen] = useState(true);
  const [isPhotoSidebarOpen, setIsPhotoSidebarOpen] = useState(true);
  const [selectedPhotoId, setSelectedPhotoId] = useState<string | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  // Toast notifications
  const { toasts, removeToast, success, info, error } = useToast();

  // Auto-place photos when they are uploaded with smart positioning
  const autoPlacePhotos = useCallback((newPhotos: UploadedPhoto[]) => {
    if (!selectedTemplate) return;

    const availableSlots = selectedTemplate.slots.filter(slot =>
      !placedPhotos.some(placed => placed.slotId === slot.id)
    );

    const newPlacedPhotos: PlacedPhoto[] = [];

    newPhotos.forEach((photo, index) => {
      if (index < availableSlots.length) {
        const slot = availableSlots[index];

        // Calculate optimal scale based on photo and slot aspect ratios
        const photoAspectRatio = photo.width / photo.height;
        const slotAspectRatio = slot.width / slot.height;

        // Start with a scale that fits the photo nicely in the slot
        let optimalScale = 1.0;
        if (photoAspectRatio > slotAspectRatio) {
          // Photo is wider than slot, scale to fit height
          optimalScale = Math.min(1.2, 1.0 / (photoAspectRatio / slotAspectRatio));
        } else {
          // Photo is taller than slot, scale to fit width
          optimalScale = Math.min(1.2, slotAspectRatio / photoAspectRatio);
        }

        newPlacedPhotos.push({
          photoId: photo.id,
          slotId: slot.id,
          x: 50, // Center horizontally
          y: 50, // Center vertically
          scale: Math.max(0.8, Math.min(1.5, optimalScale)), // Clamp scale
          rotation: 0
        });
      }
    });

    if (newPlacedPhotos.length > 0) {
      setPlacedPhotos(prev => [...prev, ...newPlacedPhotos]);

      // Show a brief notification about auto-placement
      success(
        `Auto-placed ${newPlacedPhotos.length} photo${newPlacedPhotos.length > 1 ? 's' : ''}`,
        `Photos were automatically placed in available template slots`
      );
    }
  }, [selectedTemplate, placedPhotos]);

  // Handle photo uploads with auto-placement
  const handlePhotosUploaded = useCallback((newPhotos: UploadedPhoto[]) => {
    setUploadedPhotos(prev => {
      const updated = [...prev, ...newPhotos];
      // Auto-place new photos
      autoPlacePhotos(newPhotos);
      return updated;
    });
  }, [autoPlacePhotos]);

  // Handle template change
  const handleTemplateChange = useCallback((template: CollageTemplate) => {
    const previousTemplate = selectedTemplate;
    setSelectedTemplate(template);
    // Clear placed photos when template changes
    setPlacedPhotos([]);
    setSelectedPhotoId(null);

    // Show template change notification
    info(
      `Switched to ${template.name}`,
      `Template changed from ${previousTemplate?.name || 'none'} to ${template.name}`
    );

    // Auto-place existing photos in new template
    if (uploadedPhotos.length > 0) {
      setTimeout(() => autoPlacePhotos(uploadedPhotos), 100); // Small delay for better UX
    }
  }, [uploadedPhotos, autoPlacePhotos, selectedTemplate, info]);

  // Handle photo placement changes
  const handlePlacedPhotosChange = useCallback((newPlacedPhotos: PlacedPhoto[]) => {
    setPlacedPhotos(newPlacedPhotos);
  }, []);

  // Handle photo removal
  const handlePhotoRemove = useCallback((photoId: string) => {
    setUploadedPhotos(prev => prev.filter(p => p.id !== photoId));
    setPlacedPhotos(prev => prev.filter(p => p.photoId !== photoId));
    if (selectedPhotoId === photoId) {
      setSelectedPhotoId(null);
    }
  }, [selectedPhotoId]);

  // Calculate completion status
  const filledSlots = placedPhotos.length;
  const totalSlots = selectedTemplate?.slots.length || 0;
  const completionPercentage = totalSlots > 0 ? Math.round((filledSlots / totalSlots) * 100) : 0;
  const isComplete = filledSlots === totalSlots && totalSlots > 0;

  // Handle download
  const handleDownload = async () => {
    if (!selectedTemplate || placedPhotos.length === 0) return;
    
    setIsDownloading(true);
    try {
      // This will be implemented with the LiveCanvas component
      console.log('Download initiated');
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle share
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Check out my photo collage!',
          text: `I created this amazing collage using ${selectedTemplate?.name} template`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Share cancelled or failed');
      }
    } else {
      // Fallback: copy URL to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold text-gray-900">Photo Collage Maker</h1>
            {selectedTemplate && (
              <div className="hidden md:flex items-center gap-2 text-sm text-gray-600">
                <span>•</span>
                <span>{selectedTemplate.name}</span>
                <span>•</span>
                <span>{completionPercentage}% complete</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="hidden md:flex items-center gap-2"
            >
              <Share2 className="w-4 h-4" />
              Share
            </Button>
            <Button
              onClick={handleDownload}
              disabled={!isComplete || isDownloading}
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              {isDownloading ? 'Downloading...' : 'Download'}
            </Button>
          </div>
        </div>
      </header>

      {/* Main Editor */}
      <main className="flex-1 flex overflow-hidden">
        {/* Template Sidebar */}
        <aside className={`bg-white border-r border-gray-200 transition-all duration-300 ${
          isTemplateSidebarOpen ? 'w-80' : 'w-12'
        }`}>
          <div className="h-full flex flex-col">
            <div className="p-3 border-b border-gray-200 flex items-center justify-between">
              {isTemplateSidebarOpen && (
                <h2 className="font-semibold text-gray-900">Templates</h2>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsTemplateSidebarOpen(!isTemplateSidebarOpen)}
                className="p-1"
              >
                {isTemplateSidebarOpen ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
              </Button>
            </div>
            
            {isTemplateSidebarOpen && (
              <TemplateSidebar
                selectedTemplate={selectedTemplate}
                onTemplateSelect={handleTemplateChange}
              />
            )}
          </div>
        </aside>

        {/* Canvas Area */}
        <section className="flex-1 flex flex-col">
          <div className="flex-1 p-6">
            {selectedTemplate ? (
              <LiveCanvas
                template={selectedTemplate}
                uploadedPhotos={uploadedPhotos}
                placedPhotos={placedPhotos}
                selectedPhotoId={selectedPhotoId}
                onPlacedPhotosChange={handlePlacedPhotosChange}
                onPhotoSelect={setSelectedPhotoId}
                onDownload={handleDownload}
              />
            ) : (
              <div className="h-full flex items-center justify-center">
                <Card className="p-8 text-center">
                  <CardContent>
                    <div className="text-6xl mb-4">🎨</div>
                    <h3 className="text-lg font-semibold mb-2">Select a Template</h3>
                    <p className="text-gray-600">Choose a template from the sidebar to get started</p>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </section>

        {/* Photo Upload Sidebar */}
        <aside className={`bg-white border-l border-gray-200 transition-all duration-300 ${
          isPhotoSidebarOpen ? 'w-80' : 'w-12'
        }`}>
          <div className="h-full flex flex-col">
            <div className="p-3 border-b border-gray-200 flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPhotoSidebarOpen(!isPhotoSidebarOpen)}
                className="p-1"
              >
                {isPhotoSidebarOpen ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
              </Button>
              {isPhotoSidebarOpen && (
                <h2 className="font-semibold text-gray-900">Photos</h2>
              )}
            </div>
            
            {isPhotoSidebarOpen && (
              <PhotoUploadSidebar
                uploadedPhotos={uploadedPhotos}
                placedPhotos={placedPhotos}
                selectedPhotoId={selectedPhotoId}
                selectedTemplate={selectedTemplate}
                onPhotosUploaded={handlePhotosUploaded}
                onPhotoRemove={handlePhotoRemove}
                onPhotoSelect={setSelectedPhotoId}
              />
            )}
          </div>
        </aside>
      </main>
    </div>
  );
};

export default UnifiedEditor;
