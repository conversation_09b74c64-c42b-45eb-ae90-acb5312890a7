"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/UnifiedEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _TemplateSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateSidebar */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PhotoUploadSidebar */ \"(app-pages-browser)/./src/components/PhotoUploadSidebar.tsx\");\n/* harmony import */ var _LiveCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LiveCanvas */ \"(app-pages-browser)/./src/components/LiveCanvas.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _ui_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst UnifiedEditor = (param)=>{\n    let { initialTemplateId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Get template from URL or use default\n    const getInitialTemplate = ()=>{\n        const templateParam = searchParams.get(\"template\");\n        if (templateParam) {\n            const template = _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates.find((t)=>t.id === templateParam);\n            if (template) return template;\n        }\n        return initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates[0];\n    };\n    // Core state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTemplate());\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // UI state\n    const [isTemplateSidebarOpen, setIsTemplateSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPhotoSidebarOpen, setIsPhotoSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedPhotoId, setSelectedPhotoId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Toast notifications\n    const { toasts, removeToast, success, info, error } = (0,_ui_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Auto-place photos when they are uploaded with smart positioning\n    const autoPlacePhotos = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                // Calculate optimal scale based on photo and slot aspect ratios\n                const photoAspectRatio = photo.width / photo.height;\n                const slotAspectRatio = slot.width / slot.height;\n                // Start with a scale that fits the photo nicely in the slot\n                let optimalScale = 1.0;\n                if (photoAspectRatio > slotAspectRatio) {\n                    // Photo is wider than slot, scale to fit height\n                    optimalScale = Math.min(1.2, 1.0 / (photoAspectRatio / slotAspectRatio));\n                } else {\n                    // Photo is taller than slot, scale to fit width\n                    optimalScale = Math.min(1.2, slotAspectRatio / photoAspectRatio);\n                }\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: Math.max(0.8, Math.min(1.5, optimalScale)),\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n            // Show a brief notification about auto-placement\n            success(\"Auto-placed \".concat(newPlacedPhotos.length, \" photo\").concat(newPlacedPhotos.length > 1 ? \"s\" : \"\"), \"Photos were automatically placed in available template slots\");\n        }\n    }, [\n        selectedTemplate,\n        placedPhotos\n    ]);\n    // Handle photo uploads with auto-placement\n    const handlePhotosUploaded = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        setUploadedPhotos((prev)=>{\n            const updated = [\n                ...prev,\n                ...newPhotos\n            ];\n            // Auto-place new photos\n            autoPlacePhotos(newPhotos);\n            return updated;\n        });\n    }, [\n        autoPlacePhotos\n    ]);\n    // Handle template change with URL update\n    const handleTemplateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((template)=>{\n        const previousTemplate = selectedTemplate;\n        setSelectedTemplate(template);\n        // Clear placed photos when template changes\n        setPlacedPhotos([]);\n        setSelectedPhotoId(null);\n        // Update URL with template parameter\n        const newSearchParams = new URLSearchParams(searchParams.toString());\n        newSearchParams.set(\"template\", template.id);\n        router.push(\"?\".concat(newSearchParams.toString()), {\n            scroll: false\n        });\n        // Show template change notification\n        info(\"Switched to \".concat(template.name), \"Template changed from \".concat((previousTemplate === null || previousTemplate === void 0 ? void 0 : previousTemplate.name) || \"none\", \" to \").concat(template.name));\n        // Auto-place existing photos in new template\n        if (uploadedPhotos.length > 0) {\n            setTimeout(()=>autoPlacePhotos(uploadedPhotos), 100); // Small delay for better UX\n        }\n    }, [\n        uploadedPhotos,\n        autoPlacePhotos,\n        selectedTemplate,\n        info,\n        router,\n        searchParams\n    ]);\n    // Handle photo placement changes\n    const handlePlacedPhotosChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    }, []);\n    // Handle photo removal\n    const handlePhotoRemove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((photoId)=>{\n        setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photoId));\n        setPlacedPhotos((prev)=>prev.filter((p)=>p.photoId !== photoId));\n        if (selectedPhotoId === photoId) {\n            setSelectedPhotoId(null);\n        }\n    }, [\n        selectedPhotoId\n    ]);\n    // Calculate completion status\n    const filledSlots = placedPhotos.length;\n    const totalSlots = (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0;\n    const completionPercentage = totalSlots > 0 ? Math.round(filledSlots / totalSlots * 100) : 0;\n    const isComplete = filledSlots === totalSlots && totalSlots > 0;\n    // Handle download\n    const handleDownload = async ()=>{\n        if (!selectedTemplate || placedPhotos.length === 0) return;\n        setIsDownloading(true);\n        try {\n            // This will be implemented with the LiveCanvas component\n            console.log(\"Download initiated\");\n        } catch (error) {\n            console.error(\"Download failed:\", error);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Handle share\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: \"Check out my photo collage!\",\n                    text: \"I created this amazing collage using \".concat(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name, \" template\"),\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Share cancelled or failed\");\n            }\n        } else {\n            // Fallback: copy URL to clipboard\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_toast__WEBPACK_IMPORTED_MODULE_9__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center gap-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: selectedTemplate.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                completionPercentage,\n                                                \"% complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleShare,\n                                    className: \"hidden md:flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Share\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: !isComplete || isDownloading,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isDownloading ? \"Downloading...\" : \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-r border-gray-200 transition-all duration-300 \".concat(isTemplateSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsTemplateSidebarOpen(!isTemplateSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isTemplateSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 42\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 80\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined),\n                                isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    selectedTemplate: selectedTemplate,\n                                    onTemplateSelect: handleTemplateChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-6\",\n                            children: selectedTemplate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LiveCanvas__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                selectedPhotoId: selectedPhotoId,\n                                onPlacedPhotosChange: handlePlacedPhotosChange,\n                                onPhotoSelect: setSelectedPhotoId,\n                                onDownload: handleDownload\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"p-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Select a Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Choose a template from the sidebar to get started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-l border-gray-200 transition-all duration-300 \".concat(isPhotoSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsPhotoSidebarOpen(!isPhotoSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isPhotoSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 39\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 78\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Photos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined),\n                                isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    uploadedPhotos: uploadedPhotos,\n                                    placedPhotos: placedPhotos,\n                                    selectedPhotoId: selectedPhotoId,\n                                    selectedTemplate: selectedTemplate,\n                                    onPhotosUploaded: handlePhotosUploaded,\n                                    onPhotoRemove: handlePhotoRemove,\n                                    onPhotoSelect: setSelectedPhotoId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UnifiedEditor, \"RCCw2X2uo2FjqfoDBT3qR6XpxgM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _ui_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = UnifiedEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedEditor);\nvar _c;\n$RefreshReg$(_c, \"UnifiedEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UnifiedEditor.tsx\n"));

/***/ })

});