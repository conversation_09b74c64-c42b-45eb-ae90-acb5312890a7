"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const updatePlacedPhoto = (photoId, updates)=>{\n        const updatedPlacedPhotos = placedPhotos.map((p)=>p.photoId === photoId ? {\n                ...p,\n                ...updates\n            } : p);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n        // Update selected photo if it's the one being updated\n        if (selectedPhoto && selectedPhoto.photoId === photoId) {\n            setSelectedPhoto({\n                ...selectedPhoto,\n                ...updates\n            });\n        }\n    };\n    const selectPhoto = (placedPhoto)=>{\n        setSelectedPhoto(placedPhoto);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const isSelected = selectedPhoto && selectedPhoto.slotId === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isSelected ? \"solid\" : isHovered ? \"solid\" : \"dashed\", \" \").concat(isSelected ? \"#10b981\" : isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            onClick: ()=>placedPhoto && selectPhoto(placedPhoto),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            removePhotoFromSlot(uploadedPhoto.id);\n                        },\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, undefined),\n                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 border-2 border-green-500 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 145,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 169,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Drag photos from the sidebar into the slots below\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm\",\n                                        style: {\n                                            width: \"\".concat(canvasWidth, \"px\"),\n                                            height: \"\".concat(canvasHeight, \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 text-xs font-medium\",\n                                                        children: \"Drag to slot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"VMujsgWc4pV0IVWP8wKp0d6KjJQ=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});