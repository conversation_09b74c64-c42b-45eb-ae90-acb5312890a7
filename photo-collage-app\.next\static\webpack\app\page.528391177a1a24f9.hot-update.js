"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _PhotoControls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PhotoControls */ \"(app-pages-browser)/./src/components/PhotoControls.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const updatePlacedPhoto = (photoId, updates)=>{\n        const updatedPlacedPhotos = placedPhotos.map((p)=>p.photoId === photoId ? {\n                ...p,\n                ...updates\n            } : p);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n        // Update selected photo if it's the one being updated\n        if (selectedPhoto && selectedPhoto.photoId === photoId) {\n            setSelectedPhoto({\n                ...selectedPhoto,\n                ...updates\n            });\n        }\n    };\n    const selectPhoto = (placedPhoto)=>{\n        setSelectedPhoto(placedPhoto);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const isSelected = selectedPhoto && selectedPhoto.slotId === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isSelected ? \"solid\" : isHovered ? \"solid\" : \"dashed\", \" \").concat(isSelected ? \"#10b981\" : isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            onClick: ()=>placedPhoto && selectPhoto(placedPhoto),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            removePhotoFromSlot(uploadedPhoto.id);\n                        },\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, undefined),\n                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 border-2 border-green-500 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 145,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 169,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-4 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: \"Drag photos from the sidebar into the slots below.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"md:hidden\",\n                                            children: \"Tap photos below to add them to slots.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        \"Click on placed photos to edit them.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm min-w-0 max-w-full\",\n                                        style: {\n                                            width: \"\".concat(Math.min(canvasWidth, 600), \"px\"),\n                                            height: \"\".concat(Math.min(canvasHeight, 600), \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        onClick: ()=>setSelectedPhoto(null),\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\",\n                                                selectedPhoto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-green-600\",\n                                                    children: \"• Photo selected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        setSelectedPhoto(null);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 text-xs font-medium\",\n                                                        children: \"Drag to slot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: selectedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoControls__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    placedPhoto: selectedPhoto,\n                    uploadedPhoto: getUploadedPhoto(selectedPhoto.photoId),\n                    onUpdate: (updates)=>updatePlacedPhoto(selectedPhoto.photoId, updates),\n                    onRemove: ()=>{\n                        removePhotoFromSlot(selectedPhoto.photoId);\n                        setSelectedPhoto(null);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Photo Controls\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-2\",\n                                        children: \"\\uD83C\\uDF9B️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Click on a placed photo to edit its position, scale, and rotation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"VMujsgWc4pV0IVWP8wKp0d6KjJQ=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});