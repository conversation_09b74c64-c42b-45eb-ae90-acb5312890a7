"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst ImageUpload = (param)=>{\n    let { onPhotosUploaded, maxFiles = 20, acceptedTypes = [\n        \"image/jpeg\",\n        \"image/jpg\",\n        \"image/png\",\n        \"image/webp\"\n    ] } = param;\n    _s();\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDragOver, setIsDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            const img = new Image();\n            reader.onload = (e)=>{\n                var _e_target;\n                const url = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                img.onload = ()=>{\n                    const photo = {\n                        id: \"photo-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                        file,\n                        url,\n                        width: img.width,\n                        height: img.height\n                    };\n                    resolve(photo);\n                };\n                img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n                img.src = url;\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsDataURL(file);\n        });\n    }, []);\n    const validateFile = (file)=>{\n        if (!acceptedTypes.includes(file.type)) {\n            return \"File type \".concat(file.type, \" is not supported. Please use JPEG, PNG, or WebP images.\");\n        }\n        if (file.size > 10 * 1024 * 1024) {\n            return \"File size must be less than 10MB.\";\n        }\n        return null;\n    };\n    const processFiles = async (files)=>{\n        setIsUploading(true);\n        const newPhotos = [];\n        const errors = [];\n        for(let i = 0; i < files.length; i++){\n            const file = files[i];\n            if (uploadedPhotos.length + newPhotos.length >= maxFiles) {\n                errors.push(\"Maximum \".concat(maxFiles, \" files allowed. Some files were skipped.\"));\n                break;\n            }\n            const validationError = validateFile(file);\n            if (validationError) {\n                errors.push(\"\".concat(file.name, \": \").concat(validationError));\n                continue;\n            }\n            try {\n                const photo = await createPhotoFromFile(file);\n                newPhotos.push(photo);\n            } catch (error) {\n                errors.push(\"\".concat(file.name, \": Failed to process image\"));\n            }\n        }\n        if (errors.length > 0) {\n            // Better error handling - could be replaced with a toast notification\n            console.error(\"Upload errors:\", errors);\n            alert(errors.join(\"\\n\"));\n        }\n        const updatedPhotos = [\n            ...uploadedPhotos,\n            ...newPhotos\n        ];\n        setUploadedPhotos(updatedPhotos);\n        onPhotosUploaded(updatedPhotos);\n        setIsUploading(false);\n    };\n    const handleFileSelect = (e)=>{\n        const files = e.target.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragOver(false);\n        const files = e.dataTransfer.files;\n        if (files && files.length > 0) {\n            processFiles(files);\n        }\n    };\n    const removePhoto = (photoId)=>{\n        const updatedPhotos = uploadedPhotos.filter((photo)=>photo.id !== photoId);\n        setUploadedPhotos(updatedPhotos);\n        onPhotosUploaded(updatedPhotos);\n    };\n    const openFileDialog = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6 transition-all duration-200 \".concat(isDragOver ? \"border-blue-500 border-2 bg-blue-50\" : \"border-dashed border-2 border-gray-300\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center cursor-pointer\",\n                        onDragOver: handleDragOver,\n                        onDragLeave: handleDragLeave,\n                        onDrop: handleDrop,\n                        onClick: openFileDialog,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: isUploading ? \"⏳\" : isDragOver ? \"\\uD83D\\uDCE4\" : \"\\uD83D\\uDCF7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-2\",\n                                children: isUploading ? \"Processing images...\" : \"Upload Your Photos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: isDragOver ? \"Drop your images here\" : \"Drag and drop images here, or click to select files\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Supported formats: JPEG, PNG, WebP\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Maximum file size: 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Maximum \",\n                                            maxFiles,\n                                            \" files\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            !isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"mt-4\",\n                                disabled: isUploading,\n                                children: \"Choose Files\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ref: fileInputRef,\n                type: \"file\",\n                multiple: true,\n                accept: acceptedTypes.join(\",\"),\n                onChange: handleFileSelect,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            uploadedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: [\n                                        \"Uploaded Photos (\",\n                                        uploadedPhotos.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>{\n                                        setUploadedPhotos([]);\n                                        onPhotosUploaded([]);\n                                    },\n                                    children: \"Clear All\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                            children: uploadedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: photo.url,\n                                                alt: \"Uploaded photo\",\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>removePhoto(photo.id),\n                                            className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                            children: \"\\xd7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 text-xs text-gray-500 truncate\",\n                                            children: photo.file.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: [\n                                                photo.width,\n                                                \" \\xd7 \",\n                                                photo.height\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, photo.id, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageUpload, \"unFRhwm2pWkPzdMzpoNxf1o68AI=\");\n_c = ImageUpload;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ImageUpload);\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ImageUpload.tsx\n"));

/***/ })

});