{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/ComponentStackFrameRow.tsx"], "names": ["React", "useOpenInEditor", "ComponentStackFrameRow", "componentStackFrame", "component", "file", "lineNumber", "column", "open", "div", "data-nextjs-component-stack-frame", "h3", "tabIndex", "role", "onClick", "title", "span", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,SAASC,eAAe,QAAQ,mCAAkC;AAElE,OAAO,SAASC,uBAAuB,KAItC;IAJsC,IAAA,EACrCC,qBAAqB,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAE,EAG7D,GAJsC;IAKrC,MAAMC,OAAOP,gBAAgB;QAC3BI;QACAE;QACAD;IACF;IAEA,qBACE,oBAACG;QAAIC,qCAAAA;qBACH,oBAACC,YAAIP,YACJC,qBACC,oBAACI;QACCG,UAAU;QACVC,MAAM;QACNC,SAASN;QACTO,OAAO;qBAEP,oBAACC,cACEX,MAAK,MAAGC,YAAW,KAAEC,QAAO,oBAE/B,oBAACU;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,oBAACC;QAAKC,GAAE;sBACR,oBAACC;QAASC,QAAO;sBACjB,oBAACC;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;WAGnC;AAGV"}