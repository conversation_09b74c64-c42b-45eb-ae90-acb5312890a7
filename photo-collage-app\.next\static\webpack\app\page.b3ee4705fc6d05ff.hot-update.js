"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_CollageApp__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/CollageApp */ \"(app-pages-browser)/./src/components/CollageApp.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CollageApp__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpRDtBQUVsQyxTQUFTQztJQUN0QixxQkFBTyw4REFBQ0QsOERBQVVBOzs7OztBQUNwQjtLQUZ3QkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9wYWdlLnRzeD9mNjhhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBDb2xsYWdlQXBwIGZyb20gJ0AvY29tcG9uZW50cy9Db2xsYWdlQXBwJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIDxDb2xsYWdlQXBwIC8+O1xufVxuIl0sIm5hbWVzIjpbIkNvbGxhZ2VBcHAiLCJIb21lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(app-pages-browser)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CollageApp = ()=>{\n    _s();\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 39,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 120,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-6xl mb-4\",\n                                    children: \"\\uD83D\\uDEA7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                    children: \"Editor Coming Soon\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-6\",\n                                    children: \"The collage editor is currently under development. You can browse templates and we'll add photo upload and editing features next.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Selected Template: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Photo Slots: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                \"Canvas Size: \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: [\n                                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                        \" \\xd7 \",\n                                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                        \"px\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 31\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 135,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageApp, \"VRQBFVBNyGs2ie7xBu6SudtNgyg=\");\n_c = CollageApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageApp);\nvar _c;\n$RefreshReg$(_c, \"CollageApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageApp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TemplateGallery.tsx":
/*!********************************************!*\
  !*** ./src/components/TemplateGallery.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(app-pages-browser)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst TemplateGallery = (param)=>{\n    let { onTemplateSelect } = param;\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: \"all\",\n            name: \"All Templates\",\n            icon: \"\\uD83C\\uDFA8\"\n        },\n        {\n            id: \"grid\",\n            name: \"Grid Layouts\",\n            icon: \"⚏\"\n        },\n        {\n            id: \"heart\",\n            name: \"Heart Shapes\",\n            icon: \"❤️\"\n        },\n        {\n            id: \"letter\",\n            name: \"Letters\",\n            icon: \"\\uD83D\\uDD24\"\n        },\n        {\n            id: \"number\",\n            name: \"Numbers\",\n            icon: \"\\uD83D\\uDD22\"\n        },\n        {\n            id: \"shape\",\n            name: \"Shapes\",\n            icon: \"\\uD83D\\uDD35\"\n        }\n    ];\n    const filteredTemplates = selectedCategory === \"all\" ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.filter((template)=>template.category === selectedCategory);\n    const handleTemplateClick = (template)=>{\n        setSelectedTemplate(template);\n    };\n    const handleUseTemplate = ()=>{\n        if (selectedTemplate) {\n            onTemplateSelect(selectedTemplate);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                            children: \"Choose Your Template\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                            children: \"Select a template to start creating your collage. Browse by category or view all available options.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-3\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedCategory(category.id),\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: category.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                category.name\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: filteredTemplates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group cursor-pointer transition-all duration-200 \".concat((selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.id) === template.id ? \"ring-2 ring-blue-500 ring-offset-2\" : \"hover:shadow-lg\"),\n                                    onClick: ()=>handleTemplateClick(template),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                                        className: \"h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        template: template,\n                                                        width: 250,\n                                                        showSlotNumbers: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-lg mb-2\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm mb-2\",\n                                                            children: template.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 93,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center text-xs text-gray-500 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-gray-100 px-2 py-1 rounded\",\n                                                                    children: template.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                                    lineNumber: 95,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        template.slots.length,\n                                                                        \" photos\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                                    lineNumber: 98,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.id) === template.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-600 text-sm font-medium\",\n                                                            children: \"✓ Selected\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, template.id, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        filteredTemplates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-400 text-6xl mb-4\",\n                                    children: \"\\uD83C\\uDFA8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-600 mb-2\",\n                                    children: \"No templates found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"Try selecting a different category to see more templates.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 13\n                        }, undefined),\n                        selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-lg text-blue-900\",\n                                                children: selectedTemplate.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-700 text-sm\",\n                                                children: [\n                                                    \"Ready to create your collage with \",\n                                                    selectedTemplate.slots.length,\n                                                    \" photos\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>setSelectedTemplate(null),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                onClick: handleUseTemplate,\n                                                children: \"Use This Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\TemplateGallery.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TemplateGallery, \"P+I3A+UERXxtwxLqDsv+ggerWgQ=\");\n_c = TemplateGallery;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TemplateGallery);\nvar _c;\n$RefreshReg$(_c, \"TemplateGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TemplateGallery.tsx\n"));

/***/ })

});