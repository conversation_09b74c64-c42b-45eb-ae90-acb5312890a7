"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isHovered ? \"solid\" : \"dashed\", \" \").concat(isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>removePhotoFromSlot(uploadedPhoto.id),\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 123,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 141,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 115,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Drag photos from the sidebar into the slots below\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm\",\n                                        style: {\n                                            width: \"\".concat(canvasWidth, \"px\"),\n                                            height: \"\".concat(canvasHeight, \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 text-xs font-medium\",\n                                                        children: \"Drag to slot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"k6lOAEZwIbE/AfwE5r37IYl6JHU=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});