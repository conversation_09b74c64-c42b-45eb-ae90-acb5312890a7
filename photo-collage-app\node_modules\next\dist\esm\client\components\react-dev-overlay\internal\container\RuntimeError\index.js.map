{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/index.tsx"], "names": ["React", "CodeFrame", "noop", "css", "groupStackFramesByFramework", "CallStackFrame", "GroupedStackFrames", "ComponentStackFrameRow", "RuntimeError", "error", "firstFirstPartyFrameIndex", "useMemo", "frames", "findIndex", "entry", "expanded", "Boolean", "originalCodeFrame", "originalStackFrame", "firstFrame", "allLeadingFrames", "slice", "all", "setAll", "useState", "toggleAll", "useCallback", "v", "leading<PERSON>ram<PERSON>", "filter", "f", "allCallStackFrames", "visibleCallStackFrames", "canShowMore", "length", "stackFramesGroupedByFramework", "Fragment", "h2", "map", "frame", "index", "key", "stackFrame", "codeFrame", "undefined", "componentStackFrames", "componentStackFrame", "groupedStackFrames", "button", "tabIndex", "data-nextjs-data-runtime-error-collapsed-action", "type", "onClick", "styles"], "mappings": ";;;;;;;;;;AAAA,YAAYA,WAAW,QAAO;AAC9B,SAASC,SAAS,QAAQ,6BAA4B;AAEtD,SAASC,QAAQC,GAAG,QAAQ,8BAA6B;AAEzD,SAASC,2BAA2B,QAAQ,gDAA+C;AAC3F,SAASC,cAAc,QAAQ,mBAAkB;AACjD,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,sBAAsB,QAAQ,2BAA0B;AAIjE,MAAMC,eAA4C,SAASA,aAAa,KAEvE;IAFuE,IAAA,EACtEC,KAAK,EACN,GAFuE;IAGtE,MAAMC,4BAA4BV,MAAMW,OAAO,CAAS;QACtD,OAAOF,MAAMG,MAAM,CAACC,SAAS,CAC3B,CAACC,QACCA,MAAMC,QAAQ,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;IAEtC,GAAG;QAACT,MAAMG,MAAM;KAAC;IACjB,MAAMO,aAAanB,MAAMW,OAAO,CAA4B;YACnDF;QAAP,OAAOA,CAAAA,0CAAAA,MAAMG,MAAM,CAACF,0BAA0B,YAAvCD,0CAA2C;IACpD,GAAG;QAACA,MAAMG,MAAM;QAAEF;KAA0B;IAE5C,MAAMU,mBAAmBpB,MAAMW,OAAO,CACpC,IACED,4BAA4B,IACxB,EAAE,GACFD,MAAMG,MAAM,CAACS,KAAK,CAAC,GAAGX,4BAC5B;QAACD,MAAMG,MAAM;QAAEF;KAA0B;IAG3C,MAAM,CAACY,KAAKC,OAAO,GAAGvB,MAAMwB,QAAQ,CAACL,cAAc;IACnD,MAAMM,YAAYzB,MAAM0B,WAAW,CAAC;QAClCH,OAAO,CAACI,IAAM,CAACA;IACjB,GAAG,EAAE;IAEL,MAAMC,gBAAgB5B,MAAMW,OAAO,CACjC,IAAMS,iBAAiBS,MAAM,CAAC,CAACC,IAAMA,EAAEf,QAAQ,IAAIO,MACnD;QAACA;QAAKF;KAAiB;IAEzB,MAAMW,qBAAqB/B,MAAMW,OAAO,CACtC,IAAMF,MAAMG,MAAM,CAACS,KAAK,CAACX,4BAA4B,IACrD;QAACD,MAAMG,MAAM;QAAEF;KAA0B;IAE3C,MAAMsB,yBAAyBhC,MAAMW,OAAO,CAC1C,IAAMoB,mBAAmBF,MAAM,CAAC,CAACC,IAAMA,EAAEf,QAAQ,IAAIO,MACrD;QAACA;QAAKS;KAAmB;IAG3B,MAAME,cAAcjC,MAAMW,OAAO,CAAU;QACzC,OACEoB,mBAAmBG,MAAM,KAAKF,uBAAuBE,MAAM,IAC1DZ,OAAOH,cAAc;IAE1B,GAAG;QACDG;QACAS,mBAAmBG,MAAM;QACzBf;QACAa,uBAAuBE,MAAM;KAC9B;IAED,MAAMC,gCAAgCnC,MAAMW,OAAO,CACjD,IAAMP,4BAA4B4B,yBAClC;QAACA;KAAuB;IAG1B,qBACE,oBAAChC,MAAMoC,QAAQ,QACZjB,2BACC,oBAACnB,MAAMoC,QAAQ,sBACb,oBAACC,YAAG,WACHT,cAAcU,GAAG,CAAC,CAACC,OAAOC,sBACzB,oBAACnC;YACCoC,KAAK,AAAC,mBAAgBD,QAAM,MAAGlB;YAC/BiB,OAAOA;2BAGX,oBAACtC;QACCyC,YAAYvB,WAAWD,kBAAkB;QACzCyB,WAAWxB,WAAWF,iBAAiB;UAGzC2B,WAEHnC,MAAMoC,oBAAoB,iBACzB,wDACE,oBAACR,YAAG,oBACH5B,MAAMoC,oBAAoB,CAACP,GAAG,CAAC,CAACQ,qBAAqBN,sBACpD,oBAACjC;YACCkC,KAAKD;YACLM,qBAAqBA;eAIzB,MAEHX,8BAA8BD,MAAM,iBACnC,oBAAClC,MAAMoC,QAAQ,sBACb,oBAACC,YAAG,6BACJ,oBAAC/B;QACCyC,oBAAoBZ;QACpBb,KAAKA;UAGPsB,WACHX,4BACC,oBAACjC,MAAMoC,QAAQ,sBACb,oBAACY;QACCC,UAAU;QACVC,mDAAAA;QACAC,MAAK;QACLC,SAAS3B;OAERH,MAAM,SAAS,QAAO,wBAGzBsB;AAGV;AAEA,OAAO,MAAMS,SAASlD,uBAsFrB;AAED,SAASK,YAAY,GAAE"}