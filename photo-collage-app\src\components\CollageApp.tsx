'use client';

import React, { useState } from 'react';
import { CollageTemplate, UploadedPhoto, PlacedPhoto } from '@/types/template';
import TemplateGallery from './TemplateGallery';
import TemplatePreview from './TemplatePreview';
import ImageUpload from './ImageUpload';
import CollageEditor from './CollageEditor';
import CollageRenderer from './CollageRenderer';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';

type AppState = 'welcome' | 'template-selection' | 'editing' | 'preview';

const CollageApp: React.FC = () => {
  const [currentState, setCurrentState] = useState<AppState>('welcome');
  const [selectedTemplate, setSelectedTemplate] = useState<CollageTemplate | null>(null);
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([]);
  const [placedPhotos, setPlacedPhotos] = useState<PlacedPhoto[]>([]);

  const handleGetStarted = () => {
    setCurrentState('template-selection');
  };

  const handleTemplateSelect = (template: CollageTemplate) => {
    setSelectedTemplate(template);
    setCurrentState('editing');
  };

  const handleBackToTemplates = () => {
    setCurrentState('template-selection');
    setSelectedTemplate(null);
  };

  const handleBackToWelcome = () => {
    setCurrentState('welcome');
    setSelectedTemplate(null);
    setUploadedPhotos([]);
    setPlacedPhotos([]);
  };

  const handlePlacedPhotosChange = (newPlacedPhotos: PlacedPhoto[]) => {
    setPlacedPhotos(newPlacedPhotos);
  };

  const handlePreview = () => {
    setCurrentState('preview');
  };

  const renderWelcomeScreen = () => (
    <div className="max-w-6xl mx-auto">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Get Started</CardTitle>
          <CardDescription>Follow these simple steps to create your collage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center p-6 border rounded-lg">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📷</span>
              </div>
              <h3 className="font-semibold mb-2">1. Choose Template</h3>
              <p className="text-gray-600 text-sm">
                Select from our collection of beautiful templates
              </p>
            </div>
            
            <div className="text-center p-6 border rounded-lg">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📤</span>
              </div>
              <h3 className="font-semibold mb-2">2. Upload Photos</h3>
              <p className="text-gray-600 text-sm">
                Add your favorite photos to the collage
              </p>
            </div>
            
            <div className="text-center p-6 border rounded-lg">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⬇️</span>
              </div>
              <h3 className="font-semibold mb-2">3. Download</h3>
              <p className="text-gray-600 text-sm">
                Save your beautiful collage in high quality
              </p>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <Button size="lg" onClick={handleGetStarted}>
              Start Creating Your Collage
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Feature highlights */}
      <div className="grid md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>🎨</span>
              Multiple Templates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>📱</span>
              Mobile Friendly
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderTemplateSelection = () => (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">Choose Your Template</h1>
          <p className="text-gray-600 mt-2">Select a template to start creating your collage</p>
        </div>
        <Button variant="outline" onClick={handleBackToWelcome}>
          ← Back to Home
        </Button>
      </div>
      <TemplateGallery onTemplateSelect={handleTemplateSelect} />
    </div>
  );

  const renderEditingScreen = () => (
    <div className="max-w-6xl mx-auto">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">
            Editing: {selectedTemplate?.name}
          </h1>
          <p className="text-gray-600 mt-2">
            Upload photos and arrange them in your collage
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={handleBackToTemplates}>
            ← Change Template
          </Button>
          <Button variant="outline" onClick={handleBackToWelcome}>
            ← Home
          </Button>
        </div>
      </div>

      {uploadedPhotos.length === 0 ? (
        <div className="grid lg:grid-cols-3 gap-6">
          {/* Photo Upload Section */}
          <div className="lg:col-span-2">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Upload Photos</CardTitle>
                <CardDescription>
                  Add photos to use in your collage. You need {selectedTemplate?.slots.length} photos for this template.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageUpload
                  onPhotosUploaded={setUploadedPhotos}
                  maxFiles={selectedTemplate?.slots.length || 20}
                />
              </CardContent>
            </Card>
          </div>

          {/* Template Preview Section */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Template Preview</CardTitle>
                <CardDescription>
                  {selectedTemplate?.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex justify-center">
                  <TemplatePreview
                    template={selectedTemplate!}
                    width={280}
                    showSlotNumbers={true}
                  />
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p><strong>Template:</strong> {selectedTemplate?.name}</p>
                  <p><strong>Photo Slots:</strong> {selectedTemplate?.slots.length}</p>
                  <p><strong>Canvas Size:</strong> {selectedTemplate?.canvasWidth} × {selectedTemplate?.canvasHeight}px</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Upload More Photos Section */}
          <Card>
            <CardHeader>
              <CardTitle>Add More Photos ({uploadedPhotos.length} uploaded)</CardTitle>
              <CardDescription>
                You can upload more photos or start arranging the ones you have.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageUpload
                onPhotosUploaded={setUploadedPhotos}
                maxFiles={selectedTemplate?.slots.length || 20}
              />
            </CardContent>
          </Card>

          {/* Collage Editor */}
          <CollageEditor
            template={selectedTemplate!}
            uploadedPhotos={uploadedPhotos}
            onPlacedPhotosChange={handlePlacedPhotosChange}
            onPreview={handlePreview}
          />
        </div>
      )}
    </div>
  );

  const renderPreviewScreen = () => (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">
            Preview: {selectedTemplate?.name}
          </h1>
          <p className="text-gray-600 mt-2">
            Your collage is ready! You can download it or go back to make changes.
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={() => setCurrentState('editing')}>
            ← Back to Editor
          </Button>
          <Button variant="outline" onClick={handleBackToWelcome}>
            ← Home
          </Button>
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Main Preview */}
        <div className="lg:col-span-2">
          <CollageRenderer
            template={selectedTemplate!}
            uploadedPhotos={uploadedPhotos}
            placedPhotos={placedPhotos}
            onDownload={(dataUrl) => {
              console.log('Collage downloaded:', dataUrl.substring(0, 50) + '...');
            }}
          />
        </div>

        {/* Info Panel */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Collage Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium">Template:</span>
                  <br />
                  {selectedTemplate?.name}
                </div>
                <div>
                  <span className="font-medium">Description:</span>
                  <br />
                  {selectedTemplate?.description}
                </div>
                <div>
                  <span className="font-medium">Photos:</span>
                  <br />
                  {placedPhotos.length} of {selectedTemplate?.slots.length} slots filled
                </div>
                <div>
                  <span className="font-medium">Canvas Size:</span>
                  <br />
                  {selectedTemplate?.canvasWidth} × {selectedTemplate?.canvasHeight}px
                </div>
                <div>
                  <span className="font-medium">Category:</span>
                  <br />
                  {selectedTemplate?.category}
                </div>
              </div>

              {placedPhotos.length === selectedTemplate?.slots.length && (
                <div className="mt-6 p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-700">
                    <span>✓</span>
                    <span className="text-sm font-medium">
                      Collage complete!
                    </span>
                  </div>
                  <p className="text-xs text-green-600 mt-1">
                    All photo slots have been filled. You can now download your collage.
                  </p>
                </div>
              )}

              {placedPhotos.length < (selectedTemplate?.slots.length || 0) && (
                <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 text-yellow-700">
                    <span>⚠</span>
                    <span className="text-sm font-medium">
                      Incomplete collage
                    </span>
                  </div>
                  <p className="text-xs text-yellow-600 mt-1">
                    Add {(selectedTemplate?.slots.length || 0) - placedPhotos.length} more photos to complete your collage.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
            Photo Collage Maker
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Create beautiful photo collages with our easy-to-use templates. 
            Upload your photos, choose a template, and download your masterpiece!
          </p>
        </div>
        
        {currentState === 'welcome' && renderWelcomeScreen()}
        {currentState === 'template-selection' && renderTemplateSelection()}
        {currentState === 'editing' && renderEditingScreen()}
        {currentState === 'preview' && renderPreviewScreen()}
      </div>
    </main>
  );
};

export default CollageApp;
