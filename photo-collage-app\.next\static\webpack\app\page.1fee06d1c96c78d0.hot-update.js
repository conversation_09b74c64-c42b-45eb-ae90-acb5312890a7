"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/UnifiedEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/UnifiedEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _TemplateSidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TemplateSidebar */ \"(app-pages-browser)/./src/components/TemplateSidebar.tsx\");\n/* harmony import */ var _PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PhotoUploadSidebar */ \"(app-pages-browser)/./src/components/PhotoUploadSidebar.tsx\");\n/* harmony import */ var _LiveCanvas__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./LiveCanvas */ \"(app-pages-browser)/./src/components/LiveCanvas.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Download,Share2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _ui_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ui/toast */ \"(app-pages-browser)/./src/components/ui/toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst UnifiedEditor = (param)=>{\n    let { initialTemplateId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    // Get template from URL or use default\n    const getInitialTemplate = ()=>{\n        const templateParam = searchParams.get(\"template\");\n        if (templateParam) {\n            const template = _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates.find((t)=>t.id === templateParam);\n            if (template) return template;\n        }\n        return initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_3__.templates[0];\n    };\n    // Core state\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTemplate());\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // UI state\n    const [isTemplateSidebarOpen, setIsTemplateSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isPhotoSidebarOpen, setIsPhotoSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedPhotoId, setSelectedPhotoId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDownloading, setIsDownloading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Toast notifications\n    const { toasts, removeToast, success, info, error } = (0,_ui_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    // Auto-place photos when they are uploaded with smart positioning\n    const autoPlacePhotos = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                // Calculate optimal scale based on photo and slot aspect ratios\n                const photoAspectRatio = photo.width / photo.height;\n                const slotAspectRatio = slot.width / slot.height;\n                // Start with a scale that fits the photo nicely in the slot\n                let optimalScale = 1.0;\n                if (photoAspectRatio > slotAspectRatio) {\n                    // Photo is wider than slot, scale to fit height\n                    optimalScale = Math.min(1.2, 1.0 / (photoAspectRatio / slotAspectRatio));\n                } else {\n                    // Photo is taller than slot, scale to fit width\n                    optimalScale = Math.min(1.2, slotAspectRatio / photoAspectRatio);\n                }\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: Math.max(0.8, Math.min(1.5, optimalScale)),\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n            // Show a brief notification about auto-placement\n            success(\"Auto-placed \".concat(newPlacedPhotos.length, \" photo\").concat(newPlacedPhotos.length > 1 ? \"s\" : \"\"), \"Photos were automatically placed in available template slots\");\n        }\n    }, [\n        selectedTemplate,\n        placedPhotos\n    ]);\n    // Handle photo uploads with auto-placement\n    const handlePhotosUploaded = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPhotos)=>{\n        setUploadedPhotos((prev)=>{\n            const updated = [\n                ...prev,\n                ...newPhotos\n            ];\n            // Auto-place new photos\n            autoPlacePhotos(newPhotos);\n            return updated;\n        });\n    }, [\n        autoPlacePhotos\n    ]);\n    // Handle template change with URL update\n    const handleTemplateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((template)=>{\n        const previousTemplate = selectedTemplate;\n        setSelectedTemplate(template);\n        // Clear placed photos when template changes\n        setPlacedPhotos([]);\n        setSelectedPhotoId(null);\n        // Update URL with template parameter\n        const newSearchParams = new URLSearchParams(searchParams.toString());\n        newSearchParams.set(\"template\", template.id);\n        router.push(\"?\".concat(newSearchParams.toString()), {\n            scroll: false\n        });\n        // Show template change notification\n        info(\"Switched to \".concat(template.name), \"Template changed from \".concat((previousTemplate === null || previousTemplate === void 0 ? void 0 : previousTemplate.name) || \"none\", \" to \").concat(template.name));\n        // Auto-place existing photos in new template\n        if (uploadedPhotos.length > 0) {\n            setTimeout(()=>autoPlacePhotos(uploadedPhotos), 100); // Small delay for better UX\n        }\n    }, [\n        uploadedPhotos,\n        autoPlacePhotos,\n        selectedTemplate,\n        info,\n        router,\n        searchParams\n    ]);\n    // Handle photo placement changes\n    const handlePlacedPhotosChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    }, []);\n    // Handle photo removal\n    const handlePhotoRemove = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((photoId)=>{\n        setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photoId));\n        setPlacedPhotos((prev)=>prev.filter((p)=>p.photoId !== photoId));\n        if (selectedPhotoId === photoId) {\n            setSelectedPhotoId(null);\n        }\n    }, [\n        selectedPhotoId\n    ]);\n    // Calculate completion status\n    const filledSlots = placedPhotos.length;\n    const totalSlots = (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 0;\n    const completionPercentage = totalSlots > 0 ? Math.round(filledSlots / totalSlots * 100) : 0;\n    const isComplete = filledSlots === totalSlots && totalSlots > 0;\n    // Generate breadcrumb items\n    const breadcrumbItems = [\n        {\n            label: \"Home\",\n            href: \"/\"\n        },\n        {\n            label: \"Photo Collage Maker\",\n            href: \"/\"\n        },\n        ...selectedTemplate ? [\n            {\n                label: selectedTemplate.name,\n                href: \"/?template=\".concat(selectedTemplate.id),\n                current: true\n            }\n        ] : []\n    ];\n    // Handle download\n    const handleDownload = async ()=>{\n        if (!selectedTemplate || placedPhotos.length === 0) return;\n        setIsDownloading(true);\n        try {\n            // This will be implemented with the LiveCanvas component\n            console.log(\"Download initiated\");\n        } catch (error) {\n            console.error(\"Download failed:\", error);\n        } finally{\n            setIsDownloading(false);\n        }\n    };\n    // Handle share\n    const handleShare = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: \"Check out my photo collage!\",\n                    text: \"I created this amazing collage using \".concat(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name, \" template\"),\n                    url: window.location.href\n                });\n            } catch (error) {\n                console.log(\"Share cancelled or failed\");\n            }\n        } else {\n            // Fallback: copy URL to clipboard\n            navigator.clipboard.writeText(window.location.href);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_toast__WEBPACK_IMPORTED_MODULE_9__.ToastContainer, {\n                toasts: toasts,\n                onRemove: removeToast\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center gap-2 text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: selectedTemplate.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                completionPercentage,\n                                                \"% complete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleShare,\n                                    className: \"hidden md:flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Share\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: !isComplete || isDownloading,\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isDownloading ? \"Downloading...\" : \"Download\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 flex overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-r border-gray-200 transition-all duration-300 \".concat(isTemplateSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsTemplateSidebarOpen(!isTemplateSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isTemplateSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 42\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 80\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined),\n                                isTemplateSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateSidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    selectedTemplate: selectedTemplate,\n                                    onTemplateSelect: handleTemplateChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-6\",\n                            children: selectedTemplate ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LiveCanvas__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                template: selectedTemplate,\n                                uploadedPhotos: uploadedPhotos,\n                                placedPhotos: placedPhotos,\n                                selectedPhotoId: selectedPhotoId,\n                                onPlacedPhotosChange: handlePlacedPhotosChange,\n                                onPhotoSelect: setSelectedPhotoId,\n                                onDownload: handleDownload\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                                    className: \"p-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl mb-4\",\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Select a Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Choose a template from the sidebar to get started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                        className: \"bg-white border-l border-gray-200 transition-all duration-300 \".concat(isPhotoSidebarOpen ? \"w-80\" : \"w-12\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-b border-gray-200 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>setIsPhotoSidebarOpen(!isPhotoSidebarOpen),\n                                            className: \"p-1\",\n                                            children: isPhotoSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 39\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Download_Share2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 78\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-gray-900\",\n                                            children: \"Photos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, undefined),\n                                isPhotoSidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoUploadSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    uploadedPhotos: uploadedPhotos,\n                                    placedPhotos: placedPhotos,\n                                    selectedPhotoId: selectedPhotoId,\n                                    selectedTemplate: selectedTemplate,\n                                    onPhotosUploaded: handlePhotosUploaded,\n                                    onPhotoRemove: handlePhotoRemove,\n                                    onPhotoSelect: setSelectedPhotoId\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\UnifiedEditor.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UnifiedEditor, \"RCCw2X2uo2FjqfoDBT3qR6XpxgM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _ui_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = UnifiedEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedEditor);\nvar _c;\n$RefreshReg$(_c, \"UnifiedEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/UnifiedEditor.tsx\n"));

/***/ })

});