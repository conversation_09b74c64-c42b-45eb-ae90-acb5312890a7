{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/CallStackFrame.tsx"], "names": ["React", "getFrameSource", "useOpenInEditor", "CallStackFrame", "frame", "f", "originalStackFrame", "sourceStackFrame", "hasSource", "Boolean", "originalCodeFrame", "open", "file", "lineNumber", "column", "undefined", "div", "data-nextjs-call-stack-frame", "h3", "data-nextjs-frame-expanded", "expanded", "methodName", "data-has-source", "tabIndex", "role", "onClick", "title", "span", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "line", "x1", "y1", "x2", "y2"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,SACEC,cAAc,QAET,4BAA2B;AAClC,SAASC,eAAe,QAAQ,mCAAkC;AAElE,OAAO,MAAMC,iBAER,SAASA,eAAe,KAAS;IAAT,IAAA,EAAEC,KAAK,EAAE,GAAT;QAILA;IAHtB,0CAA0C;IAC1C,2CAA2C;IAE3C,MAAMC,IAAgBD,CAAAA,4BAAAA,MAAME,kBAAkB,YAAxBF,4BAA4BA,MAAMG,gBAAgB;IACxE,MAAMC,YAAYC,QAAQL,MAAMM,iBAAiB;IACjD,MAAMC,OAAOT,gBACXM,YACI;QACEI,MAAMP,EAAEO,IAAI;QACZC,YAAYR,EAAEQ,UAAU;QACxBC,QAAQT,EAAES,MAAM;IAClB,IACAC;IAGN,qBACE,oBAACC;QAAIC,gCAAAA;qBACH,oBAACC;QAAGC,8BAA4BV,QAAQL,MAAMgB,QAAQ;OACnDf,EAAEgB,UAAU,iBAEf,oBAACL;QACCM,mBAAiBd,YAAY,SAASO;QACtCQ,UAAUf,YAAY,KAAKO;QAC3BS,MAAMhB,YAAY,SAASO;QAC3BU,SAASd;QACTe,OAAOlB,YAAY,iCAAiCO;qBAEpD,oBAACY,cAAM1B,eAAeI,mBACtB,oBAACuB;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,oBAACC;QAAKC,GAAE;sBACR,oBAACC;QAASC,QAAO;sBACjB,oBAACC;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;;AAK3C,EAAC"}