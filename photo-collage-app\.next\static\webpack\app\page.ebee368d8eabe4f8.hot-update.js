"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _PhotoControls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PhotoControls */ \"(app-pages-browser)/./src/components/PhotoControls.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const updatePlacedPhoto = (photoId, updates)=>{\n        const updatedPlacedPhotos = placedPhotos.map((p)=>p.photoId === photoId ? {\n                ...p,\n                ...updates\n            } : p);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n        // Update selected photo if it's the one being updated\n        if (selectedPhoto && selectedPhoto.photoId === photoId) {\n            setSelectedPhoto({\n                ...selectedPhoto,\n                ...updates\n            });\n        }\n    };\n    const selectPhoto = (placedPhoto)=>{\n        setSelectedPhoto(placedPhoto);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const isSelected = selectedPhoto && selectedPhoto.slotId === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isSelected ? \"solid\" : isHovered ? \"solid\" : \"dashed\", \" \").concat(isSelected ? \"#10b981\" : isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            onClick: ()=>placedPhoto && selectPhoto(placedPhoto),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: (e)=>{\n                            e.stopPropagation();\n                            removePhotoFromSlot(uploadedPhoto.id);\n                        },\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, undefined),\n                    isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 border-2 border-green-500 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 145,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 169,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-4 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden md:inline\",\n                                            children: \"Drag photos from the sidebar into the slots below.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"md:hidden\",\n                                            children: \"Tap photos below to add them to slots.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \" \",\n                                        \"Click on placed photos to edit them.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm\",\n                                        style: {\n                                            width: \"\".concat(canvasWidth, \"px\"),\n                                            height: \"\".concat(canvasHeight, \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        onClick: ()=>setSelectedPhoto(null),\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\",\n                                                selectedPhoto && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 text-green-600\",\n                                                    children: \"• Photo selected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        setSelectedPhoto(null);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 text-xs font-medium\",\n                                                        children: \"Drag to slot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: selectedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PhotoControls__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    placedPhoto: selectedPhoto,\n                    uploadedPhoto: getUploadedPhoto(selectedPhoto.photoId),\n                    onUpdate: (updates)=>updatePlacedPhoto(selectedPhoto.photoId, updates),\n                    onRemove: ()=>{\n                        removePhotoFromSlot(selectedPhoto.photoId);\n                        setSelectedPhoto(null);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-sm\",\n                                children: \"Photo Controls\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl mb-2\",\n                                        children: \"\\uD83C\\uDF9B️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: \"Click on a placed photo to edit its position, scale, and rotation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"VMujsgWc4pV0IVWP8wKp0d6KjJQ=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});