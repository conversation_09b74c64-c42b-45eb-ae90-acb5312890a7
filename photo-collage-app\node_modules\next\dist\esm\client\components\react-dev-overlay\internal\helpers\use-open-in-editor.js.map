{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-open-in-editor.ts"], "names": ["useCallback", "useOpenInEditor", "file", "lineNumber", "column", "openInEditor", "params", "URLSearchParams", "append", "String", "self", "fetch", "process", "env", "__NEXT_ROUTER_BASEPATH", "toString", "then", "console", "error"], "mappings": "AAAA,SAASA,WAAW,QAAQ,QAAO;AAEnC,OAAO,SAASC,gBAAgB;IAAA,IAAA,EAC9BC,IAAI,EACJC,UAAU,EACVC,MAAM,EAKP,GAR+B,mBAQ5B,CAAC,IAR2B;IAS9B,MAAMC,eAAeL,YAAY;QAC/B,IAAIE,QAAQ,QAAQC,cAAc,QAAQC,UAAU,MAAM;QAE1D,MAAME,SAAS,IAAIC;QACnBD,OAAOE,MAAM,CAAC,QAAQN;QACtBI,OAAOE,MAAM,CAAC,cAAcC,OAAON;QACnCG,OAAOE,MAAM,CAAC,UAAUC,OAAOL;QAE/BM,KACGC,KAAK,CACJ,AACEC,CAAAA,QAAQC,GAAG,CAACC,sBAAsB,IAAI,EAAC,IACxC,6BAA0BR,OAAOS,QAAQ,IAE3CC,IAAI,CACH,KAAO,GACP;YACEC,QAAQC,KAAK,CAAC;QAChB;IAEN,GAAG;QAAChB;QAAMC;QAAYC;KAAO;IAE7B,OAAOC;AACT"}