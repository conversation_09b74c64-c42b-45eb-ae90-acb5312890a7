"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/PhotoControls.tsx":
/*!******************************************!*\
  !*** ./src/components/PhotoControls.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst PhotoControls = (param)=>{\n    let { placedPhoto, uploadedPhoto, onUpdate, onRemove } = param;\n    const handleScaleChange = (scale)=>{\n        onUpdate({\n            scale: Math.max(0.1, Math.min(3.0, scale))\n        });\n    };\n    const handleRotationChange = (rotation)=>{\n        onUpdate({\n            rotation: rotation % 360\n        });\n    };\n    const handlePositionChange = (x, y)=>{\n        onUpdate({\n            x: Math.max(0, Math.min(100, x)),\n            y: Math.max(0, Math.min(100, y))\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                    className: \"text-sm\",\n                    children: \"Photo Controls\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"aspect-square w-20 mx-auto bg-gray-100 rounded-lg overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: uploadedPhoto.url,\n                            alt: \"Photo preview\",\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Scale: \",\n                                    (placedPhoto.scale * 100).toFixed(0),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale - 0.1),\n                                        disabled: placedPhoto.scale <= 0.1,\n                                        className: \"touch-manipulation\",\n                                        children: \"-\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0.1\",\n                                        max: \"3.0\",\n                                        step: \"0.1\",\n                                        value: placedPhoto.scale,\n                                        onChange: (e)=>handleScaleChange(parseFloat(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleScaleChange(placedPhoto.scale + 0.1),\n                                        disabled: placedPhoto.scale >= 3.0,\n                                        className: \"touch-manipulation\",\n                                        children: \"+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: [\n                                    \"Rotation: \",\n                                    placedPhoto.rotation,\n                                    \"\\xb0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation - 15),\n                                        className: \"touch-manipulation\",\n                                        children: \"↺\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"range\",\n                                        min: \"0\",\n                                        max: \"360\",\n                                        step: \"15\",\n                                        value: placedPhoto.rotation,\n                                        onChange: (e)=>handleRotationChange(parseInt(e.target.value)),\n                                        className: \"flex-1 touch-manipulation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: ()=>handleRotationChange(placedPhoto.rotation + 15),\n                                        className: \"touch-manipulation\",\n                                        children: \"↻\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-xs font-medium text-gray-700 block mb-1\",\n                                children: \"Position\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"X: \",\n                                                    placedPhoto.x.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.x,\n                                                onChange: (e)=>handlePositionChange(parseInt(e.target.value), placedPhoto.y),\n                                                className: \"w-full touch-manipulation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: [\n                                                    \"Y: \",\n                                                    placedPhoto.y.toFixed(0),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"range\",\n                                                min: \"0\",\n                                                max: \"100\",\n                                                step: \"1\",\n                                                value: placedPhoto.y,\n                                                onChange: (e)=>handlePositionChange(placedPhoto.x, parseInt(e.target.value)),\n                                                className: \"w-full touch-manipulation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>onUpdate({\n                                        scale: 1.0,\n                                        rotation: 0,\n                                        x: 50,\n                                        y: 50\n                                    }),\n                                children: \"Reset\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"destructive\",\n                                onClick: onRemove,\n                                children: \"Remove\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit width\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio > 1 ? 1.0 : aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Width\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                size: \"sm\",\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Calculate scale to fit height\n                                    const aspectRatio = uploadedPhoto.width / uploadedPhoto.height;\n                                    onUpdate({\n                                        scale: aspectRatio < 1 ? 1.0 : 1 / aspectRatio,\n                                        x: 50,\n                                        y: 50\n                                    });\n                                },\n                                children: \"Fit Height\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\PhotoControls.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PhotoControls;\n/* harmony default export */ __webpack_exports__[\"default\"] = (PhotoControls);\nvar _c;\n$RefreshReg$(_c, \"PhotoControls\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Bob3RvQ29udHJvbHMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFMEI7QUFFVztBQUNnQztBQVNyRSxNQUFNTSxnQkFBOEM7UUFBQyxFQUNuREMsV0FBVyxFQUNYQyxhQUFhLEVBQ2JDLFFBQVEsRUFDUkMsUUFBUSxFQUNUO0lBQ0MsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCSCxTQUFTO1lBQUVHLE9BQU9DLEtBQUtDLEdBQUcsQ0FBQyxLQUFLRCxLQUFLRSxHQUFHLENBQUMsS0FBS0g7UUFBUTtJQUN4RDtJQUVBLE1BQU1JLHVCQUF1QixDQUFDQztRQUM1QlIsU0FBUztZQUFFUSxVQUFVQSxXQUFXO1FBQUk7SUFDdEM7SUFFQSxNQUFNQyx1QkFBdUIsQ0FBQ0MsR0FBV0M7UUFDdkNYLFNBQVM7WUFDUFUsR0FBR04sS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxLQUFLSTtZQUM3QkMsR0FBR1AsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxLQUFLSztRQUMvQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNsQiwwQ0FBSUE7UUFBQ21CLFdBQVU7OzBCQUNkLDhEQUFDakIsZ0RBQVVBOzBCQUNULDRFQUFDQywrQ0FBU0E7b0JBQUNnQixXQUFVOzhCQUFVOzs7Ozs7Ozs7OzswQkFFakMsOERBQUNsQixpREFBV0E7Z0JBQUNrQixXQUFVOztrQ0FFckIsOERBQUNDO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDRTs0QkFDQ0MsS0FBS2hCLGNBQWNpQixHQUFHOzRCQUN0QkMsS0FBSTs0QkFDSkwsV0FBVTs7Ozs7Ozs7Ozs7a0NBS2QsOERBQUNDOzswQ0FDQyw4REFBQ0s7Z0NBQU1OLFdBQVU7O29DQUErQztvQ0FDckRkLENBQUFBLFlBQVlLLEtBQUssR0FBRyxHQUFFLEVBQUdnQixPQUFPLENBQUM7b0NBQUc7Ozs7Ozs7MENBRS9DLDhEQUFDTjtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNwQiw4Q0FBTUE7d0NBQ0w0QixNQUFLO3dDQUNMQyxTQUFRO3dDQUNSQyxTQUFTLElBQU1wQixrQkFBa0JKLFlBQVlLLEtBQUssR0FBRzt3Q0FDckRvQixVQUFVekIsWUFBWUssS0FBSyxJQUFJO3dDQUMvQlMsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDWTt3Q0FDQ0MsTUFBSzt3Q0FDTG5CLEtBQUk7d0NBQ0pELEtBQUk7d0NBQ0pxQixNQUFLO3dDQUNMQyxPQUFPN0IsWUFBWUssS0FBSzt3Q0FDeEJ5QixVQUFVLENBQUNDLElBQU0zQixrQkFBa0I0QixXQUFXRCxFQUFFRSxNQUFNLENBQUNKLEtBQUs7d0NBQzVEZixXQUFVOzs7Ozs7a0RBRVosOERBQUNwQiw4Q0FBTUE7d0NBQ0w0QixNQUFLO3dDQUNMQyxTQUFRO3dDQUNSQyxTQUFTLElBQU1wQixrQkFBa0JKLFlBQVlLLEtBQUssR0FBRzt3Q0FDckRvQixVQUFVekIsWUFBWUssS0FBSyxJQUFJO3dDQUMvQlMsV0FBVTtrREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9MLDhEQUFDQzs7MENBQ0MsOERBQUNLO2dDQUFNTixXQUFVOztvQ0FBK0M7b0NBQ25EZCxZQUFZVSxRQUFRO29DQUFDOzs7Ozs7OzBDQUVsQyw4REFBQ0s7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDcEIsOENBQU1BO3dDQUNMNEIsTUFBSzt3Q0FDTEMsU0FBUTt3Q0FDUkMsU0FBUyxJQUFNZixxQkFBcUJULFlBQVlVLFFBQVEsR0FBRzt3Q0FDM0RJLFdBQVU7a0RBQ1g7Ozs7OztrREFHRCw4REFBQ1k7d0NBQ0NDLE1BQUs7d0NBQ0xuQixLQUFJO3dDQUNKRCxLQUFJO3dDQUNKcUIsTUFBSzt3Q0FDTEMsT0FBTzdCLFlBQVlVLFFBQVE7d0NBQzNCb0IsVUFBVSxDQUFDQyxJQUFNdEIscUJBQXFCeUIsU0FBU0gsRUFBRUUsTUFBTSxDQUFDSixLQUFLO3dDQUM3RGYsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDcEIsOENBQU1BO3dDQUNMNEIsTUFBSzt3Q0FDTEMsU0FBUTt3Q0FDUkMsU0FBUyxJQUFNZixxQkFBcUJULFlBQVlVLFFBQVEsR0FBRzt3Q0FDM0RJLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPTCw4REFBQ0M7OzBDQUNDLDhEQUFDSztnQ0FBTU4sV0FBVTswQ0FBK0M7Ozs7OzswQ0FHaEUsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7OzBEQUNDLDhEQUFDSztnREFBTU4sV0FBVTs7b0RBQXdCO29EQUFJZCxZQUFZWSxDQUFDLENBQUNTLE9BQU8sQ0FBQztvREFBRzs7Ozs7OzswREFDdEUsOERBQUNLO2dEQUNDQyxNQUFLO2dEQUNMbkIsS0FBSTtnREFDSkQsS0FBSTtnREFDSnFCLE1BQUs7Z0RBQ0xDLE9BQU83QixZQUFZWSxDQUFDO2dEQUNwQmtCLFVBQVUsQ0FBQ0MsSUFBTXBCLHFCQUFxQnVCLFNBQVNILEVBQUVFLE1BQU0sQ0FBQ0osS0FBSyxHQUFHN0IsWUFBWWEsQ0FBQztnREFDN0VDLFdBQVU7Ozs7Ozs7Ozs7OztrREFHZCw4REFBQ0M7OzBEQUNDLDhEQUFDSztnREFBTU4sV0FBVTs7b0RBQXdCO29EQUFJZCxZQUFZYSxDQUFDLENBQUNRLE9BQU8sQ0FBQztvREFBRzs7Ozs7OzswREFDdEUsOERBQUNLO2dEQUNDQyxNQUFLO2dEQUNMbkIsS0FBSTtnREFDSkQsS0FBSTtnREFDSnFCLE1BQUs7Z0RBQ0xDLE9BQU83QixZQUFZYSxDQUFDO2dEQUNwQmlCLFVBQVUsQ0FBQ0MsSUFBTXBCLHFCQUFxQlgsWUFBWVksQ0FBQyxFQUFFc0IsU0FBU0gsRUFBRUUsTUFBTSxDQUFDSixLQUFLO2dEQUM1RWYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9sQiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDcEIsOENBQU1BO2dDQUNMNEIsTUFBSztnQ0FDTEMsU0FBUTtnQ0FDUkMsU0FBUyxJQUFNdEIsU0FBUzt3Q0FBRUcsT0FBTzt3Q0FBS0ssVUFBVTt3Q0FBR0UsR0FBRzt3Q0FBSUMsR0FBRztvQ0FBRzswQ0FDakU7Ozs7OzswQ0FHRCw4REFBQ25CLDhDQUFNQTtnQ0FDTDRCLE1BQUs7Z0NBQ0xDLFNBQVE7Z0NBQ1JDLFNBQVNyQjswQ0FDVjs7Ozs7Ozs7Ozs7O2tDQU1ILDhEQUFDWTt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNwQiw4Q0FBTUE7Z0NBQ0w0QixNQUFLO2dDQUNMQyxTQUFRO2dDQUNSQyxTQUFTO29DQUNQLCtCQUErQjtvQ0FDL0IsTUFBTVcsY0FBY2xDLGNBQWNtQyxLQUFLLEdBQUduQyxjQUFjb0MsTUFBTTtvQ0FDOURuQyxTQUFTO3dDQUFFRyxPQUFPOEIsY0FBYyxJQUFJLE1BQU1BO3dDQUFhdkIsR0FBRzt3Q0FBSUMsR0FBRztvQ0FBRztnQ0FDdEU7MENBQ0Q7Ozs7OzswQ0FHRCw4REFBQ25CLDhDQUFNQTtnQ0FDTDRCLE1BQUs7Z0NBQ0xDLFNBQVE7Z0NBQ1JDLFNBQVM7b0NBQ1AsZ0NBQWdDO29DQUNoQyxNQUFNVyxjQUFjbEMsY0FBY21DLEtBQUssR0FBR25DLGNBQWNvQyxNQUFNO29DQUM5RG5DLFNBQVM7d0NBQUVHLE9BQU84QixjQUFjLElBQUksTUFBTSxJQUFFQTt3Q0FBYXZCLEdBQUc7d0NBQUlDLEdBQUc7b0NBQUc7Z0NBQ3hFOzBDQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPWDtLQXpMTWQ7QUEyTE4sK0RBQWVBLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvUGhvdG9Db250cm9scy50c3g/ODYzZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBQbGFjZWRQaG90bywgVXBsb2FkZWRQaG90byB9IGZyb20gJ0AvdHlwZXMvdGVtcGxhdGUnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnLi91aS9idXR0b24nO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJy4vdWkvY2FyZCc7XG5cbmludGVyZmFjZSBQaG90b0NvbnRyb2xzUHJvcHMge1xuICBwbGFjZWRQaG90bzogUGxhY2VkUGhvdG87XG4gIHVwbG9hZGVkUGhvdG86IFVwbG9hZGVkUGhvdG87XG4gIG9uVXBkYXRlOiAodXBkYXRlczogUGFydGlhbDxQbGFjZWRQaG90bz4pID0+IHZvaWQ7XG4gIG9uUmVtb3ZlOiAoKSA9PiB2b2lkO1xufVxuXG5jb25zdCBQaG90b0NvbnRyb2xzOiBSZWFjdC5GQzxQaG90b0NvbnRyb2xzUHJvcHM+ID0gKHtcbiAgcGxhY2VkUGhvdG8sXG4gIHVwbG9hZGVkUGhvdG8sXG4gIG9uVXBkYXRlLFxuICBvblJlbW92ZVxufSkgPT4ge1xuICBjb25zdCBoYW5kbGVTY2FsZUNoYW5nZSA9IChzY2FsZTogbnVtYmVyKSA9PiB7XG4gICAgb25VcGRhdGUoeyBzY2FsZTogTWF0aC5tYXgoMC4xLCBNYXRoLm1pbigzLjAsIHNjYWxlKSkgfSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUm90YXRpb25DaGFuZ2UgPSAocm90YXRpb246IG51bWJlcikgPT4ge1xuICAgIG9uVXBkYXRlKHsgcm90YXRpb246IHJvdGF0aW9uICUgMzYwIH0pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBvc2l0aW9uQ2hhbmdlID0gKHg6IG51bWJlciwgeTogbnVtYmVyKSA9PiB7XG4gICAgb25VcGRhdGUoeyBcbiAgICAgIHg6IE1hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgeCkpLFxuICAgICAgeTogTWF0aC5tYXgoMCwgTWF0aC5taW4oMTAwLCB5KSlcbiAgICB9KTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxDYXJkIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlBob3RvIENvbnRyb2xzPC9DYXJkVGl0bGU+XG4gICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgIHsvKiBQaG90byBQcmV2aWV3ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFzcGVjdC1zcXVhcmUgdy0yMCBteC1hdXRvIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgPGltZ1xuICAgICAgICAgICAgc3JjPXt1cGxvYWRlZFBob3RvLnVybH1cbiAgICAgICAgICAgIGFsdD1cIlBob3RvIHByZXZpZXdcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXJcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBTY2FsZSBDb250cm9sICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmxvY2sgbWItMVwiPlxuICAgICAgICAgICAgU2NhbGU6IHsocGxhY2VkUGhvdG8uc2NhbGUgKiAxMDApLnRvRml4ZWQoMCl9JVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNjYWxlQ2hhbmdlKHBsYWNlZFBob3RvLnNjYWxlIC0gMC4xKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3BsYWNlZFBob3RvLnNjYWxlIDw9IDAuMX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidG91Y2gtbWFuaXB1bGF0aW9uXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgLVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgbWluPVwiMC4xXCJcbiAgICAgICAgICAgICAgbWF4PVwiMy4wXCJcbiAgICAgICAgICAgICAgc3RlcD1cIjAuMVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtwbGFjZWRQaG90by5zY2FsZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTY2FsZUNoYW5nZShwYXJzZUZsb2F0KGUudGFyZ2V0LnZhbHVlKSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSB0b3VjaC1tYW5pcHVsYXRpb25cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTY2FsZUNoYW5nZShwbGFjZWRQaG90by5zY2FsZSArIDAuMSl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtwbGFjZWRQaG90by5zY2FsZSA+PSAzLjB9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRvdWNoLW1hbmlwdWxhdGlvblwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgICtcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUm90YXRpb24gQ29udHJvbCAqL31cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJsb2NrIG1iLTFcIj5cbiAgICAgICAgICAgIFJvdGF0aW9uOiB7cGxhY2VkUGhvdG8ucm90YXRpb259wrBcbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSb3RhdGlvbkNoYW5nZShwbGFjZWRQaG90by5yb3RhdGlvbiAtIDE1KX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidG91Y2gtbWFuaXB1bGF0aW9uXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4oa6XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwicmFuZ2VcIlxuICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgbWF4PVwiMzYwXCJcbiAgICAgICAgICAgICAgc3RlcD1cIjE1XCJcbiAgICAgICAgICAgICAgdmFsdWU9e3BsYWNlZFBob3RvLnJvdGF0aW9ufVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVJvdGF0aW9uQ2hhbmdlKHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSB0b3VjaC1tYW5pcHVsYXRpb25cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVSb3RhdGlvbkNoYW5nZShwbGFjZWRQaG90by5yb3RhdGlvbiArIDE1KX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidG91Y2gtbWFuaXB1bGF0aW9uXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4oa7XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBvc2l0aW9uIENvbnRyb2xzICovfVxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmxvY2sgbWItMVwiPlxuICAgICAgICAgICAgUG9zaXRpb25cbiAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlg6IHtwbGFjZWRQaG90by54LnRvRml4ZWQoMCl9JTwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgICAgbWF4PVwiMTAwXCJcbiAgICAgICAgICAgICAgICBzdGVwPVwiMVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3BsYWNlZFBob3RvLnh9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVQb3NpdGlvbkNoYW5nZShwYXJzZUludChlLnRhcmdldC52YWx1ZSksIHBsYWNlZFBob3RvLnkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCB0b3VjaC1tYW5pcHVsYXRpb25cIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+WToge3BsYWNlZFBob3RvLnkudG9GaXhlZCgwKX0lPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInJhbmdlXCJcbiAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICAgIHN0ZXA9XCIxXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17cGxhY2VkUGhvdG8ueX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVBvc2l0aW9uQ2hhbmdlKHBsYWNlZFBob3RvLngsIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRvdWNoLW1hbmlwdWxhdGlvblwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFF1aWNrIEFjdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvblVwZGF0ZSh7IHNjYWxlOiAxLjAsIHJvdGF0aW9uOiAwLCB4OiA1MCwgeTogNTAgfSl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgUmVzZXRcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgdmFyaWFudD1cImRlc3RydWN0aXZlXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e29uUmVtb3ZlfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIFJlbW92ZVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRml0IE9wdGlvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtMlwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBzY2FsZSB0byBmaXQgd2lkdGhcbiAgICAgICAgICAgICAgY29uc3QgYXNwZWN0UmF0aW8gPSB1cGxvYWRlZFBob3RvLndpZHRoIC8gdXBsb2FkZWRQaG90by5oZWlnaHQ7XG4gICAgICAgICAgICAgIG9uVXBkYXRlKHsgc2NhbGU6IGFzcGVjdFJhdGlvID4gMSA/IDEuMCA6IGFzcGVjdFJhdGlvLCB4OiA1MCwgeTogNTAgfSk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIEZpdCBXaWR0aFxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIC8vIENhbGN1bGF0ZSBzY2FsZSB0byBmaXQgaGVpZ2h0XG4gICAgICAgICAgICAgIGNvbnN0IGFzcGVjdFJhdGlvID0gdXBsb2FkZWRQaG90by53aWR0aCAvIHVwbG9hZGVkUGhvdG8uaGVpZ2h0O1xuICAgICAgICAgICAgICBvblVwZGF0ZSh7IHNjYWxlOiBhc3BlY3RSYXRpbyA8IDEgPyAxLjAgOiAxL2FzcGVjdFJhdGlvLCB4OiA1MCwgeTogNTAgfSk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIEZpdCBIZWlnaHRcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NhcmRDb250ZW50PlxuICAgIDwvQ2FyZD5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFBob3RvQ29udHJvbHM7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJCdXR0b24iLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiUGhvdG9Db250cm9scyIsInBsYWNlZFBob3RvIiwidXBsb2FkZWRQaG90byIsIm9uVXBkYXRlIiwib25SZW1vdmUiLCJoYW5kbGVTY2FsZUNoYW5nZSIsInNjYWxlIiwiTWF0aCIsIm1heCIsIm1pbiIsImhhbmRsZVJvdGF0aW9uQ2hhbmdlIiwicm90YXRpb24iLCJoYW5kbGVQb3NpdGlvbkNoYW5nZSIsIngiLCJ5IiwiY2xhc3NOYW1lIiwiZGl2IiwiaW1nIiwic3JjIiwidXJsIiwiYWx0IiwibGFiZWwiLCJ0b0ZpeGVkIiwic2l6ZSIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJpbnB1dCIsInR5cGUiLCJzdGVwIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJwYXJzZUZsb2F0IiwidGFyZ2V0IiwicGFyc2VJbnQiLCJhc3BlY3RSYXRpbyIsIndpZHRoIiwiaGVpZ2h0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PhotoControls.tsx\n"));

/***/ })

});