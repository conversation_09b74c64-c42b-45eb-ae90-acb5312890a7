"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedPhoto, setSelectedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isHovered ? \"solid\" : \"dashed\", \" \").concat(isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>removePhotoFromSlot(uploadedPhoto.id),\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 124,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 142,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Drag photos from the sidebar into the slots below\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm\",\n                                        style: {\n                                            width: \"\".concat(canvasWidth, \"px\"),\n                                            height: \"\".concat(canvasHeight, \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 text-xs font-medium\",\n                                                        children: \"Drag to slot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"VMujsgWc4pV0IVWP8wKp0d6KjJQ=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});