'use client';

import React, { useRef, useEffect, useState } from 'react';
import { CollageTemplate, UploadedPhoto, PlacedPhoto, PhotoSlot } from '@/types/template';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface CollageRendererProps {
  template: CollageTemplate;
  uploadedPhotos: UploadedPhoto[];
  placedPhotos: PlacedPhoto[];
  onDownload?: (dataUrl: string) => void;
}

const CollageRenderer: React.FC<CollageRendererProps> = ({
  template,
  uploadedPhotos,
  placedPhotos,
  onDownload
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isRendering, setIsRendering] = useState(false);
  const [previewDataUrl, setPreviewDataUrl] = useState<string | null>(null);
  const [renderError, setRenderError] = useState<string | null>(null);

  const getUploadedPhoto = (photoId: string): UploadedPhoto | undefined => {
    return uploadedPhotos.find(p => p.id === photoId);
  };

  const getPlacedPhoto = (slotId: string): PlacedPhoto | undefined => {
    return placedPhotos.find(p => p.slotId === slotId);
  };

  const loadImage = (src: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  };

  const renderSlotToCanvas = async (
    ctx: CanvasRenderingContext2D,
    slot: PhotoSlot,
    placedPhoto: PlacedPhoto,
    uploadedPhoto: UploadedPhoto
  ) => {
    try {
      const img = await loadImage(uploadedPhoto.url);
      
      // Calculate slot dimensions in pixels
      const slotX = (slot.x / 100) * template.canvasWidth;
      const slotY = (slot.y / 100) * template.canvasHeight;
      const slotWidth = (slot.width / 100) * template.canvasWidth;
      const slotHeight = (slot.height / 100) * template.canvasHeight;

      // Save canvas state
      ctx.save();

      // Create clipping path for the slot shape
      ctx.beginPath();
      if (slot.shape === 'circle') {
        const centerX = slotX + slotWidth / 2;
        const centerY = slotY + slotHeight / 2;
        const radius = Math.min(slotWidth, slotHeight) / 2;
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      } else {
        // Rectangle or custom shape
        ctx.rect(slotX, slotY, slotWidth, slotHeight);
      }
      ctx.clip();

      // Apply slot rotation if any
      if (slot.rotation) {
        const centerX = slotX + slotWidth / 2;
        const centerY = slotY + slotHeight / 2;
        ctx.translate(centerX, centerY);
        ctx.rotate((slot.rotation * Math.PI) / 180);
        ctx.translate(-centerX, -centerY);
      }

      // Calculate image positioning and scaling
      const scale = placedPhoto.scale || 1;
      const rotation = placedPhoto.rotation || 0;
      const posX = (placedPhoto.x || 50) / 100;
      const posY = (placedPhoto.y || 50) / 100;

      // Calculate scaled image dimensions
      const scaledWidth = slotWidth * scale;
      const scaledHeight = slotHeight * scale;

      // Calculate position offset based on positioning
      const offsetX = slotX + (slotWidth - scaledWidth) * posX;
      const offsetY = slotY + (slotHeight - scaledHeight) * posY;

      // Apply image rotation
      if (rotation !== 0) {
        const centerX = offsetX + scaledWidth / 2;
        const centerY = offsetY + scaledHeight / 2;
        ctx.translate(centerX, centerY);
        ctx.rotate((rotation * Math.PI) / 180);
        ctx.translate(-centerX, -centerY);
      }

      // Draw the image
      ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);

      // Restore canvas state
      ctx.restore();
    } catch (error) {
      console.error('Error rendering slot:', error);
    }
  };

  const renderCollage = async (): Promise<string | null> => {
    const canvas = canvasRef.current;
    if (!canvas) return null;

    setIsRendering(true);
    setRenderError(null);

    try {
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Could not get canvas context');
      }

      // Set canvas dimensions
      canvas.width = template.canvasWidth;
      canvas.height = template.canvasHeight;

      // Clear canvas and set background
      ctx.fillStyle = template.backgroundColor || '#ffffff';
      ctx.fillRect(0, 0, template.canvasWidth, template.canvasHeight);

      // Render each slot with its photo
      for (const slot of template.slots) {
        const placedPhoto = getPlacedPhoto(slot.id);
        if (placedPhoto) {
          const uploadedPhoto = getUploadedPhoto(placedPhoto.photoId);
          if (uploadedPhoto) {
            await renderSlotToCanvas(ctx, slot, placedPhoto, uploadedPhoto);
          }
        }
      }

      // Get the data URL
      const dataUrl = canvas.toDataURL('image/png', 1.0);
      setPreviewDataUrl(dataUrl);
      return dataUrl;
    } catch (error) {
      console.error('Error rendering collage:', error);
      setRenderError(error instanceof Error ? error.message : 'Failed to render collage');
      return null;
    } finally {
      setIsRendering(false);
    }
  };

  const handleDownload = async () => {
    const dataUrl = await renderCollage();
    if (dataUrl) {
      // Create download link
      const link = document.createElement('a');
      link.download = `collage-${template.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.png`;
      link.href = dataUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      if (onDownload) {
        onDownload(dataUrl);
      }
    }
  };

  const handlePreview = async () => {
    await renderCollage();
  };

  useEffect(() => {
    // Auto-render preview when component mounts or data changes
    if (placedPhotos.length > 0) {
      handlePreview();
    }
  }, [template, placedPhotos, uploadedPhotos]);

  const filledSlots = placedPhotos.length;
  const totalSlots = template.slots.length;
  const isComplete = filledSlots === totalSlots;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Collage Preview</CardTitle>
        <div className="text-sm text-gray-600">
          {isComplete ? 'Your collage is ready!' : `${filledSlots} of ${totalSlots} slots filled`}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Preview Canvas */}
          <div className="flex justify-center">
            <div className="relative">
              <canvas
                ref={canvasRef}
                className="border rounded-lg shadow-sm max-w-full h-auto"
                style={{ maxHeight: '400px' }}
              />
              {isRendering && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                  <div className="text-white text-sm">Rendering...</div>
                </div>
              )}
            </div>
          </div>

          {/* Controls */}
          <div className="flex gap-3 justify-center">
            <Button
              variant="outline"
              onClick={handlePreview}
              disabled={isRendering || placedPhotos.length === 0}
            >
              {isRendering ? 'Rendering...' : 'Refresh Preview'}
            </Button>
            <Button
              onClick={handleDownload}
              disabled={isRendering || !isComplete}
            >
              Download Collage
            </Button>
          </div>

          {/* Status */}
          <div className="text-center text-sm text-gray-600">
            {renderError && (
              <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-sm">
                  <strong>Error:</strong> {renderError}
                </p>
              </div>
            )}
            {!isComplete && !renderError && (
              <p>Add photos to all slots to enable download</p>
            )}
            {isComplete && !renderError && (
              <p className="text-green-600">✓ Ready to download!</p>
            )}
          </div>

          {/* Technical Info */}
          <div className="text-xs text-gray-500 text-center space-y-1">
            <p>Canvas Size: {template.canvasWidth} × {template.canvasHeight}px</p>
            <p>Format: PNG • Quality: High</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CollageRenderer;
