"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageApp.tsx":
/*!***************************************!*\
  !*** ./src/components/CollageApp.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TemplateGallery__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TemplateGallery */ \"(app-pages-browser)/./src/components/TemplateGallery.tsx\");\n/* harmony import */ var _TemplatePreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TemplatePreview */ \"(app-pages-browser)/./src/components/TemplatePreview.tsx\");\n/* harmony import */ var _ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImageUpload */ \"(app-pages-browser)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _CollageEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CollageEditor */ \"(app-pages-browser)/./src/components/CollageEditor.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CollageApp = ()=>{\n    _s();\n    const [currentState, setCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"welcome\");\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleGetStarted = ()=>{\n        setCurrentState(\"template-selection\");\n    };\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n        setCurrentState(\"editing\");\n    };\n    const handleBackToTemplates = ()=>{\n        setCurrentState(\"template-selection\");\n        setSelectedTemplate(null);\n    };\n    const handleBackToWelcome = ()=>{\n        setCurrentState(\"welcome\");\n        setSelectedTemplate(null);\n        setUploadedPhotos([]);\n        setPlacedPhotos([]);\n    };\n    const handlePlacedPhotosChange = (newPlacedPhotos)=>{\n        setPlacedPhotos(newPlacedPhotos);\n    };\n    const handlePreview = ()=>{\n        setCurrentState(\"preview\");\n    };\n    const renderWelcomeScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Follow these simple steps to create your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCF7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"1. Choose Template\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Select from our collection of beautiful templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"\\uD83D\\uDCE4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"2. Upload Photos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Add your favorite photos to the collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-6 border rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: \"⬇️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold mb-2\",\n                                                    children: \"3. Download\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 text-sm\",\n                                                    children: \"Save your beautiful collage in high quality\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mt-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        size: \"lg\",\n                                        onClick: handleGetStarted,\n                                        children: \"Start Creating Your Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Multiple Templates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Choose from various template styles including grids, letters, numbers, hearts, and geometric patterns.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCF1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Mobile Friendly\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Create collages on any device with our responsive design that works perfectly on desktop, tablet, and mobile.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 50,\n            columnNumber: 5\n        }, undefined);\n    const renderTemplateSelection = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: \"Choose Your Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Select a template to start creating your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            onClick: handleBackToWelcome,\n                            children: \"← Back to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplateGallery__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    onTemplateSelect: handleTemplateSelect\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 131,\n            columnNumber: 5\n        }, undefined);\n    const renderEditingScreen = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-gray-800\",\n                                    children: [\n                                        \"Editing: \",\n                                        selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-2\",\n                                    children: \"Upload photos and arrange them in your collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToTemplates,\n                                    children: \"← Change Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleBackToWelcome,\n                                    children: \"← Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 7\n                }, undefined),\n                uploadedPhotos.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                children: [\n                                                    \"Add photos to use in your collage. You need \",\n                                                    selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length,\n                                                    \" photos for this template.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            onPhotosUploaded: setUploadedPhotos,\n                                            maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Template Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                children: selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TemplatePreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    template: selectedTemplate,\n                                                    width: 280,\n                                                    showSlotNumbers: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Template:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Photo Slots:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"Canvas Size:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 22\n                                                            }, undefined),\n                                                            \" \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasWidth,\n                                                            \" \\xd7 \",\n                                                            selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.canvasHeight,\n                                                            \"px\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                            children: [\n                                                \"Add More Photos (\",\n                                                uploadedPhotos.length,\n                                                \" uploaded)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                            children: \"You can upload more photos or start arranging the ones you have.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onPhotosUploaded: setUploadedPhotos,\n                                        maxFiles: (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.slots.length) || 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CollageEditor__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            template: selectedTemplate,\n                            uploadedPhotos: uploadedPhotos,\n                            onPlacedPhotosChange: handlePlacedPhotosChange,\n                            onPreview: handlePreview\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 146,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl font-bold text-gray-800 mb-4\",\n                            children: \"Photo Collage Maker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Create beautiful photo collages with our easy-to-use templates. Upload your photos, choose a template, and download your masterpiece!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, undefined),\n                currentState === \"welcome\" && renderWelcomeScreen(),\n                currentState === \"template-selection\" && renderTemplateSelection(),\n                currentState === \"editing\" && renderEditingScreen()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n            lineNumber: 244,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageApp.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageApp, \"VRQBFVBNyGs2ie7xBu6SudtNgyg=\");\n_c = CollageApp;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageApp);\nvar _c;\n$RefreshReg$(_c, \"CollageApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageApp.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/CollageEditor.tsx":
/*!******************************************!*\
  !*** ./src/components/CollageEditor.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageEditor = (param)=>{\n    let { template, uploadedPhotos, onPlacedPhotosChange, onPreview } = param;\n    _s();\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedPhoto, setDraggedPhoto] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoveredSlot, setHoveredSlot] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (photo)=>{\n        setDraggedPhoto(photo);\n    };\n    const handleDragEnd = ()=>{\n        setDraggedPhoto(null);\n        setHoveredSlot(null);\n    };\n    const handleSlotDragOver = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(slotId);\n    };\n    const handleSlotDragLeave = ()=>{\n        setHoveredSlot(null);\n    };\n    const handleSlotDrop = (e, slotId)=>{\n        e.preventDefault();\n        setHoveredSlot(null);\n        if (!draggedPhoto) return;\n        // Remove photo from any existing slot\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== draggedPhoto.id);\n        // Add photo to new slot\n        const newPlacedPhoto = {\n            photoId: draggedPhoto.id,\n            slotId,\n            x: 50,\n            y: 50,\n            scale: 1.0,\n            rotation: 0\n        };\n        const finalPlacedPhotos = [\n            ...updatedPlacedPhotos,\n            newPlacedPhoto\n        ];\n        setPlacedPhotos(finalPlacedPhotos);\n        onPlacedPhotosChange(finalPlacedPhotos);\n    };\n    const removePhotoFromSlot = (photoId)=>{\n        const updatedPlacedPhotos = placedPhotos.filter((p)=>p.photoId !== photoId);\n        setPlacedPhotos(updatedPlacedPhotos);\n        onPlacedPhotosChange(updatedPlacedPhotos);\n    };\n    const getPhotoInSlot = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const renderSlot = (slot)=>{\n        const placedPhoto = getPhotoInSlot(slot.id);\n        const uploadedPhoto = placedPhoto ? getUploadedPhoto(placedPhoto.photoId) : null;\n        const isHovered = hoveredSlot === slot.id;\n        const slotStyle = {\n            position: \"absolute\",\n            left: \"\".concat(slot.x, \"%\"),\n            top: \"\".concat(slot.y, \"%\"),\n            width: \"\".concat(slot.width, \"%\"),\n            height: \"\".concat(slot.height, \"%\"),\n            border: \"2px \".concat(isHovered ? \"solid\" : \"dashed\", \" \").concat(isHovered ? \"#3b82f6\" : \"#9ca3af\"),\n            backgroundColor: isHovered ? \"#dbeafe\" : uploadedPhoto ? \"transparent\" : \"#f3f4f6\",\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"center\",\n            cursor: \"pointer\",\n            transition: \"all 0.2s ease\",\n            overflow: \"hidden\"\n        };\n        if (slot.shape === \"circle\") {\n            slotStyle.borderRadius = \"50%\";\n        } else if (slot.shape === \"rectangle\") {\n            slotStyle.borderRadius = \"4px\";\n        }\n        if (slot.rotation) {\n            slotStyle.transform = \"rotate(\".concat(slot.rotation, \"deg)\");\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: slotStyle,\n            onDragOver: (e)=>handleSlotDragOver(e, slot.id),\n            onDragLeave: handleSlotDragLeave,\n            onDrop: (e)=>handleSlotDrop(e, slot.id),\n            children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full h-full group\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: uploadedPhoto.url,\n                        alt: \"Placed photo\",\n                        className: \"w-full h-full object-cover\",\n                        style: {\n                            transform: \"scale(\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.scale) || 1, \") rotate(\").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.rotation) || 0, \"deg)\"),\n                            objectPosition: \"\".concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.x) || 50, \"% \").concat((placedPhoto === null || placedPhoto === void 0 ? void 0 : placedPhoto.y) || 50, \"%\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>removePhotoFromSlot(uploadedPhoto.id),\n                        className: \"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 122,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl mb-1\",\n                        children: \"\\uD83D\\uDCF7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs\",\n                        children: \"Drop photo here\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 140,\n                columnNumber: 11\n            }, undefined)\n        }, slot.id, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, undefined);\n    };\n    const aspectRatio = template.canvasWidth / template.canvasHeight;\n    const canvasWidth = 600;\n    const canvasHeight = canvasWidth / aspectRatio;\n    const unplacedPhotos = uploadedPhotos.filter((photo)=>!placedPhotos.some((placed)=>placed.photoId === photo.id));\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid lg:grid-cols-3 gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Collage Canvas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Drag photos from the sidebar into the slots below\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        ref: canvasRef,\n                                        className: \"relative border rounded-lg shadow-sm\",\n                                        style: {\n                                            width: \"\".concat(canvasWidth, \"px\"),\n                                            height: \"\".concat(canvasHeight, \"px\"),\n                                            backgroundColor: template.backgroundColor || \"#ffffff\"\n                                        },\n                                        children: template.slots.map((slot)=>renderSlot(slot))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                filledSlots,\n                                                \" of \",\n                                                totalSlots,\n                                                \" slots filled\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        setPlacedPhotos([]);\n                                                        onPlacedPhotosChange([]);\n                                                    },\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Clear All\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    size: \"sm\",\n                                                    onClick: onPreview,\n                                                    disabled: placedPhotos.length === 0,\n                                                    children: \"Preview Collage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:col-span-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    children: \"Photo Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unplacedPhotos.length,\n                                        \" photos available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                unplacedPhotos.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-3\",\n                                    children: unplacedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group cursor-move\",\n                                            draggable: true,\n                                            onDragStart: ()=>handleDragStart(photo),\n                                            onDragEnd: handleDragEnd,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 border-transparent hover:border-blue-300 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: photo.url,\n                                                        alt: \"Available photo\",\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white opacity-0 group-hover:opacity-100 text-xs font-medium\",\n                                                        children: \"Drag to slot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, photo.id, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"All photos have been placed!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined),\n                                uploadedPhotos.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-8 text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl mb-2\",\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: \"Upload photos to get started\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageEditor.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageEditor, \"k6lOAEZwIbE/AfwE5r37IYl6JHU=\");\n_c = CollageEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageEditor);\nvar _c;\n$RefreshReg$(_c, \"CollageEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageEditor.tsx\n"));

/***/ })

});