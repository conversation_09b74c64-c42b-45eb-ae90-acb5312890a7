{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/Terminal/EditorLink.tsx"], "names": ["React", "useOpenInEditor", "EditorLink", "file", "isSourceFile", "location", "open", "lineNumber", "line", "column", "div", "data-with-open-in-editor-link", "data-with-open-in-editor-link-source-file", "undefined", "data-with-open-in-editor-link-import-trace", "tabIndex", "role", "onClick", "title", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,eAAe,QAAQ,mCAAkC;AAUlE,OAAO,SAASC,WAAW,KAAiD;IAAjD,IAAA,EAAEC,IAAI,EAAEC,YAAY,EAAEC,QAAQ,EAAmB,GAAjD;QAGXA,gBACJA;IAHV,MAAMC,OAAOL,gBAAgB;QAC3BE;QACAI,YAAYF,CAAAA,iBAAAA,4BAAAA,SAAUG,IAAI,YAAdH,iBAAkB;QAC9BI,QAAQJ,CAAAA,mBAAAA,4BAAAA,SAAUI,MAAM,YAAhBJ,mBAAoB;IAC9B;IAEA,qBACE,oBAACK;QACCC,iCAAAA;QACAC,6CACER,eAAe,OAAOS;QAExBC,8CACEV,eAAeS,YAAY;QAE7BE,UAAU;QACVC,MAAM;QACNC,SAASX;QACTY,OAAO;OAENf,MACAE,WAAW,AAAC,MAAGA,SAASG,IAAI,GAAC,MAAGH,SAASI,MAAM,GAAK,oBACrD,oBAACU;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,oBAACC;QAAKC,GAAE;sBACR,oBAACC;QAASC,QAAO;sBACjB,oBAACtB;QAAKuB,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;;AAIzC"}