{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/container/RuntimeError/FrameworkIcon.tsx"], "names": ["React", "FrameworkIcon", "framework", "svg", "data-nextjs-call-stack-framework-icon", "xmlns", "width", "height", "viewBox", "fill", "shapeRendering", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "path", "d", "mask", "id", "maskUnits", "x", "y", "circle", "cx", "cy", "r", "g", "rect", "defs", "linearGradient", "x1", "y1", "x2", "y2", "gradientUnits", "stop", "stopColor", "offset", "stopOpacity"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAGzB,OAAO,SAASC,cAAc,KAI7B;IAJ6B,IAAA,EAC5BC,SAAS,EAGV,GAJ6B;IAK5B,IAAIA,cAAc,SAAS;QACzB,qBACE,oBAACC;YACCC,yCAAsC;YACtCC,OAAM;YACNC,OAAM;YACNC,QAAO;YACPC,SAAQ;YACRC,MAAK;YACLC,gBAAe;YACfC,QAAO;YACPC,eAAc;YACdC,gBAAe;YACfC,aAAY;yBAEZ,oBAACC;YACCC,GAAE;YACFP,MAAK;0BAEP,oBAACM;YACCC,GAAE;YACFP,MAAK;;IAIb;IAEA,qBACE,oBAACN;QACCC,yCAAsC;QACtCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;qBAEL,oBAACQ;QACCC,IAAG;QACHC,WAAU;QACVC,GAAE;QACFC,GAAE;QACFf,OAAM;QACNC,QAAO;qBAEP,oBAACe;QAAOC,IAAG;QAAKC,IAAG;QAAKC,GAAE;QAAKhB,MAAK;uBAEtC,oBAACiB;QAAET,MAAK;qBACN,oBAACK;QACCC,IAAG;QACHC,IAAG;QACHC,GAAE;QACFhB,MAAK;QACLE,QAAO;QACPG,aAAY;sBAEd,oBAACC;QACCC,GAAE;QACFP,MAAK;sBAEP,oBAACkB;QACCP,GAAE;QACFC,GAAE;QACFf,OAAM;QACNC,QAAO;QACPE,MAAK;uBAGT,oBAACmB,4BACC,oBAACC;QACCX,IAAG;QACHY,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,eAAc;qBAEd,oBAACC;QAAKC,WAAU;sBAChB,oBAACD;QAAKE,QAAO;QAAID,WAAU;QAAQE,aAAY;uBAEjD,oBAACT;QACCX,IAAG;QACHY,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,IAAG;QACHC,eAAc;qBAEd,oBAACC;QAAKC,WAAU;sBAChB,oBAACD;QAAKE,QAAO;QAAID,WAAU;QAAQE,aAAY;;AAKzD"}