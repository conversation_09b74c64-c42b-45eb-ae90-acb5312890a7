{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/CodeFrame/CodeFrame.tsx"], "names": ["CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "React", "useMemo", "lines", "split", "prefixLength", "map", "line", "exec", "stripAnsi", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "p", "repeat", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "useOpenInEditor", "file", "lineNumber", "column", "div", "data-nextjs-codeframe", "role", "onClick", "tabIndex", "title", "span", "getFrameSource", "methodName", "svg", "xmlns", "viewBox", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "path", "d", "polyline", "points", "x1", "y1", "x2", "y2", "pre", "entry", "index", "key", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content"], "mappings": ";;;;+BASaA;;;eAAAA;;;;;gEATK;iEACK;oEAED;4BACS;iCACC;AAIzB,MAAMA,YAAsC,SAASA,UAAU,KAGrE;IAHqE,IAAA,EACpEC,UAAU,EACVC,SAAS,EACV,GAHqE;IAIpE,8CAA8C;IAC9C,MAAMC,iBAAiBC,OAAMC,OAAO,CAAS;QAC3C,MAAMC,QAAQJ,UAAUK,KAAK,CAAC;QAC9B,MAAMC,eAAeF,MAClBG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,QAExCG,MAAM,CAACC,SACPL,GAAG,CAAC,CAACM,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,IAAIhB,eAAe,GAAG;YACpB,MAAMiB,IAAI,IAAIC,MAAM,CAAClB;YACrB,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMiB,IACV,CAAEA,CAAAA,IAAIjB,KAAKkB,OAAO,CAAC,IAAG,IAClBlB,KAAKmB,SAAS,CAAC,GAAGF,KAAKjB,KAAKmB,SAAS,CAACF,GAAGG,OAAO,CAACL,GAAG,MACpDf,MAELqB,IAAI,CAAC;QACV;QACA,OAAOzB,MAAMyB,IAAI,CAAC;IACpB,GAAG;QAAC7B;KAAU;IAEd,MAAM8B,UAAU5B,OAAMC,OAAO,CAAC;QAC5B,OAAO4B,cAAK,CAACC,UAAU,CAAC/B,gBAAgB;YACtCgC,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAClC;KAAe;IAEnB,MAAMmC,OAAOC,IAAAA,gCAAe,EAAC;QAC3BC,MAAMvC,WAAWuC,IAAI;QACrBC,YAAYxC,WAAWwC,UAAU;QACjCC,QAAQzC,WAAWyC,MAAM;IAC3B;IAEA,gCAAgC;IAChC,qBACE,qBAACC;QAAIC,yBAAAA;qBACH,qBAACD,2BACC,qBAAClB;QACCoB,MAAK;QACLC,SAASR;QACTS,UAAU;QACVC,OAAM;qBAEN,qBAACC,cACEC,IAAAA,0BAAc,EAACjD,aAAY,OAAIA,WAAWkD,UAAU,iBAEvD,qBAACC;QACCC,OAAM;QACNC,SAAQ;QACRC,MAAK;QACLC,QAAO;QACPC,aAAY;QACZC,eAAc;QACdC,gBAAe;qBAEf,qBAACC;QAAKC,GAAE;sBACR,qBAACC;QAASC,QAAO;sBACjB,qBAACrD;QAAKsD,IAAG;QAAKC,IAAG;QAAKC,IAAG;QAAKC,IAAG;yBAIvC,qBAACC,aACEpC,QAAQvB,GAAG,CAAC,CAAC4D,OAAOC,sBACnB,qBAACrB;YACCsB,KAAK,AAAC,WAAQD;YACdE,OAAO;gBACLC,OAAOJ,MAAMK,EAAE,GAAG,AAAC,iBAAcL,MAAMK,EAAE,GAAC,MAAKC;gBAC/C,GAAIN,MAAMO,UAAU,KAAK,SACrB;oBAAEC,YAAY;gBAAI,IAClBR,MAAMO,UAAU,KAAK,WACrB;oBAAEE,WAAW;gBAAS,IACtBH,SAAS;YACf;WAECN,MAAMU,OAAO;AAM1B"}