"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CollageRenderer.tsx":
/*!********************************************!*\
  !*** ./src/components/CollageRenderer.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst CollageRenderer = (param)=>{\n    let { template, uploadedPhotos, placedPhotos, onDownload } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isRendering, setIsRendering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewDataUrl, setPreviewDataUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [renderError, setRenderError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const getUploadedPhoto = (photoId)=>{\n        return uploadedPhotos.find((p)=>p.id === photoId);\n    };\n    const getPlacedPhoto = (slotId)=>{\n        return placedPhotos.find((p)=>p.slotId === slotId);\n    };\n    const loadImage = (src)=>{\n        return new Promise((resolve, reject)=>{\n            const img = new Image();\n            img.onload = ()=>resolve(img);\n            img.onerror = reject;\n            img.src = src;\n        });\n    };\n    const renderSlotToCanvas = async (ctx, slot, placedPhoto, uploadedPhoto)=>{\n        try {\n            const img = await loadImage(uploadedPhoto.url);\n            // Calculate slot dimensions in pixels\n            const slotX = slot.x / 100 * template.canvasWidth;\n            const slotY = slot.y / 100 * template.canvasHeight;\n            const slotWidth = slot.width / 100 * template.canvasWidth;\n            const slotHeight = slot.height / 100 * template.canvasHeight;\n            // Save canvas state\n            ctx.save();\n            // Create clipping path for the slot shape\n            ctx.beginPath();\n            if (slot.shape === \"circle\") {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                const radius = Math.min(slotWidth, slotHeight) / 2;\n                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\n            } else {\n                // Rectangle or custom shape\n                ctx.rect(slotX, slotY, slotWidth, slotHeight);\n            }\n            ctx.clip();\n            // Apply slot rotation if any\n            if (slot.rotation) {\n                const centerX = slotX + slotWidth / 2;\n                const centerY = slotY + slotHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(slot.rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Calculate image positioning and scaling\n            const scale = placedPhoto.scale || 1;\n            const rotation = placedPhoto.rotation || 0;\n            const posX = (placedPhoto.x || 50) / 100;\n            const posY = (placedPhoto.y || 50) / 100;\n            // Calculate scaled image dimensions\n            const scaledWidth = slotWidth * scale;\n            const scaledHeight = slotHeight * scale;\n            // Calculate position offset based on positioning\n            const offsetX = slotX + (slotWidth - scaledWidth) * posX;\n            const offsetY = slotY + (slotHeight - scaledHeight) * posY;\n            // Apply image rotation\n            if (rotation !== 0) {\n                const centerX = offsetX + scaledWidth / 2;\n                const centerY = offsetY + scaledHeight / 2;\n                ctx.translate(centerX, centerY);\n                ctx.rotate(rotation * Math.PI / 180);\n                ctx.translate(-centerX, -centerY);\n            }\n            // Draw the image\n            ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);\n            // Restore canvas state\n            ctx.restore();\n        } catch (error) {\n            console.error(\"Error rendering slot:\", error);\n        }\n    };\n    const renderCollage = async ()=>{\n        const canvas = canvasRef.current;\n        if (!canvas) return null;\n        setIsRendering(true);\n        setRenderError(null);\n        try {\n            const ctx = canvas.getContext(\"2d\");\n            if (!ctx) {\n                throw new Error(\"Could not get canvas context\");\n            }\n            // Set canvas dimensions\n            canvas.width = template.canvasWidth;\n            canvas.height = template.canvasHeight;\n            // Clear canvas and set background\n            ctx.fillStyle = template.backgroundColor || \"#ffffff\";\n            ctx.fillRect(0, 0, template.canvasWidth, template.canvasHeight);\n            // Render each slot with its photo\n            for (const slot of template.slots){\n                const placedPhoto = getPlacedPhoto(slot.id);\n                if (placedPhoto) {\n                    const uploadedPhoto = getUploadedPhoto(placedPhoto.photoId);\n                    if (uploadedPhoto) {\n                        await renderSlotToCanvas(ctx, slot, placedPhoto, uploadedPhoto);\n                    }\n                }\n            }\n            // Get the data URL\n            const dataUrl = canvas.toDataURL(\"image/png\", 1.0);\n            setPreviewDataUrl(dataUrl);\n            return dataUrl;\n        } catch (error) {\n            console.error(\"Error rendering collage:\", error);\n            setRenderError(error instanceof Error ? error.message : \"Failed to render collage\");\n            return null;\n        } finally{\n            setIsRendering(false);\n        }\n    };\n    const handleDownload = async ()=>{\n        const dataUrl = await renderCollage();\n        if (dataUrl) {\n            // Create download link\n            const link = document.createElement(\"a\");\n            link.download = \"collage-\".concat(template.name.toLowerCase().replace(/\\s+/g, \"-\"), \"-\").concat(Date.now(), \".png\");\n            link.href = dataUrl;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            if (onDownload) {\n                onDownload(dataUrl);\n            }\n        }\n    };\n    const handlePreview = async ()=>{\n        await renderCollage();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Auto-render preview when component mounts or data changes\n        if (placedPhotos.length > 0) {\n            handlePreview();\n        }\n    }, [\n        template,\n        placedPhotos,\n        uploadedPhotos\n    ]);\n    const filledSlots = placedPhotos.length;\n    const totalSlots = template.slots.length;\n    const isComplete = filledSlots === totalSlots;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        children: \"Collage Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600\",\n                        children: isComplete ? \"Your collage is ready!\" : \"\".concat(filledSlots, \" of \").concat(totalSlots, \" slots filled\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                        ref: canvasRef,\n                                        className: \"border rounded-lg shadow-sm max-w-full h-auto\",\n                                        style: {\n                                            maxHeight: \"400px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isRendering && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-white text-sm\",\n                                            children: \"Rendering...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handlePreview,\n                                    disabled: isRendering || placedPhotos.length === 0,\n                                    children: isRendering ? \"Rendering...\" : \"Refresh Preview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDownload,\n                                    disabled: isRendering || !isComplete,\n                                    children: \"Download Collage\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-sm text-gray-600\",\n                            children: [\n                                !isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add photos to all slots to enable download\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined),\n                                isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600\",\n                                    children: \"✓ Ready to download!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Canvas Size: \",\n                                        template.canvasWidth,\n                                        \" \\xd7 \",\n                                        template.canvasHeight,\n                                        \"px\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Format: PNG • Quality: High\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\CollageRenderer.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CollageRenderer, \"YielKjIkWuN+XikvhPlPJlx0t9U=\");\n_c = CollageRenderer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CollageRenderer);\nvar _c;\n$RefreshReg$(_c, \"CollageRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CollageRenderer.tsx\n"));

/***/ })

});